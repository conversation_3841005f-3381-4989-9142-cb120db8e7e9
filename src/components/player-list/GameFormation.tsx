import { useMemo } from "react";

interface GameFormationProps {
  inCount: number;
  allow3Teams: boolean;
}

export function GameFormation({ inCount, allow3Teams }: GameFormationProps) {
  const text = useMemo(() => {
    if (inCount < 10) {
      return "Nicht genug Spieler";
    }
    if (inCount <= 14) {
      return "2 Teams";
    }
    if (inCount >= 15 && inCount <= 19) {
      return allow3Teams ? "3 Teams" : "2 Teams";
    }
    return "4 Teams";
  }, [inCount, allow3Teams]);

  return <div className="text-center min-w-[80px]">{text}</div>;
}
