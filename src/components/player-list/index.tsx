import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/custom-card";
import { Ta<PERSON>, TabsContent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Check,
  X,
  Users,
  AlertCircle,
  Shield,
  Search,
  Lock,
  CalendarPlus,
  ChevronLeft,
  ChevronRight,
  UserCheck,
  UserX,
  HelpCircle,
  User,
} from "lucide-react";
import { Player } from "@/types";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { usePlayers } from "@/hooks/usePlayers";
import { useGameSessions } from "@/hooks/useGameSessions";
import { useTeamSettings } from "@/hooks/useTeamSettings";
import { Input } from "@/components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { GameFormation } from "./GameFormation";
import { StatusChangeDialog } from "./StatusChangeDialog";
import { PlayerAvatar } from "./PlayerAvatar";
import { Separator } from "../ui/separator";
import { Badge } from "@/components/ui/badge";
import { PlayerName } from "@/components/player-profile/PlayerName";
import { notifyPlayerSignup, notifyPlayerSignoff } from "@/services/whatsappService";

export default function PlayerList() {
  const { players, updatePlayer } = usePlayers();
  const { currentSession } = useGameSessions();
  const { allow3Teams } = useTeamSettings();

  const [activeTab, setActiveTab] = useState("all");
  const [confirmedPlayers, setConfirmedPlayers] = useState<Player[]>([]);
  const [reservePlayers, setReservePlayers] = useState<Player[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const playersPerPage = 12; // Reduced for better visual density

  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    playerId: string;
    status: "in" | "out";
  } | null>(null);

  useEffect(() => {
    if (players && players.length > 0 && currentSession) {
      updatePlayerLists(players);
    } else {
      setConfirmedPlayers([]);
      setReservePlayers([]);
    }
  }, [players, currentSession, allow3Teams]);

  const updatePlayerLists = (currentPlayers: Player[]) => {
    const inPlayers = currentPlayers
      .filter((p) => p.status === "in")
      .sort((a, b) => {
        if (a.signupTimestamp && b.signupTimestamp) {
          return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
        }
        return 0;
      });

    const totalIn = inPlayers.length;
    let confirmed: Player[] = [];
    let reserves: Player[] = [];

    if (totalIn <= 9) {
      confirmed = [...inPlayers];
    } else if (totalIn === 10) {
      confirmed = [...inPlayers];
    } else if (totalIn >= 11 && totalIn <= 14) {
      confirmed = inPlayers.slice(0, 10);
      reserves = inPlayers.slice(10).map((player, index) => ({
        ...player,
        reserveNumber: index + 1,
      }));
    } else if (totalIn === 15) {
      if (allow3Teams) {
        confirmed = [...inPlayers];
      } else {
        confirmed = inPlayers.slice(0, 10);
        reserves = inPlayers.slice(10).map((player, index) => ({
          ...player,
          reserveNumber: index + 1,
        }));
      }
    } else if (totalIn >= 16 && totalIn <= 19) {
      if (allow3Teams) {
        confirmed = inPlayers.slice(0, 15);
        reserves = inPlayers.slice(15).map((player, index) => ({
          ...player,
          reserveNumber: index + 1,
        }));
      } else {
        confirmed = inPlayers.slice(0, 10);
        reserves = inPlayers.slice(10).map((player, index) => ({
          ...player,
          reserveNumber: index + 1,
        }));
      }
    } else if (totalIn >= 20) {
      confirmed = inPlayers.slice(0, 20);
      reserves = inPlayers.slice(20).map((player, index) => ({
        ...player,
        reserveNumber: index + 1,
      }));
    }

    setConfirmedPlayers(confirmed);
    setReservePlayers(reserves);
  };

  const triggerStatusChange = (playerId: string, status: "in" | "out") => {
    setPendingStatusChange({ playerId, status });
    setDialogOpen(true);
  };

  const confirmStatusChange = async () => {
    if (!pendingStatusChange) return;
    const { playerId, status } = pendingStatusChange;
    setDialogOpen(false);
    setPendingStatusChange(null);

    if (!currentSession || currentSession.status !== "scheduled") {
      toast({
        title: "Kein aktives Spiel",
        description: "Es gibt derzeit kein aktives Spiel für Anmeldungen.",
        variant: "destructive",
      });
      return;
    }

    const isSignupOpen = currentSession?.isSignupOpen || false;

    if (!isSignupOpen) {
      toast({
        title: "Anmeldung nicht geöffnet",
        description: "Der Admin hat die Anmeldung für dieses Spiel geschlossen",
        variant: "destructive",
      });
      return;
    }

    const result = await updatePlayer(playerId, {
      status,
      signupTimestamp: status === "in" ? new Date() : undefined,
    });

    if (result) {
      toast({
        title: "Status aktualisiert",
        description: `Sie sind jetzt als "${status === "in" ? "Dabei" : "Nicht dabei"
          }" für das nächste Spiel markiert.`,
        variant: status === "in" ? "default" : "destructive",
      });

      // Send WhatsApp notification
      try {
        const player = players.find((p) => p.id === playerId);
        if (player) {
          if (status === "in") {
            await notifyPlayerSignup(player);
          } else {
            await notifyPlayerSignoff(player);
          }
        }
      } catch (error) {
        console.error("Failed to send WhatsApp notification:", error);
        // Don't show error to user, as it's not critical functionality
      }
    } else {
      toast({
        title: "Aktualisierung fehlgeschlagen",
        description: "Es gab ein Problem bei der Aktualisierung Ihres Status.",
        variant: "destructive",
      });
    }
  };

  const filteredPlayers = players
    .filter((player) => player.is_active)
    .filter((player) => {
      const matchesSearch =
        searchQuery === "" || player.name.toLowerCase().includes(searchQuery.toLowerCase());

      if (!matchesSearch) return false;

      if (activeTab === "all") return true;
      if (activeTab === "confirmed") return confirmedPlayers.some((p) => p.id === player.id);
      if (activeTab === "reserves") return reservePlayers.some((p) => p.id === player.id);
      return true;
    })
    .sort((a, b) => {
      if (activeTab === "confirmed" && a.signupTimestamp && b.signupTimestamp) {
        return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
      }
      return 0;
    });

  const indexOfLastPlayer = currentPage * playersPerPage;
  const indexOfFirstPlayer = indexOfLastPlayer - playersPerPage;
  const currentPlayers = filteredPlayers.slice(indexOfFirstPlayer, indexOfLastPlayer);
  const totalPages = Math.ceil(filteredPlayers.length / playersPerPage);

  const inCount = players.filter((p) => p.status === "in").length;
  const isSignupOpen = currentSession?.isSignupOpen || false;

  if (!currentSession || currentSession.status !== "scheduled") {
    return (
      <Card className="bg-white dark:bg-zinc-900 border-0 shadow-md">
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-blue-50 dark:bg-blue-900/20 p-4">
              <CalendarPlus className="h-8 w-8 text-blue-500 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Kein aktives Spiel
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md">
              Es ist derzeit kein aktives Spiel geplant. Sobald ein neues Spiel erstellt wird,
              können Sie sich hier anmelden.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <CardTitle icon={<Users className="w-5 h-5" />} className="dark:text-white">
                Spielerliste
              </CardTitle>
            </div>
            <CardDescription className="dark:text-zinc-400">
              Übersicht aller Spieler und deren Anmeldestatus
            </CardDescription>
          </div>
          <Badge
            variant="outline"
            className="px-3 py-1 bg-gray-50 dark:bg-zinc-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-zinc-700"
          >
            <GameFormation inCount={inCount} allow3Teams={allow3Teams} />
          </Badge>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>

      <CardContent className="pt-3 space-y-4">
        {/* Spielerstatus Progress Bar */}
        <div className="bg-gray-50 dark:bg-zinc-800/50 rounded-lg p-4 border border-gray-100 dark:border-zinc-700/50">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-1 h-4 bg-blue-500 rounded-full"></div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Spielerstatus</h3>
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
              {players.filter((p) => p.is_active).length} Spieler gesamt
            </span>
          </div>

          {(() => {
            const confirmedCount = players.filter((p) => p.is_active && p.status === "in").length;
            const declinedCount = players.filter((p) => p.is_active && p.status === "out").length;
            const pendingCount = players.filter((p) => p.is_active && p.status === "pending").length;
            const totalCount = confirmedCount + declinedCount + pendingCount;

            const confirmedPercent = totalCount > 0 ? (confirmedCount / totalCount) * 100 : 0;
            const declinedPercent = totalCount > 0 ? (declinedCount / totalCount) * 100 : 0;
            const pendingPercent = totalCount > 0 ? (pendingCount / totalCount) * 100 : 0;

            return (
              <>
                {/* Progress Bar */}
                <div className="relative h-3 bg-gray-200 dark:bg-zinc-700 rounded-full overflow-hidden mb-3">
                  <div className="absolute inset-0 flex">
                    {/* Angemeldet Segment */}
                    {confirmedPercent > 0 && (
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-full transition-all duration-500"
                        style={{ width: `${confirmedPercent}%` }}
                      />
                    )}
                    {/* Abgemeldet Segment */}
                    {declinedPercent > 0 && (
                      <div
                        className="bg-gradient-to-r from-red-500 to-red-600 h-full transition-all duration-500"
                        style={{ width: `${declinedPercent}%` }}
                      />
                    )}
                    {/* Ausstehend Segment */}
                    {pendingPercent > 0 && (
                      <div
                        className="bg-gradient-to-r from-gray-500 to-gray-600 h-full transition-all duration-500"
                        style={{ width: `${pendingPercent}%` }}
                      />
                    )}
                  </div>
                </div>

                {/* Legende - Mobile responsive */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Angemeldet: <span className="font-medium text-gray-900 dark:text-gray-100">{confirmedCount}</span>
                    </span>
                  </div>

                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Abgemeldet: <span className="font-medium text-gray-900 dark:text-gray-100">{declinedCount}</span>
                    </span>
                  </div>

                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Ausstehend: <span className="font-medium text-gray-900 dark:text-gray-100">{pendingCount}</span>
                    </span>
                  </div>
                </div>
              </>
            );
          })()}
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Spieler suchen..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
          </div>

          <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => {
            setActiveTab(value);
            setCurrentPage(1); // Reset pagination when switching tabs
          }}>
            <TabsList className="grid grid-cols-3 h-9 items-center justify-center rounded-lg bg-gray-100 dark:bg-zinc-800 p-1 text-gray-700 dark:text-gray-300 mb-6 w-full">
              <TabsTrigger
                value="all"
                className={cn(
                  "w-full inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-700 data-[state=active]:text-gray-900 dark:data-[state=active]:text-gray-100 data-[state=active]:shadow-sm",
                  "gap-2"
                )}
              >
                <Users className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                <span>Alle ({players.filter((p) => p.is_active).length})</span>
              </TabsTrigger>
              <TabsTrigger
                value="confirmed"
                className={cn(
                  "w-full inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-700 data-[state=active]:text-gray-900 dark:data-[state=active]:text-gray-100 data-[state=active]:shadow-sm",
                  "gap-2"
                )}
              >
                <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span>Bestätigt ({confirmedPlayers.length})</span>
              </TabsTrigger>
              <TabsTrigger
                value="reserves"
                className={cn(
                  "w-full inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-700 data-[state=active]:text-gray-900 dark:data-[state=active]:text-gray-100 data-[state=active]:shadow-sm",
                  "gap-2"
                )}
              >
                <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                <span>Reserve ({reservePlayers.length})</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-0 space-y-6">
              {activeTab === "confirmed" && confirmedPlayers.length > 0 && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-900/30 rounded-lg flex items-center gap-2">
                  <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm text-blue-800 dark:text-blue-300">
                    Diese Spieler sind für das nächste Spiel bestätigt
                  </span>
                </div>
              )}

              {activeTab === "reserves" && reservePlayers.length > 0 && (
                <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-900/30 rounded-lg flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                  <span className="text-sm text-amber-800 dark:text-amber-300">
                    Diese Spieler stehen auf der Reserveliste
                  </span>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {currentPlayers.length === 0 ? (
                  <div className="col-span-full py-8 text-center text-gray-500 dark:text-gray-400">
                    Keine Spieler gefunden
                  </div>
                ) : (
                  currentPlayers.map((player) => {
                    const isConfirmed = confirmedPlayers.some((p) => p.id === player.id);
                    const isReserve = reservePlayers.some((p) => p.id === player.id);

                    return (
                      <div
                        key={player.id}
                        className={cn(
                          "group relative flex flex-row items-center gap-3 rounded-lg border bg-white dark:bg-zinc-900 p-3 transition-all hover:shadow-md",
                          player.status === "in"
                            ? "border-blue-200 dark:border-blue-800"
                            : "border-gray-200 dark:border-zinc-800",
                          isReserve && "border-amber-200 dark:border-amber-900"
                        )}
                      >
                        <div
                          className={cn(
                            "flex-shrink-0",
                            player.status === "in" &&
                            !isReserve &&
                            "ring-2 ring-blue-500 dark:ring-blue-400 rounded-full",
                            isReserve && "ring-2 ring-amber-500 dark:ring-amber-400 rounded-full"
                          )}
                        >
                          <PlayerAvatar
                            jerseyNumber={player.jerseyNumber}
                            status={player.status}
                            isReserve={isReserve}
                          />
                        </div>

                        <div className="flex-grow min-w-0">
                          <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
                            <PlayerName player={player} playerName={player.name} />
                          </div>
                          {isReserve && (
                            <div className="text-xs text-amber-600 dark:text-amber-400 font-medium mt-0.5">
                              Reserve #
                              {reservePlayers.find((p) => p.id === player.id)?.reserveNumber}
                            </div>
                          )}
                        </div>

                        {!isSignupOpen ? (
                          <div className="flex-shrink-0 text-gray-400 dark:text-gray-500">
                            <Lock className="h-4 w-4" />
                          </div>
                        ) : (
                          <div className="flex-shrink-0 flex items-center gap-1">
                            <button
                              className={cn(
                                "rounded-full p-1.5 transition-colors",
                                player.status === "in"
                                  ? "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                                  : "hover:bg-blue-50 dark:hover:bg-blue-900/30 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400"
                              )}
                              onClick={() => triggerStatusChange(player.id!, "in")}
                            >
                              <Check className="h-4 w-4" />
                            </button>
                            <button
                              className={cn(
                                "rounded-full p-1.5 transition-colors",
                                player.status === "out"
                                  ? "bg-rose-50 dark:bg-rose-900/30 text-rose-600 dark:text-rose-400"
                                  : "hover:bg-rose-50 dark:hover:bg-rose-900/30 text-gray-400 dark:text-gray-500 hover:text-rose-600 dark:hover:text-rose-400"
                              )}
                              onClick={() => triggerStatusChange(player.id!, "out")}
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        )}
                      </div>
                    );
                  })
                )}
              </div>

              {totalPages > 1 && (
                <div className="flex justify-center pt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                          className={cn(
                            "border-gray-200 dark:border-zinc-800",
                            currentPage === 1 && "pointer-events-none opacity-50"
                          )}
                        />
                      </PaginationItem>
                      {Array.from({ length: totalPages }).map((_, i) => (
                        <PaginationItem key={i + 1}>
                          <PaginationLink
                            onClick={() => setCurrentPage(i + 1)}
                            isActive={currentPage === i + 1}
                            className="border-gray-200 dark:border-zinc-800"
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                          className={cn(
                            "border-gray-200 dark:border-zinc-800",
                            currentPage === totalPages && "pointer-events-none opacity-50"
                          )}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>

      <StatusChangeDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        status={pendingStatusChange?.status}
        onConfirm={confirmStatusChange}
      />
    </Card>
  );
}
