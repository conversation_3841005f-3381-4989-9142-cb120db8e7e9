import { cn } from "@/lib/utils";
import { User } from "lucide-react";

interface PlayerAvatarProps {
  jerseyNumber?: number;
  status: string;
  isReserve: boolean;
}

export function PlayerAvatar({ jerseyNumber, status, isReserve }: PlayerAvatarProps) {
  const getStatusColor = () => {
    if (status === "in" && !isReserve) return "bg-blue-500 dark:bg-blue-600";
    if (status === "in" && isReserve) return "bg-amber-500 dark:bg-amber-600";
    if (status === "out") return "bg-rose-500 dark:bg-rose-600";
    return "bg-gray-400 dark:bg-gray-600";
  };

  return (
    <div
      className={cn(
        "relative w-9 h-9 rounded-full flex items-center justify-center text-white font-medium text-sm transition-colors",
        getStatusColor()
      )}
    >
      {jerseyNumber ? (
        <span className="text-sm">{jerseyNumber}</span>
      ) : (
        <User className="w-4 w-4" />
      )}
    </div>
  );
}
