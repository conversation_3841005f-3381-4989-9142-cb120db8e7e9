import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface StatusChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  status: "in" | "out" | undefined;
  onConfirm: () => void;
}

export function StatusChangeDialog({
  open,
  onOpenChange,
  status,
  onConfirm,
}: StatusChangeDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {status === "in" ? "Teilnahme bestätigen?" : "Absage bestätigen?"}
          </DialogTitle>
          <DialogDescription>
            {status === "in"
              ? "Möchtest du dich wirklich für dieses Spiel anmelden?"
              : "Möchtest du dich wirklich abmelden und absagen?"}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Abbrechen
          </Button>
          <Button
            className={
              status === "out" ? "bg-red-600 hover:bg-red-700" : "bg-green-600 hover:bg-green-700"
            }
            onClick={onConfirm}
          >
            {status === "in" ? "Ja, anmelden" : "Ja, absagen"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
