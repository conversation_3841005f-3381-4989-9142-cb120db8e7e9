import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import { submitFeedback, getRecentSessions } from "@/services/feedbackService";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

type FeedbackMode = "game" | "general";

const MAX_GENERAL_FEEDBACK_LENGTH = 500;
const MAX_POSITIVES_LENGTH = 300;
const MAX_IMPROVEMENTS_LENGTH = 300;
const MAX_NOTES_LENGTH = 200;
const MAX_NAME_LENGTH = 50;

const generalFeedbackSchema = z.object({
  content: z
    .string()
    .min(1, "Feedback wird benötigt")
    .max(MAX_GENERAL_FEEDBACK_LENGTH, `Maximal ${MAX_GENERAL_FEEDBACK_LENGTH} Zeichen erlaubt`),
  submittedBy: z
    .string()
    .max(MAX_NAME_LENGTH, `Maximal ${MAX_NAME_LENGTH} Zeichen erlaubt`)
    .optional(),
});

const gameFeedbackSchema = z.object({
  gameSessionId: z.string({
    required_error: "Bitte wähle eine Spielsession aus",
  }),
  rating: z.number().min(1).max(5),
  positives: z
    .string()
    .max(MAX_POSITIVES_LENGTH, `Maximal ${MAX_POSITIVES_LENGTH} Zeichen erlaubt`)
    .optional(),
  improvements: z
    .string()
    .max(MAX_IMPROVEMENTS_LENGTH, `Maximal ${MAX_IMPROVEMENTS_LENGTH} Zeichen erlaubt`)
    .optional(),
  notes: z.string().max(MAX_NOTES_LENGTH, `Maximal ${MAX_NOTES_LENGTH} Zeichen erlaubt`).optional(),
  submittedBy: z
    .string()
    .max(MAX_NAME_LENGTH, `Maximal ${MAX_NAME_LENGTH} Zeichen erlaubt`)
    .optional(),
});

export function FeedbackForm() {
  const [mode, setMode] = useState<FeedbackMode>("general");
  const [sessions, setSessions] = useState<Array<{ id: string; formattedDate: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedRating, setSelectedRating] = useState(3);

  const generalForm = useForm<z.infer<typeof generalFeedbackSchema>>({
    resolver: zodResolver(generalFeedbackSchema),
    defaultValues: {
      content: "",
      submittedBy: "",
    },
  });

  const gameForm = useForm<z.infer<typeof gameFeedbackSchema>>({
    resolver: zodResolver(gameFeedbackSchema),
    defaultValues: {
      gameSessionId: "",
      rating: 3,
      positives: "",
      improvements: "",
      notes: "",
      submittedBy: "",
    },
  });

  React.useEffect(() => {
    if (mode === "game" && sessions.length === 0) {
      setLoading(true);
      getRecentSessions()
        .then((data) => {
          setSessions(data);
        })
        .catch((error) => {
          console.error("Error fetching sessions:", error);
          toast({
            title: "Fehler beim Laden der Spiele",
            description:
              "Die letzten Spiele konnten nicht geladen werden. Bitte versuche es erneut.",
            variant: "destructive",
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [mode, sessions.length]);

  // Synchronisiere selectedRating mit dem Formwert, wenn sich das Formular zurücksetzt
  React.useEffect(() => {
    const subscription = gameForm.watch((value, { name }) => {
      if (name === "rating" && typeof value.rating === "number") {
        setSelectedRating(value.rating);
      }
    });
    return () => subscription.unsubscribe();
  }, [gameForm]);

  const onSubmitGeneral = async (data: z.infer<typeof generalFeedbackSchema>) => {
    setSubmitting(true);
    try {
      await submitFeedback({
        content: data.content,
        submittedBy: data.submittedBy,
        isGameFeedback: false,
      });

      toast({
        title: "Feedback gesendet",
        description: "Vielen Dank für dein Feedback!",
      });

      generalForm.reset();
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast({
        title: "Fehler beim Senden des Feedbacks",
        description: "Bitte versuche es erneut.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onSubmitGame = async (data: z.infer<typeof gameFeedbackSchema>) => {
    setSubmitting(true);
    try {
      console.log("Form data being submitted:", data);
      console.log("Game session ID:", data.gameSessionId, "Type:", typeof data.gameSessionId);

      if (!data.gameSessionId) {
        toast({
          title: "Fehler beim Senden des Feedbacks",
          description: "Bitte wähle ein Spiel aus.",
          variant: "destructive",
        });
        setSubmitting(false);
        return;
      }

      await submitFeedback({
        content: data.notes || "Spiel-Feedback",
        isGameFeedback: true,
        gameSessionId: data.gameSessionId,
        rating: data.rating,
        positives: data.positives,
        improvements: data.improvements,
        notes: data.notes,
        submittedBy: data.submittedBy,
      });

      toast({
        title: "Spiel-Feedback gesendet",
        description: "Vielen Dank für dein Feedback!",
      });

      gameForm.reset();
      setSelectedRating(3);
    } catch (error) {
      console.error("Error submitting game feedback:", error);
      toast({
        title: "Fehler beim Senden des Feedbacks",
        description: "Bitte versuche es erneut.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <section className="w-full">
      <Card className="w-full dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader className="pb-0">
          <CardTitle className="text-2xl dark:text-white">Feedback</CardTitle>
          <CardDescription className="dark:text-zinc-400">
            Teile deine Gedanken mit uns, um zukünftige Spielsessions zu verbessern.
          </CardDescription>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>

        <CardContent className="pt-6">
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-3">Welche Art von Feedback möchtest du geben?</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button
                variant={mode === "game" ? "default" : "outline"}
                className={cn(
                  "justify-start h-auto p-4",
                  mode === "game" &&
                    "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
                )}
                onClick={() => setMode("game")}
              >
                <div className="flex flex-col items-start text-left">
                  <span className="font-semibold">📝 Feedback zu einem Spiel</span>
                  <span className="text-sm text-muted-foreground dark:text-zinc-400 mt-1">
                    Bewerte eine kürzliche Spielsession
                  </span>
                </div>
              </Button>
              <Button
                variant={mode === "general" ? "default" : "outline"}
                className={cn(
                  "justify-start h-auto p-4",
                  mode === "general" &&
                    "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
                )}
                onClick={() => setMode("general")}
              >
                <div className="flex flex-col items-start text-left">
                  <span className="font-semibold">💬 Allgemeines Feedback</span>
                  <span className="text-sm text-muted-foreground dark:text-zinc-400 mt-1">
                    Teile uns deine Gedanken mit
                  </span>
                </div>
              </Button>
            </div>
          </div>

          {mode === "general" ? (
            <Form {...generalForm}>
              <form onSubmit={generalForm.handleSubmit(onSubmitGeneral)} className="space-y-6">
                <FormField
                  control={generalForm.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dein Feedback</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Teile deine Gedanken, Vorschläge oder Ideen..."
                            className="min-h-32"
                            maxLength={MAX_GENERAL_FEEDBACK_LENGTH}
                            {...field}
                          />
                          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                            {field.value?.length || 0}/{MAX_GENERAL_FEEDBACK_LENGTH}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={generalForm.control}
                  name="submittedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dein Name (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Dein Name" maxLength={MAX_NAME_LENGTH} {...field} />
                      </FormControl>
                      <FormDescription>Lass uns wissen, wer du bist (optional)</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={submitting}
                  className="bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-800 dark:text-white dark:hover:bg-blue-700"
                >
                  {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Feedback absenden
                </Button>
              </form>
            </Form>
          ) : (
            <Form {...gameForm}>
              <form onSubmit={gameForm.handleSubmit(onSubmitGame)} className="space-y-6">
                <FormField
                  control={gameForm.control}
                  name="gameSessionId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Wähle ein Spiel</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          console.log("Session selected:", value);
                          field.onChange(value);
                          gameForm.setValue("gameSessionId", value, { shouldValidate: true });
                        }}
                        defaultValue={field.value}
                        disabled={loading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            {loading ? (
                              <div className="flex items-center">
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Lade Spiele...
                              </div>
                            ) : (
                              <SelectValue placeholder="Wähle ein Spiel aus" />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {sessions.map((session) => (
                            <SelectItem key={session.id} value={session.id}>
                              {session.formattedDate}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={gameForm.control}
                  name="rating"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Gesamtbewertung</FormLabel>
                      <FormControl>
                        <div className="flex justify-between">
                          {[1, 2, 3, 4, 5].map((rating) => (
                            <div key={rating} className="flex flex-col items-center space-y-2">
                              <Button
                                type="button"
                                variant={selectedRating === rating ? "default" : "outline"}
                                className={cn(
                                  "h-10 w-10",
                                  selectedRating === rating
                                    ? "bg-blue-600 text-white dark:bg-blue-700"
                                    : ""
                                )}
                                onClick={() => {
                                  setSelectedRating(rating);
                                  field.onChange(rating);
                                  gameForm.setValue("rating", rating, { shouldValidate: true });
                                }}
                              >
                                {rating}
                              </Button>
                              <FormLabel className="text-xs font-normal">
                                {rating === 1 ? "Schlecht" : rating === 5 ? "Ausgezeichnet" : ""}
                              </FormLabel>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={gameForm.control}
                  name="positives"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Was lief gut?</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Teile, was dir an dieser Spielsession gefallen hat..."
                            className="min-h-24"
                            maxLength={MAX_POSITIVES_LENGTH}
                            {...field}
                          />
                          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                            {field.value?.length || 0}/{MAX_POSITIVES_LENGTH}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={gameForm.control}
                  name="improvements"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Was könnte besser sein?</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Schlage Verbesserungen oder Änderungen für zukünftige Spiele vor..."
                            className="min-h-24"
                            maxLength={MAX_IMPROVEMENTS_LENGTH}
                            {...field}
                          />
                          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                            {field.value?.length || 0}/{MAX_IMPROVEMENTS_LENGTH}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={gameForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Zusätzliche Anmerkungen (optional)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Weitere Gedanken oder Kommentare..."
                            maxLength={MAX_NOTES_LENGTH}
                            {...field}
                          />
                          <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                            {field.value?.length || 0}/{MAX_NOTES_LENGTH}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={gameForm.control}
                  name="submittedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dein Name (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Dein Name" maxLength={MAX_NAME_LENGTH} {...field} />
                      </FormControl>
                      <FormDescription>Lass uns wissen, wer du bist (optional)</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={submitting}
                  className="bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-800 dark:text-white dark:hover:bg-blue-700"
                >
                  {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Spiel-Feedback absenden
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </section>
  );
}
