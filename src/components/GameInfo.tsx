import React, { useMemo } from "react";
import { getNextFriday } from "@/utils/dateUtils";
import { useGameSessions, useGameDuration, usePlayerData, useGameCountdown } from "@/hooks";
import { GameInfoSkeleton, NoGameScheduled, GameInfoCard } from "@/components/game";

/**
 * GameInfo component
 *
 * Main component for displaying information about upcoming games.
 * Responsible for fetching data and orchestrating the different display states.
 */
function GameInfo() {
  // Only fetch current session, not past sessions for the main page
  const { currentSession, loading: gameLoading } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });

  // Use custom hook for player data with realtime updates
  const { players, loading: playersLoading } = usePlayerData(currentSession?.id);

  // Calculate players with "in" status for game duration
  const inPlayersCount = useMemo(() => {
    return players.filter((p) => p.status === "in").length;
  }, [players]);

  // Next game date for countdown timer
  const nextGameDate = useMemo(() => {
    return currentSession?.date || getNextFriday();
  }, [currentSession?.date]);

  // Get game duration based on player count
  const { displayDuration, isLoading: durationLoading } = useGameDuration(
    currentSession,
    inPlayersCount
  );

  // Use countdown timer hook
  const timeToGame = useGameCountdown(nextGameDate);

  // Loading state
  if (gameLoading) {
    return <GameInfoSkeleton />;
  }

  // No game scheduled state
  if (!currentSession || currentSession.status !== "scheduled") {
    return <NoGameScheduled />;
  }

  // Render game info card with all the data
  return (
    <GameInfoCard
      currentSession={currentSession}
      players={players}
      timeToGame={timeToGame}
      playersLoading={playersLoading}
      durationLoading={durationLoading}
      displayDuration={displayDuration}
    />
  );
}

export default GameInfo;
