"use client";

import { useMemo } from "react";
import { useGameSessions } from "@/hooks/useGameSessions";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import { CalendarClock } from "lucide-react";
import { Player } from "@/types";
import { differenceInDays, format, parseISO } from "date-fns";
import { de } from "date-fns/locale";
import { PlayerName } from "@/components/player-profile/PlayerName";

interface PlayerWithComeback extends Player {
  daysAbsent: number;
  lastGameDate: Date;
  currentGameDate: Date;
}

export default function ComebackOfMonth() {
  const { pastSessions, loading } = useGameSessions();

  // Calculate players who came back after a long absence
  const comebackPlayers = useMemo(() => {
    if (!pastSessions || pastSessions.length === 0) {
      console.log("No past sessions available for comeback calculation");
      return [];
    }

    // Filter sessions with teams and sort by date (newest to oldest)
    const sortedSessions = [...pastSessions]
      .filter(
        (session) => session.date instanceof Date && session.teams && session.teams.length > 0
      )
      .sort((a, b) => b.date.getTime() - a.date.getTime()); // newest first

    if (sortedSessions.length < 2) {
      console.log("Not enough sessions to calculate comebacks");
      return [];
    }

    console.log(`Processing ${sortedSessions.length} sessions for comebacks`);

    // Track all players in each session
    const playersInSessions = sortedSessions.map((session) => {
      const players = new Set<string>();
      const playerObjects = new Map<string, Player>();

      if (session.teams) {
        session.teams.forEach((team) => {
          if (team.players) {
            team.players.forEach((player) => {
              if (player.id) {
                players.add(player.id);
                playerObjects.set(player.id, player);
              }
            });
          }
        });
      }

      return {
        date: session.date,
        playerIds: players,
        players: playerObjects,
      };
    });

    // Early return if no valid sessions found
    if (playersInSessions.length < 2) {
      return [];
    }

    // Look at most recent session
    const latestSession = playersInSessions[0];
    const comebacks: PlayerWithComeback[] = [];

    // For each player in the most recent session
    latestSession.playerIds.forEach((playerId) => {
      // Check when they last played before this session
      let lastPlayedIndex = -1;
      for (let i = 1; i < playersInSessions.length; i++) {
        if (playersInSessions[i].playerIds.has(playerId)) {
          lastPlayedIndex = i;
          break;
        }
      }

      // If they played before and we found their last session
      if (lastPlayedIndex > 0) {
        const lastPlayedDate = playersInSessions[lastPlayedIndex].date;
        const currentDate = latestSession.date;

        // Calculate days between their last game and current game
        const daysAbsent = differenceInDays(currentDate, lastPlayedDate);

        // If they were absent for more than 30 days (about a month)
        if (daysAbsent >= 30) {
          const playerData = latestSession.players.get(playerId);
          if (playerData) {
            comebacks.push({
              ...playerData,
              daysAbsent,
              lastGameDate: lastPlayedDate,
              currentGameDate: currentDate,
            });
          }
        }
      } else if (lastPlayedIndex === -1) {
        // This would mean they're in the current session but not in any previous ones
        // They're new, not a comeback, so we don't include them
      }
    });

    // Sort by days absent (descending)
    return comebacks.sort((a, b) => b.daysAbsent - a.daysAbsent).slice(0, 3);
  }, [pastSessions]);

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Comeback des Monats</CardTitle>
              <CardDescription>Spieler, die nach längerer Pause zurückgekehrt sind</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 ml-4">Lade Daten...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (comebackPlayers.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Comeback des Monats</CardTitle>
              <CardDescription>Spieler, die nach längerer Pause zurückgekehrt sind</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 dark:text-gray-400">Keine Comebacks verfügbar</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Comeback des Monats</CardTitle>
            <CardDescription>Spieler, die nach längerer Pause zurückgekehrt sind</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3">
          <div className="space-y-8 py-2">
            {comebackPlayers.map((player, index) => (
              <div key={player.id} className="flex items-center">
                {/* Rank/medal circle */}
                <div className="flex-shrink-0 flex items-center justify-center w-11 h-11 rounded-full bg-amber-100 dark:bg-amber-900/40 text-amber-800 dark:text-amber-300 font-semibold text-lg mr-3">
                  {index === 0 ? "🥇" : index === 1 ? "🥈" : "🥉"}
                </div>

                {/* Initials circle */}
                <div className="flex-shrink-0 flex items-center justify-center w-11 h-11 rounded-full bg-gray-200 dark:bg-zinc-700 text-gray-800 dark:text-gray-200 font-medium text-lg mr-3">
                  {player.jerseyNumber
                    ? "#" + player.jerseyNumber
                    : player.name && player.name.substring(0, 2).toUpperCase()}
                </div>

                {/* Player info */}
                <div className="flex flex-col flex-grow">
                  <PlayerName
                    player={player}
                    playerName={player.name}
                    className="font-medium text-base hover:text-blue-600 dark:hover:text-blue-400"
                  />
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <span>Zuletzt: {format(player.lastGameDate, "d. MMMM", { locale: de })}</span>
                  </div>
                </div>

                {/* Absence badge */}
                <div className="flex-shrink-0 flex items-center bg-blue-50 dark:bg-blue-950/40 text-blue-800 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-medium">
                  <CalendarClock className="h-5 w-5 mr-2" />
                  <span>{player.daysAbsent} Tage</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
