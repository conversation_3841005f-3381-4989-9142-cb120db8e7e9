"use client";

import { useMemo } from "react";
import { useGameSessions } from "@/hooks/useGameSessions";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import { Flame } from "lucide-react";
import { Player } from "@/types";
import { PlayerName } from "@/components/player-profile/PlayerName";

interface PlayerWithStreak extends Player {
  currentStreak: number;
  longestStreak: number;
}

export default function IronManAward() {
  const { pastSessions, loading } = useGameSessions();

  // Calculate the longest attendance streaks for each player
  const playersWithStreaks = useMemo(() => {
    if (!pastSessions || pastSessions.length === 0) {
      console.log("No past sessions available for streak calculation");
      return [];
    }

    // Filter sessions with teams and sort by date (newest to oldest)
    const sortedSessions = [...pastSessions]
      .filter(
        (session) => session.date instanceof Date && session.teams && session.teams.length > 0
      )
      .sort((a, b) => b.date.getTime() - a.date.getTime()); // newest first

    if (sortedSessions.length === 0) {
      console.log("No valid sessions with teams found");
      return [];
    }

    console.log(`Processing ${sortedSessions.length} sessions for streaks`);

    // Track player attendance by session
    const attendanceBySession = new Map<string, Set<string>>();

    // Process all sessions to record attendance
    sortedSessions.forEach((session) => {
      const sessionId = session.id || "";
      const attendees = new Set<string>();

      if (session.teams) {
        session.teams.forEach((team) => {
          if (team.players) {
            team.players.forEach((player) => {
              if (player.id) {
                attendees.add(player.id);
              }
            });
          }
        });
      }

      attendanceBySession.set(sessionId, attendees);
    });

    // Get all unique player IDs
    const allPlayerIds = new Set<string>();
    attendanceBySession.forEach((attendees) => {
      attendees.forEach((id) => allPlayerIds.add(id));
    });

    // Calculate streaks for each player
    const playerStreaks = new Map<
      string,
      {
        player: Player | null;
        currentStreak: number;
        longestStreak: number;
        name: string;
      }
    >();

    // Initialize player data
    allPlayerIds.forEach((playerId) => {
      playerStreaks.set(playerId, {
        player: null,
        currentStreak: 0,
        longestStreak: 0,
        name: "",
      });
    });

    // First pass: collect player data and names
    sortedSessions.forEach((session) => {
      if (session.teams) {
        session.teams.forEach((team) => {
          if (team.players) {
            team.players.forEach((player) => {
              if (player.id && playerStreaks.has(player.id)) {
                const data = playerStreaks.get(player.id);
                if (data) {
                  playerStreaks.set(player.id, {
                    ...data,
                    player,
                    name: player.name,
                  });
                }
              }
            });
          }
        });
      }
    });

    // Go through sessions in chronological order for streak calculation
    const chronologicalSessions = [...sortedSessions].reverse();

    chronologicalSessions.forEach((session) => {
      const sessionId = session.id || "";
      const attendees = attendanceBySession.get(sessionId) || new Set();

      allPlayerIds.forEach((playerId) => {
        const data = playerStreaks.get(playerId);
        if (!data) return;

        if (attendees.has(playerId)) {
          // Player attended this session
          const newCurrentStreak = data.currentStreak + 1;
          const newLongestStreak = Math.max(data.longestStreak, newCurrentStreak);

          playerStreaks.set(playerId, {
            ...data,
            currentStreak: newCurrentStreak,
            longestStreak: newLongestStreak,
          });
        } else {
          // Player missed this session - reset current streak
          playerStreaks.set(playerId, {
            ...data,
            currentStreak: 0,
          });
        }
      });
    });

    // Create result array with player data and streaks
    const result: PlayerWithStreak[] = [];

    playerStreaks.forEach((data, playerId) => {
      if (data.player && data.longestStreak > 0) {
        result.push({
          ...data.player,
          name: data.name,
          currentStreak: data.currentStreak,
          longestStreak: data.longestStreak,
        });
      }
    });

    // Sort by longest streak (descending), then by current streak (descending)
    return result
      .sort((a, b) => {
        if (b.longestStreak !== a.longestStreak) {
          return b.longestStreak - a.longestStreak;
        }
        return b.currentStreak - a.currentStreak;
      })
      .slice(0, 5); // Top 5 players
  }, [pastSessions]);

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Iron Man Award</CardTitle>
              <CardDescription>Spieler mit der längsten aktiven Teilnahmesträhne</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 ml-4">Lade Daten...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (playersWithStreaks.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Iron Man Award</CardTitle>
              <CardDescription>Spieler mit der längsten aktiven Teilnahmesträhne</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 dark:text-gray-400">Keine Daten verfügbar</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full flex flex-col overflow-hidden">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Iron Man Award</CardTitle>
            <CardDescription>Spieler mit der längsten aktiven Teilnahmesträhne</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3 flex-grow min-h-0 overflow-auto">
          <div className="space-y-8 py-2">
            {playersWithStreaks.map((player, index) => (
              <div key={player.id} className="flex items-center">
                {/* Rank circle */}
                <div className="flex-shrink-0 flex items-center justify-center w-11 h-11 rounded-full bg-gray-100 dark:bg-zinc-800 text-gray-500 dark:text-gray-400 font-semibold text-lg mr-3">
                  {index + 1}
                </div>

                {/* Initials circle */}
                <div className="flex-shrink-0 flex items-center justify-center w-11 h-11 rounded-full bg-gray-200 dark:bg-zinc-700 text-gray-800 dark:text-gray-200 font-medium text-lg mr-3">
                  {player.jerseyNumber
                    ? "#" + player.jerseyNumber
                    : player.name && player.name.substring(0, 2).toUpperCase()}
                </div>

                {/* Player info */}
                <div className="flex flex-col flex-grow">
                  <PlayerName
                    player={player}
                    playerName={player.name}
                    className="font-medium text-base hover:text-blue-600 dark:hover:text-blue-400"
                  />
                </div>

                {/* Streak badge */}
                <div className="flex-shrink-0 flex items-center bg-amber-50 dark:bg-amber-950/40 text-amber-800 dark:text-amber-300 px-4 py-2 rounded-full text-sm font-medium">
                  <Flame className="h-5 w-5 mr-2" />
                  <span>{player.longestStreak} Spiele</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
