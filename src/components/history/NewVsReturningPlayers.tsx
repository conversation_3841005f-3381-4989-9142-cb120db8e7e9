"use client";

import { useMemo, useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useGameSessions } from "@/hooks/useGameSessions";
import { format, parseISO, subWeeks, eachWeekOfInterval } from "date-fns";
import { de } from "date-fns/locale";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import { ChartContainer, ChartTooltipContent } from "@/components/ui/chart";

// Type for the processed chart data
interface ChartDataItem {
  period: string;
  newPlayers: number;
  returningPlayers: number;
  date: Date;
}

export default function NewVsReturningPlayers() {
  const { pastSessions, loading } = useGameSessions();

  // Process the data to identify new vs. returning players
  const chartData = useMemo(() => {
    if (!pastSessions || pastSessions.length === 0) {
      console.log("No past sessions available for chart");
      return [];
    }

    // Filter and sort sessions by date (oldest to newest)
    const sortedSessions = [...pastSessions]
      .filter(
        (session) => session.date instanceof Date && session.teams && session.teams.length > 0
      )
      .sort((a, b) => a.date.getTime() - b.date.getTime());

    if (sortedSessions.length === 0) {
      console.log("No valid sessions with teams found");
      return [];
    }

    const knownPlayersByWeek = new Map<string, Set<string>>();
    const allKnownPlayers = new Set<string>();

    // Group sessions by week
    const sessionsByWeek = new Map<string, typeof sortedSessions>();

    sortedSessions.forEach((session) => {
      if (!session.date) return;

      const weekStart = format(new Date(session.date), "yyyy-MM-dd", { locale: de });

      if (!sessionsByWeek.has(weekStart)) {
        sessionsByWeek.set(weekStart, []);
      }

      sessionsByWeek.get(weekStart)?.push(session);
    });

    // Create weekly data points
    const weeklyData: ChartDataItem[] = [];

    // Process weeks in chronological order
    const weekKeys = Array.from(sessionsByWeek.keys()).sort();

    weekKeys.forEach((weekKey) => {
      const sessions = sessionsByWeek.get(weekKey) || [];
      const weekDate = new Date(weekKey);

      // Find all unique players in this week's sessions
      const playersThisWeek = new Set<string>();

      sessions.forEach((session) => {
        if (session.teams) {
          session.teams.forEach((team) => {
            if (team.players) {
              team.players.forEach((player) => {
                if (player.id) {
                  playersThisWeek.add(player.id);
                }
              });
            }
          });
        }
      });

      // Count new vs. returning players for this week
      let newPlayersCount = 0;
      let returningPlayersCount = 0;

      playersThisWeek.forEach((playerId) => {
        if (allKnownPlayers.has(playerId)) {
          returningPlayersCount++;
        } else {
          newPlayersCount++;
          // Add to all known players for future weeks
          allKnownPlayers.add(playerId);
        }
      });

      // Save known players set for this week
      knownPlayersByWeek.set(weekKey, new Set(allKnownPlayers));

      // Create data point for this week
      const periodLabel = format(weekDate, "dd.MM", { locale: de });

      weeklyData.push({
        period: periodLabel,
        newPlayers: newPlayersCount,
        returningPlayers: returningPlayersCount,
        date: weekDate,
      });
    });

    // For better visualization, limit to last 8 weeks if there are more
    if (weeklyData.length > 8) {
      return weeklyData.slice(-8);
    }

    return weeklyData;
  }, [pastSessions]);

  // Chart configuration
  const chartConfig = {
    newPlayers: {
      label: "Neue Spieler",
      color: "hsl(var(--chart-1))",
    },
    returningPlayers: {
      label: "Stammspieler",
      color: "hsl(var(--chart-2))",
    },
  };

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Neue vs. Wiederkehrende Spieler</CardTitle>
              <CardDescription>
                Verhältnis zwischen neuen und wiederkehrenden Spielern
              </CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 ml-4">Lade Daten...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (chartData.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Neue vs. Wiederkehrende Spieler</CardTitle>
              <CardDescription>
                Verhältnis zwischen neuen und wiederkehrenden Spielern
              </CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 dark:text-gray-400">Keine Daten verfügbar</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  // Calculate total averages for the summary text
  const totalNewPlayers = chartData.reduce((sum, item) => sum + item.newPlayers, 0);
  const totalReturningPlayers = chartData.reduce((sum, item) => sum + item.returningPlayers, 0);
  const avgNewPlayers = Math.round(totalNewPlayers / chartData.length);
  const avgReturningPlayers = Math.round(totalReturningPlayers / chartData.length);
  const avgTotalPlayers = avgNewPlayers + avgReturningPlayers;

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full flex flex-col overflow-hidden">
        <CardHeader className="pb-2 flex-shrink-0">
          <CardTitle className="dark:text-white">Neue vs. Wiederkehrende Spieler</CardTitle>
          <CardDescription className="dark:text-zinc-400">
            Verhältnis zwischen neuen und Stammspielern pro Woche
          </CardDescription>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-2 flex-grow min-h-0 p-0 pb-0 flex flex-col">
          <div className="w-full h-full p-4">
            <ChartContainer config={chartConfig}>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 10 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="period"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={10}
                    fontSize={11}
                    tick={{ fill: "var(--foreground)" }}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tickMargin={10}
                    fontSize={11}
                    tick={{ fill: "var(--foreground)" }}
                    width={30}
                  />
                  <Tooltip
                    content={
                      <ChartTooltipContent
                        labelKey="period"
                        formatter={(value) => `${value} Spieler`}
                      />
                    }
                  />
                  <Legend wrapperStyle={{ paddingTop: "10px" }} />
                  <Line
                    type="monotone"
                    dataKey="newPlayers"
                    stroke="hsl(var(--chart-1))"
                    name="Neue Spieler"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="returningPlayers"
                    stroke="hsl(var(--chart-2))"
                    name="Stammspieler"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>
          <div className="text-center text-sm text-muted-foreground mb-4 mt-auto px-4">
            Durchschnittlich {avgTotalPlayers} Spieler pro Woche, davon {avgNewPlayers} neue und{" "}
            {avgReturningPlayers} wiederkehrende Spieler.
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
