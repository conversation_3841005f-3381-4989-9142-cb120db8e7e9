import { usePlayers } from "@/hooks/usePlayers";
import { useGameSessions } from "@/hooks/useGameSessions";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useEffect, useState } from "react";
import { Player } from "@/types";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";

interface PlayerWithStats extends Player {
  participationCount: number;
  participationPercentage: number;
}

export default function TopParticipants() {
  const { players } = usePlayers();
  const { pastSessions, loading } = useGameSessions();
  const [topPlayers, setTopPlayers] = useState<PlayerWithStats[]>([]);

  useEffect(() => {
    // Only calculate when we have past sessions with teams data
    if (!pastSessions || pastSessions.length === 0) {
      console.log("No past sessions available for top players calculation");
      setTopPlayers([]);
      return;
    }

    console.log("Calculating top players from past sessions:", pastSessions.length);

    // Count participation for each player by scanning all teams
    const playerParticipation = new Map<string, number>();
    let anyTeamsFound = false;

    pastSessions.forEach((session) => {
      if (session.teams && session.teams.length > 0) {
        anyTeamsFound = true;
        session.teams.forEach((team) => {
          if (team.players && team.players.length > 0) {
            team.players.forEach((player) => {
              if (player.id) {
                const currentCount = playerParticipation.get(player.id) || 0;
                playerParticipation.set(player.id, currentCount + 1);
              }
            });
          }
        });
      }
    });

    if (!anyTeamsFound) {
      console.log("No teams found in past sessions");
      setTopPlayers([]);
      return;
    }

    console.log("Player participation data:", Object.fromEntries(playerParticipation));

    // Create array with all players and their stats
    const playersWithStats: PlayerWithStats[] = players
      .map((player) => {
        const participationCount = playerParticipation.get(player.id || "") || 0;
        const participationPercentage =
          pastSessions.length > 0
            ? Math.round((participationCount / pastSessions.length) * 100)
            : 0;

        return {
          ...player,
          participationCount,
          participationPercentage,
        };
      })
      // Filter to only include players who have participated
      .filter((player) => player.participationCount > 0);

    // Sort by participation count (descending) and take top 5
    const sorted = playersWithStats
      .sort((a, b) => b.participationCount - a.participationCount)
      .slice(0, 5);

    console.log("Top players calculated:", sorted);
    setTopPlayers(sorted);
  }, [players, pastSessions]);

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Top Teilnehmer</CardTitle>
              <CardDescription>Spieler mit den meisten Teilnahmen</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex flex-col items-center justify-center py-8">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 mt-4">Lade Spielerstatistiken...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (topPlayers.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Top Teilnehmer</CardTitle>
              <CardDescription>Spieler mit den meisten Teilnahmen</CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <p className="text-gray-500 dark:text-gray-400">
              Noch keine Spielestatistiken verfügbar
            </p>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Top Teilnehmer</CardTitle>
            <CardDescription>Spieler mit den meisten Teilnahmen</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3">
          <div className="space-y-2">
            {topPlayers.map((player, index) => (
              <div
                key={player.id}
                className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-zinc-900/30"
              >
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-6 h-6 text-sm font-semibold">
                    #{index + 1}
                  </div>
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-sm">
                      {player.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{player.name}</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {player.participationCount}{" "}
                      {player.participationCount === 1 ? "Spiel" : "Spiele"}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
