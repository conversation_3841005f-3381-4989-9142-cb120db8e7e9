import { render, screen } from "@testing-library/react";
import { MatchResultsDisplay } from "../MatchResultsDisplay";
import { MatchResult } from "@/types/match-results";
import "@testing-library/jest-dom";
import { describe, it, expect } from "vitest";

describe("MatchResultsDisplay", () => {
  const mockSessionId = "test-session-id";
  const mockTeamMap = {
    "team-weiss": "Weiß",
    "team-bunt": "Bunt",
    "team-leibchen": "Leibchen",
  };

  const mockResults: Record<string, MatchResult[]> = {
    "test-session-id": [
      {
        id: "1",
        game_session_id: mockSessionId,
        team_a_id: "team-weiss",
        team_b_id: "team-leibchen",
        goals_a: 3,
        goals_b: 2,
        match_order: 1,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "2",
        game_session_id: mockSessionId,
        team_a_id: "team-bunt",
        team_b_id: "team-leibchen",
        goals_a: 4,
        goals_b: 2,
        match_order: 2,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "3",
        game_session_id: mockSessionId,
        team_a_id: "team-bunt",
        team_b_id: "team-weiss",
        goals_a: 3,
        goals_b: 5,
        match_order: 3,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "4",
        game_session_id: mockSessionId,
        team_a_id: "team-weiss",
        team_b_id: "team-leibchen",
        goals_a: 5,
        goals_b: 3,
        match_order: 4,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "5",
        game_session_id: mockSessionId,
        team_a_id: "team-bunt",
        team_b_id: "team-leibchen",
        goals_a: 1,
        goals_b: 2,
        match_order: 5,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "6",
        game_session_id: mockSessionId,
        team_a_id: "team-weiss",
        team_b_id: "team-bunt",
        goals_a: 2,
        goals_b: 2,
        match_order: 6,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "7",
        game_session_id: mockSessionId,
        team_a_id: "team-weiss",
        team_b_id: "team-leibchen",
        goals_a: 3,
        goals_b: 3,
        match_order: 7,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "8",
        game_session_id: mockSessionId,
        team_a_id: "team-bunt",
        team_b_id: "team-leibchen",
        goals_a: 5,
        goals_b: 3,
        match_order: 8,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
      {
        id: "9",
        game_session_id: mockSessionId,
        team_a_id: "team-bunt",
        team_b_id: "team-weiss",
        goals_a: 8,
        goals_b: 4,
        match_order: 9,
        created_at: "2024-01-01",
        updated_at: "2024-01-01",
      },
    ],
  };

  it("displays loading state", () => {
    render(
      <MatchResultsDisplay
        sessionId={mockSessionId}
        teamMap={mockTeamMap}
        results={{}}
        loading={true}
      />
    );
    expect(screen.getByText(/Ergebnisse werden geladen/i)).toBeInTheDocument();
  });

  it("displays no results message when empty", () => {
    render(
      <MatchResultsDisplay
        sessionId={mockSessionId}
        teamMap={mockTeamMap}
        results={{}}
        loading={false}
      />
    );
    expect(screen.getByText(/Keine Spielergebnisse verfügbar/i)).toBeInTheDocument();
  });

  it("displays correct match results and winner based on wins and goal difference", () => {
    render(
      <MatchResultsDisplay
        sessionId={mockSessionId}
        teamMap={mockTeamMap}
        results={mockResults}
        loading={false}
      />
    );

    // Calculate expected statistics
    const stats = {
      Weiß: {
        wins: 3, // Games 1, 3, 4
        goalsFor: 22, // 3 + 5 + 5 + 2 + 3 + 4 = 22
        goalsAgainst: 21, // 2 + 3 + 3 + 2 + 3 + 8 = 21
        goalDiff: 1,
      },
      Bunt: {
        wins: 3, // Games 2, 8, 9
        goalsFor: 23, // 4 + 3 + 1 + 2 + 5 + 8 = 23
        goalsAgainst: 16, // 2 + 5 + 2 + 2 + 3 + 4 = 18
        goalDiff: 5,
      },
      Leibchen: {
        wins: 1, // Game 5
        goalsFor: 15, // 2 + 2 + 2 + 3 + 3 + 3 = 15
        goalsAgainst: 21, // 3 + 4 + 5 + 1 + 3 + 5 = 21
        goalDiff: -6,
      },
    };

    // Bunt should be the winner due to better goal difference (+5 vs +1)
    expect(screen.getByText(/Gesamtsieger:/i)).toBeInTheDocument();
    expect(screen.getByRole("heading").textContent).toContain("Bunt");
    expect(screen.getByText(/3 Siege/i)).toBeInTheDocument();
    expect(screen.getByText(/Tordifferenz: \+5/i)).toBeInTheDocument();

    // Check all matches are displayed with correct results
    mockResults[mockSessionId].forEach((match, index) => {
      const matchNumber = index + 1;
      const matchElement = screen.getByText(`Spiel #${matchNumber}`).closest("div")?.parentElement;
      expect(matchElement).toBeInTheDocument();

      if (matchElement) {
        // Verify teams and scores
        const teamA = mockTeamMap[match.team_a_id];
        const teamB = mockTeamMap[match.team_b_id];
        const scoreA = match.goals_a;
        const scoreB = match.goals_b;

        // Verify teams are displayed
        expect(matchElement.textContent).toContain(teamA);
        expect(matchElement.textContent).toContain(teamB);

        // Verify scores are displayed
        expect(matchElement.textContent).toContain(scoreA.toString());
        expect(matchElement.textContent).toContain(scoreB.toString());

        // Verify winner highlighting
        const teams = matchElement.querySelectorAll(".text-base.font-medium");
        const teamAElement = Array.from(teams).find((team) => team.textContent === teamA);
        const teamBElement = Array.from(teams).find((team) => team.textContent === teamB);

        if (scoreA > scoreB) {
          expect(teamAElement).toHaveClass("text-blue-500");
          expect(teamBElement).toHaveClass("text-gray-700");
        } else if (scoreB > scoreA) {
          expect(teamAElement).toHaveClass("text-gray-700");
          expect(teamBElement).toHaveClass("text-blue-500");
        } else {
          // In case of a draw
          expect(teamAElement).toHaveClass("text-gray-700");
          expect(teamBElement).toHaveClass("text-gray-700");
        }
      }
    });
  });

  // Add a new test for equal wins but different goal differences
  it("determines winner correctly when teams have equal wins but different goal differences", () => {
    const equalWinsResults: Record<string, MatchResult[]> = {
      "test-session-id": [
        {
          id: "1",
          game_session_id: mockSessionId,
          team_a_id: "team-weiss",
          team_b_id: "team-leibchen",
          goals_a: 5,
          goals_b: 0,
          match_order: 1,
          created_at: "2024-01-01",
          updated_at: "2024-01-01",
        },
        {
          id: "2",
          game_session_id: mockSessionId,
          team_a_id: "team-bunt",
          team_b_id: "team-leibchen",
          goals_a: 2,
          goals_b: 1,
          match_order: 2,
          created_at: "2024-01-01",
          updated_at: "2024-01-01",
        },
      ],
    };

    render(
      <MatchResultsDisplay
        sessionId={mockSessionId}
        teamMap={mockTeamMap}
        results={equalWinsResults}
        loading={false}
      />
    );

    // Both teams have 1 win, but Weiß has better goal difference (+5 vs +1)
    expect(screen.getByText(/Gesamtsieger:/i)).toBeInTheDocument();
    expect(screen.getByRole("heading").textContent).toContain("Weiß");
    expect(screen.getByText(/1 Sieg/i)).toBeInTheDocument();
    expect(screen.getByText(/Tordifferenz: \+5/i)).toBeInTheDocument();
  });

  // Add a test for complete tie (equal wins and goal difference)
  it("shows tie when teams have equal wins and goal differences", () => {
    const tieResults: Record<string, MatchResult[]> = {
      "test-session-id": [
        {
          id: "1",
          game_session_id: mockSessionId,
          team_a_id: "team-weiss",
          team_b_id: "team-leibchen",
          goals_a: 2,
          goals_b: 0,
          match_order: 1,
          created_at: "2024-01-01",
          updated_at: "2024-01-01",
        },
        {
          id: "2",
          game_session_id: mockSessionId,
          team_a_id: "team-bunt",
          team_b_id: "team-leibchen",
          goals_a: 2,
          goals_b: 0,
          match_order: 2,
          created_at: "2024-01-01",
          updated_at: "2024-01-01",
        },
      ],
    };

    render(
      <MatchResultsDisplay
        sessionId={mockSessionId}
        teamMap={mockTeamMap}
        results={tieResults}
        loading={false}
      />
    );

    // Both teams have 1 win and +2 goal difference
    expect(screen.getByText("Unentschieden")).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return (
          element?.tagName.toLowerCase() === "p" &&
          content.includes("Jedes Team hat 1 Sieg") &&
          content.includes("Tordifferenz von 2")
        );
      })
    ).toBeInTheDocument();
  });
});
