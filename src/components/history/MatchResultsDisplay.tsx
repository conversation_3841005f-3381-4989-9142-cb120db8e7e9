import { MatchResult } from "@/types/match-results";
import { getOverallWinner } from "@/hooks/useMatchResults";
import { cn } from "@/lib/utils";
import { Award, Flag } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";

interface MatchResultsDisplayProps {
  sessionId: string;
  teamMap: Record<string, string>;
  results: Record<string, MatchResult[]>;
  loading: boolean;
}

export const MatchResultsDisplay = ({
  sessionId,
  teamMap,
  results,
  loading,
}: MatchResultsDisplayProps) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-4">
        <LoadingSpinner size="sm" />
        <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
          Ergebnisse werden geladen...
        </span>
      </div>
    );
  }

  const sessionResults = results[sessionId] || [];

  if (sessionResults.length === 0) {
    return (
      <div className="p-4 text-center bg-gray-50 dark:bg-zinc-800/50 rounded-lg border border-gray-100 dark:border-zinc-800">
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Keine Spielergebnisse verfügbar
        </span>
      </div>
    );
  }

  // Gesamtsieger ermitteln
  const overallWinner = getOverallWinner(sessionResults, teamMap);

  return (
    <div className="space-y-4">
      {/* Gesamtsieger oder Unentschieden anzeigen */}
      {overallWinner && (
        <div
          className={cn(
            "p-4 rounded-lg flex items-center shadow-sm",
            overallWinner.isTie
              ? "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-zinc-800/60 dark:to-zinc-800/40 border border-gray-200 dark:border-zinc-700"
              : "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-900/10 border border-blue-200 dark:border-blue-800"
          )}
        >
          <div
            className={cn(
              "rounded-full p-2 mr-3",
              overallWinner.isTie ? "bg-gray-200 dark:bg-zinc-700" : "bg-blue-200 dark:bg-blue-800"
            )}
          >
            {overallWinner.isTie ? (
              <Flag className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            ) : (
              <Award className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            )}
          </div>
          <div>
            {overallWinner.isTie ? (
              <>
                <h3 className="font-medium text-gray-800 dark:text-gray-300">Unentschieden</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                  Jedes Team hat {overallWinner.wins} {overallWinner.wins === 1 ? "Sieg" : "Siege"}
                  {overallWinner.goalDifference !== 0 &&
                    ` und eine Tordifferenz von ${overallWinner.goalDifference}`}
                </p>
              </>
            ) : (
              <>
                <h3 className="font-medium text-blue-800 dark:text-blue-400">
                  Gesamtsieger: <span className="font-bold">{overallWinner.teamName}</span>
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                  {overallWinner.wins} {overallWinner.wins === 1 ? "Sieg" : "Siege"}
                  {" • "}
                  Tordifferenz: {overallWinner.goalDifference > 0 ? "+" : ""}
                  {overallWinner.goalDifference}
                </p>
              </>
            )}
          </div>
        </div>
      )}

      {/* Einzelne Spielergebnisse anzeigen */}
      <div className="space-y-3">
        <div className="grid grid-cols-1 gap-3">
          {sessionResults
            .sort((a, b) => (a.match_order || 0) - (b.match_order || 0))
            .map((result) => {
              // Use the team name from the map or create a more meaningful fallback
              const teamAId = result.team_a_id || "";
              const teamBId = result.team_b_id || "";
              const teamA =
                teamMap[teamAId] || (teamAId ? `Team ${teamAId.substring(0, 4)}` : "Unbekannt");
              const teamB =
                teamMap[teamBId] || (teamBId ? `Team ${teamBId.substring(0, 4)}` : "Unbekannt");
              const isTeamAWinner = result.goals_a > result.goals_b;
              const isTeamBWinner = result.goals_b > result.goals_a;
              const isTie = result.goals_a === result.goals_b;

              return (
                <div
                  key={result.id}
                  className="bg-white dark:bg-zinc-900 border border-gray-100 dark:border-zinc-800 rounded-lg overflow-hidden shadow-sm"
                >
                  <div className="p-3 border-b border-gray-100 dark:border-zinc-800 flex justify-between items-center bg-gray-50 dark:bg-zinc-800/30">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Spiel #{result.match_order || "?"}
                    </span>
                    {result.field && (
                      <Badge
                        variant="outline"
                        className="bg-gray-100 dark:bg-zinc-800 text-gray-700 dark:text-gray-300"
                      >
                        Feld: {result.field}
                      </Badge>
                    )}
                  </div>

                  <div className="p-4">
                    {/* Mobile Layout */}
                    <div className="sm:hidden flex flex-col space-y-2">
                      <div className="flex justify-between items-center">
                        <span
                          className={cn(
                            "text-base font-medium",
                            isTeamAWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {teamA}
                        </span>
                        <span
                          className={cn(
                            "text-xl font-bold ml-3",
                            isTeamAWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {result.goals_a}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span
                          className={cn(
                            "text-base font-medium",
                            isTeamBWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {teamB}
                        </span>
                        <span
                          className={cn(
                            "text-xl font-bold ml-3",
                            isTeamBWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {result.goals_b}
                        </span>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden sm:flex items-center justify-between gap-4">
                      {/* Team A */}
                      <div className="flex-1 text-right">
                        <span
                          className={cn(
                            "text-base font-medium",
                            isTeamAWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {teamA}
                        </span>
                      </div>

                      {/* Score */}
                      <div className="flex items-center bg-gray-100 dark:bg-zinc-800 rounded-lg p-1 px-3 mx-3">
                        <span
                          className={cn(
                            "text-xl font-bold px-2",
                            isTeamAWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {result.goals_a}
                        </span>

                        <span className="text-xl font-bold text-gray-400 dark:text-gray-500 px-0.5">
                          :
                        </span>

                        <span
                          className={cn(
                            "text-xl font-bold px-2",
                            isTeamBWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {result.goals_b}
                        </span>
                      </div>

                      {/* Team B */}
                      <div className="flex-1">
                        <span
                          className={cn(
                            "text-base font-medium",
                            isTeamBWinner
                              ? "text-blue-500 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          )}
                        >
                          {teamB}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

// Need to import this component
import { Badge } from "@/components/ui/badge";
