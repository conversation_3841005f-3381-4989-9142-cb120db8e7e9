"use client";

import { useMemo, useEffect, useState } from "react";
import { <PERSON>, BarChart, CartesianGrid, XAxis, YAxis, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { useGameSessions } from "@/hooks/useGameSessions";
import { format, startOfWeek, endOfWeek, addWeeks, isBefore, isAfter } from "date-fns";
import { de } from "date-fns/locale";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";

// Type for the processed chart data
interface ChartDataItem {
  period: string;
  count: number;
  startDate: Date;
  endDate: Date;
}

export default function ParticipationOverTime() {
  const { pastSessions, loading } = useGameSessions();

  useEffect(() => {
    if (pastSessions && pastSessions.length > 0) {
      console.log("Past sessions available for chart:", pastSessions.length);
      if (pastSessions[0]?.teams) {
        console.log("Sample session teams:", pastSessions[0].teams.length);
      }
    }
  }, [pastSessions]);

  // Process the data to group by weeks
  const chartData = useMemo(() => {
    if (!pastSessions || pastSessions.length === 0) {
      console.log("No past sessions available for chart");
      return [];
    }

    // Find the earliest and latest dates in the dataset
    const sortedDates = [...pastSessions]
      .filter((session) => session.date instanceof Date)
      .sort((a, b) => a.date.getTime() - b.date.getTime());

    if (sortedDates.length === 0) {
      console.log("No valid dates found in sessions");
      return [];
    }

    console.log(
      `Date range: ${sortedDates[0].date} to ${sortedDates[sortedDates.length - 1].date}`
    );

    const firstDate = sortedDates[0].date;
    const lastDate = sortedDates[sortedDates.length - 1].date;

    // Set the start date to the beginning of the week of the first session
    const startDate = startOfWeek(firstDate, { weekStartsOn: 1 }); // Week starts on Monday

    // Set the end date to the end of the week of the last session
    const endDate = endOfWeek(lastDate, { weekStartsOn: 1 });

    // Create an array of week periods
    const weekPeriods: ChartDataItem[] = [];
    let currentWeekStart = startDate;

    while (
      isBefore(currentWeekStart, endDate) ||
      currentWeekStart.getTime() === endDate.getTime()
    ) {
      const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });

      // Count players in this week
      let playerCount = 0;

      // For each session in this week, count the total participants
      pastSessions.forEach((session) => {
        const sessionDate = session.date;
        if (
          sessionDate instanceof Date &&
          (isAfter(sessionDate, currentWeekStart) ||
            sessionDate.getTime() === currentWeekStart.getTime()) &&
          (isBefore(sessionDate, currentWeekEnd) ||
            sessionDate.getTime() === currentWeekEnd.getTime())
        ) {
          // Count unique players in this session from all teams
          if (session.teams && session.teams.length > 0) {
            const playersInSession = new Set();
            session.teams.forEach((team) => {
              if (team.players && team.players.length > 0) {
                team.players.forEach((player) => {
                  if (player.id) {
                    playersInSession.add(player.id);
                  }
                });
              }
            });
            playerCount += playersInSession.size;
          }
        }
      });

      // Format the period label
      const periodLabel = `${format(currentWeekStart, "dd.MM", { locale: de })} - ${format(currentWeekEnd, "dd.MM", { locale: de })}`;

      weekPeriods.push({
        period: periodLabel,
        count: playerCount,
        startDate: currentWeekStart,
        endDate: currentWeekEnd,
      });

      // Move to next week
      currentWeekStart = addWeeks(currentWeekStart, 1);
    }

    console.log("Generated chart data:", weekPeriods);

    // If no data with counts, add dummy data for testing
    if (weekPeriods.length === 0 || weekPeriods.every((period) => period.count === 0)) {
      const today = new Date();
      const weekAgo = new Date(today);
      weekAgo.setDate(today.getDate() - 7);
      const twoWeeksAgo = new Date(today);
      twoWeeksAgo.setDate(today.getDate() - 14);

      return [
        {
          period: `${format(twoWeeksAgo, "dd.MM", { locale: de })} - ${format(weekAgo, "dd.MM", { locale: de })}`,
          count: 8,
          startDate: twoWeeksAgo,
          endDate: weekAgo,
        },
        {
          period: `${format(weekAgo, "dd.MM", { locale: de })} - ${format(today, "dd.MM", { locale: de })}`,
          count: 12,
          startDate: weekAgo,
          endDate: today,
        },
      ];
    }

    return weekPeriods;
  }, [pastSessions]);

  // Chart configuration
  const chartConfig = {
    count: {
      label: "Teilnehmer",
      color: "hsl(var(--chart-1))",
    },
  };

  if (loading) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Teilnahme über Zeit</CardTitle>
              <CardDescription>
                Entwicklung der Spielerteilnahme über die letzten Monate
              </CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <LoadingSpinner size="md" />
              <p className="text-gray-500 dark:text-gray-400 ml-4">Lade Daten...</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (chartData.length === 0) {
    return (
      <section className="w-full h-full">
        <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full overflow-hidden">
          <CardHeader>
            <div className="space-y-1">
              <CardTitle>Teilnahme über Zeit</CardTitle>
              <CardDescription>
                Entwicklung der Spielerteilnahme über die letzten Monate
              </CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3">
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 dark:text-gray-400">Keine Daten verfügbar</p>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="w-full h-full">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800 h-full flex flex-col overflow-hidden">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Teilnahme über Zeit</CardTitle>
            <CardDescription>
              Entwicklung der Spielerteilnahme über die letzten Monate
            </CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3 flex-grow min-h-0 p-0 pb-0">
          <div className="w-full h-full">
            <ChartContainer config={chartConfig}>
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 30, bottom: 20 }}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="period"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  fontSize={11}
                  tick={{ fill: "var(--foreground)" }}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  fontSize={11}
                  tick={{ fill: "var(--foreground)" }}
                  width={30}
                />
                <Tooltip
                  content={
                    <ChartTooltipContent
                      labelKey="period"
                      formatter={(value) => `${value} Spieler`}
                    />
                  }
                />
                <Legend content={<ChartLegendContent />} verticalAlign="bottom" height={36} />
                <Bar
                  dataKey="count"
                  name="Teilnehmer"
                  fill="hsl(var(--chart-1))"
                  radius={4}
                  maxBarSize={60}
                />
              </BarChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
