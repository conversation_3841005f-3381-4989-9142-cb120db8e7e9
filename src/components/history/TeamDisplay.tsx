import { <PERSON><PERSON><PERSON>, <PERSON>, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PlayerName } from "@/components/player-profile/PlayerName";
import { cn } from "@/lib/utils";
import { Team } from "@/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";

interface TeamDisplayProps {
  teams: Team[];
  isAdminView?: boolean;
  sessionId: string;
  mvpPlayerNames?: string[];
}

export const TeamDisplay = ({
  teams,
  isAdminView = false,
  sessionId,
  mvpPlayerNames = [],
}: TeamDisplayProps) => {
  if (!teams || teams.length === 0) {
    return (
      <div className="p-4 text-center bg-gray-50 dark:bg-zinc-800/50 rounded-lg border border-gray-100 dark:border-zinc-800">
        <span className="text-sm text-gray-500 dark:text-gray-400">Keine Teams verfügbar</span>
      </div>
    );
  }

  // Helper to check if a player is an MVP for this match
  const isPlayerMvp = (playerName: string) => {
    return mvpPlayerNames.includes(playerName);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      {teams.map((team, index) => (
        <Card key={team.id || index} className="dark:bg-zinc-900 dark:border-zinc-800">
          <CardHeader className="p-4 pb-0">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-300 to-blue-400 dark:bg-gradient-to-r dark:from-blue-600/40 dark:to-blue-500/40 flex items-center justify-center border border-blue-300 dark:border-blue-500/40">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-300" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between gap-2">
                  <div className="min-w-0">
                    <p className="text-lg font-semibold truncate dark:text-white">{team.name}</p>
                    <p className="text-sm text-muted-foreground">{team.players.length} Spieler</p>
                  </div>
                  {team.averageRating && (
                    <Badge
                      variant="secondary"
                      className="h-7 shrink-0 px-2.5 bg-gradient-to-r from-blue-100 to-blue-200 hover:from-blue-200 hover:to-blue-300 text-blue-600 dark:bg-gradient-to-r dark:from-blue-900/20 dark:to-blue-800/30 dark:text-blue-300 dark:hover:from-blue-900/30 dark:hover:to-blue-800/40 border border-blue-200 dark:border-blue-700/30"
                    >
                      <BarChart className="h-3.5 w-3.5 mr-1" />Ø {team.averageRating.toFixed(1)}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-2">
              {team.players.map((player) => (
                <div
                  key={player.id}
                  className={cn(
                    "flex items-center justify-between py-1.5 px-2 rounded-md text-sm",
                    isPlayerMvp(player.name)
                      ? "bg-gradient-to-r from-blue-50 to-blue-100 dark:bg-gradient-to-r dark:from-blue-900/20 dark:to-blue-800/30"
                      : "hover:bg-gray-50 dark:hover:bg-zinc-800/50"
                  )}
                >
                  <div className="flex items-center gap-2 min-w-0">
                    <PlayerName
                      playerId={player.id}
                      playerName={player.name}
                      className={cn(
                        "truncate hover:text-blue-600 dark:hover:text-blue-400",
                        isPlayerMvp(player.name) && "font-medium text-blue-600 dark:text-blue-400"
                      )}
                    />
                    {isPlayerMvp(player.name) && (
                      <Trophy className="h-3.5 w-3.5 shrink-0 text-amber-500 dark:text-amber-400" />
                    )}
                  </div>
                  {isAdminView && (
                    <Badge
                      variant="secondary"
                      className="ml-2 shrink-0 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border border-gray-200 dark:bg-gradient-to-r dark:from-zinc-800 dark:to-zinc-700 dark:text-zinc-300 dark:border-zinc-700"
                    >
                      {player.rating}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
