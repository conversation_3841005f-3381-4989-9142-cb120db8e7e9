import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { Heart, Instagram, Mail, MapPin, ExternalLink, Github } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

interface FooterProps {
  className?: string;
}

export default function Footer({ className }: FooterProps) {
  const location = useLocation();
  const isAdminPage = location.pathname.startsWith("/admin");
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // Check if user is logged in
    const checkLoginStatus = () => {
      try {
        const loggedIn = localStorage.getItem("isLoggedIn") === "true";
        setIsLoggedIn(loggedIn);
      } catch (error) {
        console.error("Error accessing localStorage:", error);
        setIsLoggedIn(false);
      }
    };

    checkLoginStatus();
  }, []);

  // Simple admin footer version
  if (isAdminPage) {
    return (
      <footer
        className={cn(
          "py-4 px-6 text-center text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-zinc-800 mt-8",
          className
        )}
      >
        <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
          <p>© {new Date().getFullYear()} INTER NRW Football Group</p>
          <span className="hidden sm:inline">•</span>
          <a
            href="https://ckrdigital.de"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 group"
          >
            <span>Made with</span>
            <Heart className="h-3 w-3 text-yellow-500 fill-yellow-500 group-hover:animate-pulse" />
            <span>by ckrdigital</span>
          </a>
        </div>
      </footer>
    );
  }

  // Login page or not logged in - simplified footer without navigation links
  if (!isLoggedIn) {
    return (
      <footer
        className={cn(
          "relative px-6 py-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-zinc-900 dark:to-zinc-950 border-t border-gray-200 dark:border-zinc-800 w-full mt-8 overflow-hidden",
          className
        )}
      >
        {/* Wave Background */}
        <div className="absolute inset-0 z-0 opacity-[0.03] dark:opacity-[0.05] overflow-hidden pointer-events-none">
          <svg
            className="absolute w-full min-w-[1000px] h-[200px] top-1/2 -translate-y-1/2"
            viewBox="0 0 1000 200"
            preserveAspectRatio="none"
          >
            <path
              d="M0,100 C150,20 350,170 500,100 C650,30 850,160 1000,100 L1000,200 L0,200 Z"
              className="fill-team-primary dark:fill-zinc-700"
            />
            <path
              d="M0,100 C150,40 350,130 500,80 C650,30 850,120 1000,80 L1000,200 L0,200 Z"
              className="fill-gray-300 dark:fill-zinc-800 opacity-50 translate-y-1"
            />
            <path
              d="M0,100 C150,70 250,60 500,120 C750,180 850,120 1000,100 L1000,200 L0,200 Z"
              className="fill-gray-200 dark:fill-zinc-800 opacity-30 translate-y-2"
            />
          </svg>
        </div>

        {/* Simplified Footer - Only legal links and credits */}
        <div className="flex flex-col items-center space-y-4 relative z-10">
          <div className="flex flex-row gap-4 text-xs">
            <Link
              to="/impressum"
              className="text-gray-500 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
            >
              Impressum
            </Link>
            <span className="text-gray-300 dark:text-gray-600">•</span>
            <Link
              to="/datenschutz"
              className="text-gray-500 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
            >
              Datenschutz
            </Link>
          </div>

          <p className="text-xs text-gray-500 dark:text-gray-400">
            © {new Date().getFullYear()} INTER NRW
          </p>

          <a
            href="https://ckrdigital.de"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 group"
          >
            <span>Made with</span>
            <Heart className="h-3 w-3 text-yellow-500 fill-yellow-500 group-hover:animate-pulse" />
            <span>by ckrdigital</span>
          </a>
        </div>

        {/* Decorative Elements */}
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-team-primary to-transparent opacity-20 z-10"></div>
      </footer>
    );
  }

  // Regular footer with wave design for logged-in users on non-admin pages
  return (
    <footer
      className={cn(
        "relative px-6 py-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-zinc-900 dark:to-zinc-950 border-t border-gray-200 dark:border-zinc-800 w-full mt-8 overflow-hidden",
        className
      )}
    >
      {/* Wave Background */}
      <div className="absolute inset-0 z-0 opacity-[0.03] dark:opacity-[0.05] overflow-hidden pointer-events-none">
        <svg
          className="absolute w-full min-w-[1000px] h-[200px] top-1/2 -translate-y-1/2"
          viewBox="0 0 1000 200"
          preserveAspectRatio="none"
        >
          <path
            d="M0,100 C150,20 350,170 500,100 C650,30 850,160 1000,100 L1000,200 L0,200 Z"
            className="fill-team-primary dark:fill-zinc-700"
          />
          <path
            d="M0,100 C150,40 350,130 500,80 C650,30 850,120 1000,80 L1000,200 L0,200 Z"
            className="fill-gray-300 dark:fill-zinc-800 opacity-50 translate-y-1"
          />
          <path
            d="M0,100 C150,70 250,60 500,120 C750,180 850,120 1000,100 L1000,200 L0,200 Z"
            className="fill-gray-200 dark:fill-zinc-800 opacity-30 translate-y-2"
          />
        </svg>
      </div>

      {/* Desktop version - only visible on md and up */}
      <div className="hidden md:block max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-3 gap-8">
          {/* About Column */}
          <div className="flex flex-col space-y-4">
            <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">INTER NRW</h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 max-w-md">
              Weniger WhatsApp. Mehr Fußball. Die Plattform für die Organisation des wöchentlichen
              Fußballspiels.
            </p>
            <div className="flex items-center space-x-3 mt-2">
              <a
                href="https://instagram.com/internrw"
                target="_blank"
                rel="noopener noreferrer"
                className="p-1.5 rounded-full bg-gray-200 dark:bg-zinc-800 text-gray-700 dark:text-gray-300 hover:bg-team-primary hover:text-white dark:hover:bg-team-primary transition-all"
                aria-label="Instagram"
              >
                <Instagram className="h-3.5 w-3.5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="p-1.5 rounded-full bg-gray-200 dark:bg-zinc-800 text-gray-700 dark:text-gray-300 hover:bg-team-primary hover:text-white dark:hover:bg-team-primary transition-all"
                aria-label="Email"
              >
                <Mail className="h-3.5 w-3.5" />
              </a>
            </div>
          </div>

          {/* Team Column */}
          <div className="flex flex-col space-y-4">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-900 dark:text-gray-100">
              Team
            </h3>
            <ul className="space-y-1.5">
              <li>
                <Link
                  to="/match-rules"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  Spielregeln
                </Link>
              </li>
              <li>
                <Link
                  to="/history"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  Spielhistorie
                </Link>
              </li>
              <li>
                <Link
                  to="/mvp-history"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  MVP Historie
                </Link>
              </li>
              <li>
                <Link
                  to="/feedback"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  Feedback
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Column */}
          <div className="flex flex-col space-y-4">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-900 dark:text-gray-100">
              Kontakt & Legal
            </h3>
            <ul className="space-y-1.5">
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  <EMAIL>
                </a>
              </li>
              <li>
                <Link
                  to="/impressum"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  Impressum
                </Link>
              </li>
              <li>
                <Link
                  to="/datenschutz"
                  className="text-xs text-gray-600 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
                >
                  Datenschutz
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright and Credits Row - Desktop */}
        <div className="mt-8 pt-4 border-t border-gray-200 dark:border-zinc-800 flex flex-row justify-between items-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            © {new Date().getFullYear()} INTER NRW Football Group
          </p>

          <a
            href="https://ckrdigital.de"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors group"
          >
            <span>Made with</span>
            <Heart className="h-3 w-3 text-yellow-500 fill-yellow-500 group-hover:animate-pulse" />
            <span>by ckrdigital</span>
            <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
          </a>
        </div>
      </div>

      {/* Mobile version - only visible on sm and below */}
      <div className="md:hidden flex flex-col items-center space-y-4 relative z-10">
        <div className="flex flex-row gap-4 text-xs">
          <Link
            to="/impressum"
            className="text-gray-500 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
          >
            Impressum
          </Link>
          <span className="text-gray-300 dark:text-gray-600">•</span>
          <Link
            to="/datenschutz"
            className="text-gray-500 dark:text-gray-400 hover:text-team-primary dark:hover:text-team-primary transition-colors"
          >
            Datenschutz
          </Link>
        </div>

        <p className="text-xs text-gray-500 dark:text-gray-400">
          © {new Date().getFullYear()} INTER NRW
        </p>

        <a
          href="https://ckrdigital.de"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 group"
        >
          <span>Made with</span>
          <Heart className="h-3 w-3 text-yellow-500 fill-yellow-500 group-hover:animate-pulse" />
          <span>by ckrdigital</span>
        </a>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-team-primary to-transparent opacity-20 z-10"></div>
    </footer>
  );
}
