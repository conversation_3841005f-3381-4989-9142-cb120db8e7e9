import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

/**
 * Component displayed when no game is scheduled
 * Shows a message that no active game is planned
 */
export function NoGameScheduled() {
  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <CardTitle>Nächstes Spiel</CardTitle>
        <CardDescription>Informationen zum nächsten geplanten Spiel</CardDescription>
        <Separator className="dark:bg-zinc-700" />
      </CardHeader>
      <CardContent className="p-6 text-center">
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
            Kein aktives Spiel geplant
          </h3>
          <p className="text-gray-500 dark:text-gray-400 max-w-md">
            Es ist derzeit kein Spiel geplant oder das nächste Spiel wurde noch nicht festgelegt.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
