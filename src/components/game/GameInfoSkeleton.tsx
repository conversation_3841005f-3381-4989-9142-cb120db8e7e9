import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import LoadingSpinner from "@/components/LoadingSpinner";

/**
 * Loading state component for GameInfo
 * Displayed while game session data is being fetched
 */
export function GameInfoSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Nächstes Spiel</CardTitle>
        <CardDescription>Informationen zum nächsten geplanten Spiel</CardDescription>
        <Separator />
      </CardHeader>
      <CardContent className="py-6 flex justify-center">
        <div className="flex flex-col items-center justify-center">
          <LoadingSpinner size="md" />
          <p className="text-gray-500 mt-4">Lade Spielinformationen...</p>
        </div>
      </CardContent>
    </Card>
  );
}
