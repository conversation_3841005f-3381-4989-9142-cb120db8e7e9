import React, { useMemo, useCallback } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Link } from "react-router-dom";
import { AlertCircle, Trophy } from "lucide-react";
import { cn } from "@/lib/utils";
import { Player } from "@/types";
import { getNextFriday } from "@/utils/dateUtils";
import { GameInformation } from "./GameInformation";
import { GameStats } from "./GameStats";
import { useTeamSettings } from "@/hooks/useTeamSettings";
import { CreditCard } from "lucide-react";

interface GameInfoCardProps {
  currentSession: any;
  players: Player[];
  timeToGame: string;
  playersLoading: boolean;
  durationLoading: boolean;
  displayDuration: string;
}

/**
 * Main card component for displaying game information
 * Contains all the game details and player statistics
 */
export function GameInfoCard({
  currentSession,
  players,
  timeToGame,
  playersLoading,
  durationLoading,
  displayDuration,
}: GameInfoCardProps) {
  // Next game date
  const nextGameDate = useMemo(() => {
    return currentSession?.date || getNextFriday();
  }, [currentSession?.date]);

  // Signup status
  const signupOpen = useMemo(() => {
    return currentSession?.isSignupOpen || false;
  }, [currentSession?.isSignupOpen]);

  // Calculate number of players with "in" status
  const inPlayersCount = useMemo(() => {
    return players.filter((p) => p.status === "in").length;
  }, [players]);

  // Calculate if we have enough players
  const hasEnoughPlayers = inPlayersCount >= 10;

  // Calculate max players based on player count
  const getMaxPlayersForGameFormat = useCallback(() => {
    if (inPlayersCount <= 14) return 10;
    if (inPlayersCount <= 19) return 15;
    return 20;
  }, [inPlayersCount]);

  const maxPlayers = getMaxPlayersForGameFormat();
  const reservePlayers = Math.max(0, inPlayersCount - maxPlayers);

  // Fetch team settings for costs
  const {
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    loading: settingsLoading,
    payPalMeLink,
  } = useTeamSettings();

  // Determine cost per player based on confirmed players
  const costPerPlayer = useMemo(() => {
    if (settingsLoading) return null;
    if (inPlayersCount >= 20) return costPerPlayer4Teams;
    if (inPlayersCount >= 15) return costPerPlayer3Teams;
    if (inPlayersCount >= 10) return costPerPlayer2Teams;
    return null; // Not enough players or settings not loaded
  }, [
    inPlayersCount,
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    settingsLoading,
  ]);

  // Format cost for display and URL (e.g., 8.1 -> 8.10)
  const formattedCost = useMemo(() => {
    return costPerPlayer !== null ? costPerPlayer.toFixed(2) : null;
  }, [costPerPlayer]);

  // Construct PayPal link
  const payPalLink = useMemo(() => {
    if (!formattedCost || !payPalMeLink) return null;
    // Replace comma with dot if locale uses comma for decimals
    const amountForUrl = formattedCost.replace(",", ".");
    // Use dynamic link and ensure it ends with a slash
    const baseUrl = payPalMeLink.endsWith("/") ? payPalMeLink : `${payPalMeLink}/`;
    return `${baseUrl}${amountForUrl}EUR`;
  }, [formattedCost, payPalMeLink]);

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle icon={<Trophy className="w-5 h-5" />}>Nächstes Spiel</CardTitle>
            <CardDescription>Informationen zum nächsten Spiel</CardDescription>
          </div>
          <Badge
            className={cn(
              "text-white text-center whitespace-normal",
              signupOpen ? "bg-blue-500 hover:bg-blue-600" : "bg-rose-500 hover:bg-rose-600"
            )}
          >
            {signupOpen ? "Anmeldung geöffnet" : "Anmeldung geschlossen"}
          </Badge>
        </div>
        <Separator />
      </CardHeader>

      <CardContent className="space-y-4 pt-3">
        {/* Game Information Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-300">
            Allgemeine Spielinformationen:
          </h3>
          <GameInformation
            nextGameDate={nextGameDate}
            timeToGame={timeToGame}
            displayDuration={displayDuration}
          />
        </div>

        {/* Player Status */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-300">Spielerstatus:</h3>
          <GameStats
            inPlayersCount={inPlayersCount}
            hasEnoughPlayers={hasEnoughPlayers}
            reservePlayers={reservePlayers}
            loading={playersLoading}
          />
        </div>

        {/* PayPal Link Section (Conditionally Rendered) */}
        {!signupOpen && hasEnoughPlayers && payPalLink && (
          <div className="border-t pt-4">
            <a
              href={payPalLink}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-green-50 dark:bg-green-900/30 hover:bg-green-100 dark:hover:bg-green-800/40 transition-colors p-3 rounded-lg w-full"
            >
              <div className="bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full shadow-sm">
                <CreditCard className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-semibold dark:text-white">
                  Platzgebühr bezahlen ({formattedCost} €)
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Jetzt die Kosten für das Spielfeld via PayPal begleichen.
                </p>
              </div>
            </a>
          </div>
        )}

        {/* Spielregeln */}
        <div className="border-t pt-4">
          <Link
            to="/match-rules"
            className="inline-flex items-center gap-2 bg-gray-50 dark:bg-zinc-800 hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors p-3 rounded-lg w-full"
          >
            <div className="bg-amber-100 p-1.5 rounded-full shadow-sm">
              <AlertCircle className="h-5 w-5 text-amber-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-semibold dark:text-white">Spielregeln</p>
              <p className="text-xs text-gray-500">
                Hier finden Sie alle Regeln und Informationen zum Spielablauf
              </p>
            </div>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
