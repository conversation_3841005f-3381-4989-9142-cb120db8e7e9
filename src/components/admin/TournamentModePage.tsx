import { useState } from "react";
import { AdminPanelLayout } from "./AdminPanelLayout";
import { useGameSessions } from "@/hooks/useGameSessions";
import { useTournament } from "@/hooks/useTournament";
import { TournamentConfig, TournamentMatch } from "@/types/tournament";
import { TournamentConfigForm } from "./tournament/TournamentConfig";
import { TournamentSchedule } from "./tournament/TournamentSchedule";
import { TournamentStandings } from "./tournament/TournamentStandings";
import { TournamentCurrentGame } from "./tournament/TournamentCurrentGame";
import { ResultsEntry } from "./tournament/ResultsEntry";
import { MultiResultsEntry } from "./tournament/MultiResultsEntry";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import {
  AlertCircle,
  Calendar,
  Trophy,
  Timer,
  Users,
  Goal,
  Hourglass,
  Play,
  Settings,
} from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { TeamDisplay } from "@/components/history/TeamDisplay";
import { syncTournamentResultsToMatchResults } from "@/services/tournamentResultsService";
import { Badge } from "../ui/badge";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";

export default function TournamentModePage() {
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [activeTab, setActiveTab] = useState("config");
  const [selectedMatch, setSelectedMatch] = useState<TournamentMatch | null>(null);
  const [isResultDialogOpen, setIsResultDialogOpen] = useState(false);
  const [isMultiResultDialogOpen, setIsMultiResultDialogOpen] = useState(false);
  const [isEndTournamentDialogOpen, setIsEndTournamentDialogOpen] = useState(false);
  const [isDeleteMatchDialogOpen, setIsDeleteMatchDialogOpen] = useState(false);
  const [matchToDelete, setMatchToDelete] = useState<string | null>(null);

  // Get current game session
  const { currentSession, loading: sessionLoading } = useGameSessions({
    fetchCurrent: true,
  });

  // Tournament state
  const {
    tournament,
    teams,
    teamsLoading,
    loading: tournamentLoading,
    createTournament,
    updateMatchResult,
    endTournament,
    startMatch,
    startMultipleMatches,
    addNextMatch,
    deleteMatch,
  } = useTournament({
    gameSessionId: currentSession?.id,
  });

  // Calculate total possible matches in a round-robin tournament
  const calculateTotalPossibleMatches = (numberOfTeams: number) => {
    return (numberOfTeams * (numberOfTeams - 1)) / 2;
  };

  // Handle tournament creation
  const handleCreateTournament = async (config: TournamentConfig) => {
    const newTournament = await createTournament(config);
    if (newTournament) {
      // Calculate total possible matches in a round-robin tournament
      const totalPossibleMatches = calculateTotalPossibleMatches(config.numberOfTeams);

      // Immediately set active tab to schedule
      setActiveTab("schedule");

      // Show toast notification
      toast({
        title: "Turnier erstellt",
        description: `Turnierplan mit ${config.numberOfTeams} Teams und ${newTournament.matches.length} Spielen erstellt.
                     ${
                       newTournament.matches.length < totalPossibleMatches
                         ? `(${totalPossibleMatches - newTournament.matches.length} Spiele können nach und nach hinzugefügt werden)`
                         : "Alle möglichen Spiele wurden eingeplant."
                     }`,
      });
    }
  };

  // Handle adding next match
  const handleAddNextMatch = async () => {
    await addNextMatch();
  };

  // Handle deleting a match
  const handleDeleteMatch = (matchId: string) => {
    setMatchToDelete(matchId);
    setIsDeleteMatchDialogOpen(true);
  };

  // Confirm match deletion
  const confirmDeleteMatch = () => {
    if (matchToDelete) {
      deleteMatch(matchToDelete);
      setMatchToDelete(null);

      toast({
        title: "Spiel gelöscht",
        description: "Das Spiel wurde erfolgreich aus dem Turnierplan entfernt.",
      });
    }
  };

  // Handle match selection for timer
  const handleSelectMatch = async (matchId: string) => {
    await startMatch(matchId);
    setActiveTab("timer");

    toast({
      title: "Spiel ausgewählt",
      description:
        "Das Spiel wurde für den Timer ausgewählt. Klicke auf Start, um den Timer zu starten.",
    });
  };

  // Handle multiple match selection for timer
  const handleSelectMultipleMatches = async (matchIds: string[]) => {
    if (!matchIds.length) return;

    const firstMatchId = await startMultipleMatches(matchIds);
    if (firstMatchId) {
      setActiveTab("timer");

      toast({
        title: "Spiele gestartet",
        description: `${matchIds.length} Spiele wurden gleichzeitig gestartet.`,
      });

      return firstMatchId;
    }
  };

  // Handle match end
  const handleMatchEnd = (match?: TournamentMatch) => {
    if (!tournament) return;

    // If a specific match is provided, use it; otherwise use the current match
    if (match) {
      setSelectedMatch(match);
      setIsResultDialogOpen(true);
      return;
    }

    if (tournament.currentMatchIndex < 0) return;

    const currentMatch = tournament.matches[tournament.currentMatchIndex];
    setSelectedMatch(currentMatch);
    setIsResultDialogOpen(true);
  };

  // Handle match result entry
  const handleEnterResult = (match: TournamentMatch) => {
    setSelectedMatch(match);
    setIsResultDialogOpen(true);
  };

  // Handle result save for a single match
  const handleSaveResult = (matchId: string, goalsA: number, goalsB: number) => {
    updateMatchResult(matchId, goalsA, goalsB);

    toast({
      title: "Ergebnis gespeichert",
      description: `Spielergebnis ${goalsA}:${goalsB} wurde gespeichert.`,
    });

    // Bleibe im aktuellen Tab, damit andere aktive Spiele weiterhin beendet werden können
  };

  // Handle saving multiple results at once
  const handleSaveMultipleResults = (
    results: { matchId: string; goalsA: number; goalsB: number }[]
  ) => {
    results.forEach((result) => {
      updateMatchResult(result.matchId, result.goalsA, result.goalsB);
    });

    toast({
      title: "Ergebnisse gespeichert",
      description: `${results.length} Spielergebnisse wurden erfolgreich gespeichert.`,
    });
  };

  // This function is no longer used as each match timer now handles its own time up event

  // Get current match (primary selected match)
  const currentMatch =
    tournament && tournament.currentMatchIndex >= 0
      ? tournament.matches[tournament.currentMatchIndex]
      : null;

  // Get all active matches (matches that have been started but not completed)
  const activeMatches = tournament
    ? tournament.matches.filter((match) => match.startTime && !match.endTime && !match.isCompleted)
    : [];

  // Get recently completed matches (completed in the current session)
  const recentlyCompletedMatches = tournament
    ? tournament.matches.filter((match) => match.startTime && match.isCompleted && match.endTime)
    : [];

  // Check if we have enough teams
  const hasEnoughTeams = teams.length >= 3;

  // Loading state
  if (sessionLoading || teamsLoading) {
    return (
      <AdminPanelLayout
        isLoggedIn={isLoggedIn}
        isLoading={false}
        onLoginSuccess={() => setIsLoggedIn(true)}
      >
        <div className="flex justify-center items-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </AdminPanelLayout>
    );
  }

  return (
    <AdminPanelLayout
      isLoggedIn={isLoggedIn}
      isLoading={false}
      onLoginSuccess={() => setIsLoggedIn(true)}
    >
      <div className="space-y-6">
        {!currentSession && (
          <Alert variant="destructive" className="border-red-200 dark:border-red-900/30">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Keine aktive Spielsitzung</AlertTitle>
            <AlertDescription>
              Es gibt keine aktive Spielsitzung. Bitte erstelle zuerst eine Spielsitzung im Bereich
              "Spielplanung".
            </AlertDescription>
          </Alert>
        )}

        {currentSession && !hasEnoughTeams && (
          <Alert className="border-amber-200 bg-amber-50/50 text-amber-800 dark:border-amber-900/30 dark:bg-amber-900/10 dark:text-amber-300">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Nicht genügend Teams</AlertTitle>
            <AlertDescription>
              Es müssen mindestens 3 Teams generiert werden, um ein Turnier zu erstellen. Bitte
              generiere Teams im Bereich "Spielplanung".
            </AlertDescription>
          </Alert>
        )}

        {currentSession && hasEnoughTeams && (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              {/* Mobile-optimized tabs with consistent styling */}
              <div className="w-full sm:w-auto">
                {/* Mobile tabs */}
                <div className="sm:hidden space-y-2">
                  <TabsList className="grid grid-cols-2 gap-2 w-full bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                    <TabsTrigger
                      value="config"
                      className="flex items-center justify-center gap-1 text-xs h-8 px-2 rounded-md -mt-[1px] data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      <span>Konfig</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="schedule"
                      className="flex items-center justify-center gap-1 text-xs h-8 px-2 rounded-md -mt-[1px] data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                      disabled={!tournament}
                    >
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>Plan</span>
                    </TabsTrigger>
                  </TabsList>
                  <TabsList className="grid grid-cols-2 gap-2 w-full bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                    <TabsTrigger
                      value="timer"
                      className="flex items-center justify-center gap-1 text-xs h-8 px-2 rounded-md -mt-[1px] data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                      disabled={!tournament}
                    >
                      <Play className="h-3 w-3 mr-1" />
                      <span>Spiel</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="standings"
                      className="flex items-center justify-center gap-1 text-xs h-8 px-2 rounded-md -mt-[1px] data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                      disabled={!tournament}
                    >
                      <Trophy className="h-3 w-3 mr-1" />
                      <span>Tabelle</span>
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Desktop tabs */}
                <TabsList className="hidden sm:flex sm:flex-row bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                  <TabsTrigger
                    value="config"
                    className="flex items-center justify-center gap-1 text-sm px-3 h-9 rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    <span>Allgemein</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="schedule"
                    className="flex items-center justify-center gap-1 text-sm px-3 h-9 rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                    disabled={!tournament}
                  >
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Spielplan</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="timer"
                    className="flex items-center justify-center gap-1 text-sm px-3 h-9 rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                    disabled={!tournament}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    <span>Aktuelles Spiel</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="standings"
                    className="flex items-center justify-center gap-1 text-sm px-3 h-9 rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400"
                    disabled={!tournament}
                  >
                    <Trophy className="h-4 w-4 mr-1" />
                    <span>Tabelle</span>
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            <TabsContent value="config" className="space-y-4">
              {!tournament ? (
                <TournamentConfigForm
                  teams={teams}
                  onCreateTournament={handleCreateTournament}
                  currentSession={currentSession}
                />
              ) : (
                <Card>
                  <CardHeader>
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                      <div>
                        <CardTitle>Turnierkonfiguration</CardTitle>
                        <CardDescription>
                          Es wurde bereits ein Turnier für diese Spielsitzung erstellt.
                        </CardDescription>
                      </div>
                      <Badge
                        variant="outline"
                        className={`${!tournament.isActive && tournament.endTime ? "bg-green-50 text-green-700 border-green-200" : "bg-blue-50 text-blue-700 border-blue-200"} px-3 py-1.5`}
                      >
                        {!tournament.isActive && tournament.endTime ? (
                          <>
                            <Trophy className="h-4 w-4 mr-1" />
                            Turnier beendet
                          </>
                        ) : (
                          <>
                            <Calendar className="h-4 w-4 mr-1" />
                            Turnier aktiv
                          </>
                        )}
                      </Badge>
                    </div>
                    <Separator className="mt-3" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-4">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-300">
                          Turnierübersicht:
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {/* Teams */}
                          <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                            <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm">
                              <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold dark:text-white">Teams:</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {tournament.config.numberOfTeams} Teams
                              </p>
                            </div>
                          </div>

                          {/* Total Matches */}
                          <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                            <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm">
                              <Goal className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold dark:text-white">
                                Spiele insgesamt:
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {tournament.matches.length} Spiele
                              </p>
                            </div>
                          </div>

                          {/* Match Duration */}
                          <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                            <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full shadow-sm">
                              <Timer className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold dark:text-white">Spieldauer:</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {tournament.config.matchDurationMinutes} Minuten pro Spiel
                              </p>
                            </div>
                          </div>

                          {/* Break Duration */}
                          <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                            <div className="bg-team-primary/20 dark:bg-gray-900/40 p-1.5 rounded-full shadow-sm">
                              <Hourglass className="h-5 w-5 text-team-primary dark:text-gray-400/90" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold dark:text-white">
                                Pause zwischen Spielen:
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {tournament.config.breakBetweenMatchesMinutes} Minuten
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button
                          onClick={() => setActiveTab("schedule")}
                          className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Zum Spielplan
                        </Button>

                        {tournament.isActive && (
                          <Button
                            variant="destructive"
                            onClick={() => setIsEndTournamentDialogOpen(true)}
                            className="w-full"
                          >
                            <Trophy className="h-4 w-4 mr-2" />
                            Turnier beenden
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <div>
                    <CardTitle>Verfügbare Teams</CardTitle>
                    <CardDescription>Teams für das Turnier</CardDescription>
                  </div>
                  <Separator className="mt-3" />
                </CardHeader>
                <CardContent>
                  <TeamDisplay
                    teams={teams}
                    isAdminView={true}
                    sessionId={currentSession?.id || ""}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="schedule" className="space-y-4">
              {tournament && (
                <TournamentSchedule
                  tournament={tournament}
                  onStartMatch={handleSelectMatch}
                  onStartMultipleMatches={handleSelectMultipleMatches}
                  onEnterResult={handleEnterResult}
                  onAddNextMatch={handleAddNextMatch}
                  onDeleteMatch={handleDeleteMatch}
                  currentMatchId={currentMatch?.id}
                  totalPossibleMatches={calculateTotalPossibleMatches(
                    tournament.config.numberOfTeams
                  )}
                  isTournamentEnded={!tournament.isActive && tournament.endTime !== undefined}
                />
              )}
            </TabsContent>

            <TabsContent value="timer" className="space-y-4">
              <TournamentCurrentGame
                tournament={tournament}
                tournamentLoading={tournamentLoading}
                activeMatches={activeMatches}
                recentlyCompletedMatches={recentlyCompletedMatches}
                onMatchEnd={handleMatchEnd}
                onMultipleResultsOpen={() => setIsMultiResultDialogOpen(true)}
                onTabChange={setActiveTab}
              />
            </TabsContent>

            <TabsContent value="standings" className="space-y-4">
              {tournament && <TournamentStandings standings={tournament.standings} />}
            </TabsContent>
          </Tabs>
        )}
      </div>

      {/* Single Result Entry Dialog */}
      <ResultsEntry
        match={selectedMatch}
        isOpen={isResultDialogOpen}
        onClose={() => setIsResultDialogOpen(false)}
        onSave={handleSaveResult}
      />

      {/* Multiple Results Entry Dialog */}
      {isMultiResultDialogOpen && (
        <MultiResultsEntry
          matches={activeMatches}
          isOpen={isMultiResultDialogOpen}
          onClose={() => setIsMultiResultDialogOpen(false)}
          onSave={handleSaveMultipleResults}
        />
      )}

      {/* End Tournament Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isEndTournamentDialogOpen}
        onClose={() => setIsEndTournamentDialogOpen(false)}
        onConfirm={async () => {
          // End the tournament without syncing results again (they're already synced by updateMatchResult)
          endTournament();

          // Show success message
          toast({
            title: "Turnier beendet",
            description:
              "Das Turnier wurde erfolgreich beendet. Es können keine weiteren Änderungen vorgenommen werden.",
          });
        }}
        title="Turnier beenden"
        description="Möchtest du das Turnier wirklich beenden? Das Turnier kann danach nicht mehr bearbeitet werden."
        confirmText="Turnier beenden"
        cancelText="Abbrechen"
        variant="destructive"
      />

      {/* Delete Match Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteMatchDialogOpen}
        onClose={() => setIsDeleteMatchDialogOpen(false)}
        onConfirm={confirmDeleteMatch}
        title="Spiel löschen"
        description="Möchtest du dieses Spiel wirklich aus dem Turnierplan entfernen? Diese Aktion kann nicht rückgängig gemacht werden."
        confirmText="Spiel löschen"
        cancelText="Abbrechen"
        variant="destructive"
      />
    </AdminPanelLayout>
  );
}
