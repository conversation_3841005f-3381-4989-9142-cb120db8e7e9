import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface AdminTopbarProps {
  title: string;
  toggleSidebar: () => void;
  isSidebarOpen?: boolean;
}

export default function AdminTopbar({
  title,
  toggleSidebar,
  isSidebarOpen = true,
}: AdminTopbarProps) {
  return (
    <div
      className={`fixed top-0 right-0 h-16 bg-white dark:bg-zinc-900 border-b dark:border-zinc-800 shadow-sm z-40 transition-all duration-300 w-full ${
        isSidebarOpen ? "lg:w-[calc(100%-16rem)] lg:left-64" : "lg:w-full lg:left-0"
      }`}
    >
      <div className="flex items-center justify-between h-full px-4">
        <div>
          <Button variant="ghost" size="icon" onClick={toggleSidebar}>
            <Menu className="h-5 w-5 dark:text-gray-300" />
          </Button>
        </div>

        <h1 className="text-xl font-semibold dark:text-white absolute left-1/2 transform -translate-x-1/2">
          {title}
        </h1>

        <div className="w-10">{/* Leerer Div für gleichmäßiges Layout */}</div>
      </div>
    </div>
  );
}
