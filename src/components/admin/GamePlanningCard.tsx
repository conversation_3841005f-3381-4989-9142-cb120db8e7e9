import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Calendar as CalendarIcon,
  Clock,
  Users,
  CheckCircle,
  Settings,
  CalendarDays,
  Lock,
  Unlock,
  Timer,
  X,
  AlertCircle,
  ChevronRight,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { GameSession } from "@/types";
import LoadingSpinner from "@/components/LoadingSpinner";
import { formatDate } from "@/utils/dateUtils";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface GamePlanningCardProps {
  currentSession: GameSession | null;
  gameDate: Date;
  gameTime: string;
  gameFormation: string;
  canGenerateTeams: boolean;
  showMarkAsPlayedButton: boolean;
  isGeneratingTeams: boolean;
  isMarkingAsPlayed: boolean;
  isCancelingMatch: boolean;
  isPendingTeamApproval?: boolean;
  isSignupOpen: boolean;
  onDateChange: (date: Date | undefined) => void;
  onTimeChange: (time: string) => void;
  onSave: () => void;
  onToggleSignup: () => void;
  onGenerateTeams: () => void;
  onMarkAsPlayed: () => void;
  onCancelMatch: () => void;
}

function GamePlanningSection({
  gameDate,
  gameTime,
  currentSession,
  onDateChange,
  onTimeChange,
  onSave,
}: {
  gameDate: Date;
  gameTime: string;
  currentSession: GameSession | null;
  onDateChange: (date: Date | undefined) => void;
  onTimeChange: (time: string) => void;
  onSave: () => void;
}) {
  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <div className="space-y-1">
          <CardTitle className="dark:text-white flex items-center gap-2">Spielplanung</CardTitle>
          <CardDescription className="dark:text-zinc-400">
            Legen Sie Datum und Uhrzeit für das nächste Spiel fest
          </CardDescription>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>
      <CardContent className="pt-3 space-y-6">
        <div className="grid gap-6 sm:grid-cols-2">
          <div className="space-y-2">
            <label className="text-sm font-medium dark:text-zinc-300">Spieldatum</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal dark:bg-zinc-800/50 dark:border-zinc-700/50 dark:text-zinc-300 dark:hover:bg-zinc-800",
                    !gameDate && "text-muted-foreground dark:text-zinc-400"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {gameDate ? (
                    format(gameDate, "dd.MM.yyyy", { locale: de })
                  ) : (
                    <span>Datum wählen</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 dark:bg-zinc-800 dark:border-zinc-700"
                align="start"
              >
                <CalendarComponent
                  mode="single"
                  selected={gameDate}
                  onSelect={onDateChange}
                  disabled={(date) => date < new Date()}
                  initialFocus
                  locale={de}
                  className="dark:bg-zinc-800"
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium dark:text-zinc-300">Spielzeit</label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-zinc-500 dark:text-zinc-400" />
              <Input
                type="time"
                value={gameTime}
                onChange={(e) => onTimeChange(e.target.value)}
                className="pl-10 dark:bg-zinc-800/50 dark:border-zinc-700/50 dark:text-zinc-300 dark:hover:bg-zinc-800"
              />
            </div>
          </div>
        </div>

        <Button
          onClick={onSave}
          className="w-full bg-team-primary text-white hover:bg-team-primary/90 dark:bg-team-primary/80 dark:hover:bg-team-primary h-12 text-base"
        >
          {currentSession?.status === "scheduled" ? (
            <>
              <Settings className="mr-2 h-5 w-5" />
              Spielplan aktualisieren
            </>
          ) : (
            <>
              <CalendarDays className="mr-2 h-5 w-5" />
              Spielplan erstellen
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

function GameStatusSection({
  currentSession,
  gameFormation,
  canGenerateTeams,
  showMarkAsPlayedButton,
  isGeneratingTeams,
  isMarkingAsPlayed,
  isCancelingMatch,
  isPendingTeamApproval = false,
  isSignupOpen,
  onToggleSignup,
  onGenerateTeams,
  onMarkAsPlayed,
  onCancelMatch,
}: Omit<
  GamePlanningCardProps,
  "gameDate" | "gameTime" | "onDateChange" | "onTimeChange" | "onSave"
>) {
  if (!currentSession?.status) {
    return (
      <Card className="dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle className="dark:text-white flex items-center gap-2">
              Kein aktives Spiel
            </CardTitle>
            <CardDescription className="dark:text-zinc-400">
              Bitte erstellen Sie ein neues Spiel, um mit der Planung zu beginnen
            </CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3 space-y-4">
          <div className="text-center text-muted-foreground">
            Verwenden Sie die Spielplanung oben, um ein neues Spiel zu erstellen
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <div className="space-y-1">
          <CardTitle className="dark:text-white">Spielstatus</CardTitle>
          <CardDescription className="dark:text-zinc-400">
            Verwalten Sie den Spielstatus und die Teamformation
          </CardDescription>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-4 sm:grid-cols-2">
          <StatusCard
            icon={CalendarIcon}
            title="Datum & Zeit"
            value={formatDate(currentSession.date)}
          />
          <StatusCard
            icon={isSignupOpen ? Unlock : Lock}
            title="Anmeldestatus"
            value={isSignupOpen ? "Geöffnet" : "Geschlossen"}
            variant={isSignupOpen ? "success" : "warning"}
          />
          <StatusCard icon={Users} title="Formation" value={gameFormation} />
          <StatusCard
            icon={Timer}
            title="Spieldauer"
            value={`${currentSession.duration_minutes || 90} Minuten`}
          />
        </div>

        {currentSession.isTeamGenerated && (
          <div className="flex items-center gap-3 rounded-lg border border-green-200 bg-green-50 p-4 dark:bg-green-950/30 dark:border-green-800">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500/10 dark:bg-green-500/20">
              <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-green-900 dark:text-green-400">Teams generiert</p>
              {isPendingTeamApproval && (
                <Badge
                  variant="outline"
                  className="mt-1 border-amber-200 bg-amber-50 text-amber-700 dark:bg-amber-950/30 dark:border-amber-800 dark:text-amber-400"
                >
                  Noch nicht gespeichert
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="grid gap-3">
          <ActionButton
            label={isSignupOpen ? "Anmeldung schließen" : "Anmeldung öffnen"}
            icon={isSignupOpen ? Lock : Unlock}
            onClick={onToggleSignup}
            variant={isSignupOpen ? "warning" : "success"}
            confirmText={
              isSignupOpen
                ? "Diese Aktion schließt die Anmeldung für das aktuelle Spiel. Spieler können sich danach nicht mehr anmelden oder ihren Status ändern."
                : "Diese Aktion öffnet die Anmeldung für das aktuelle Spiel. Spieler können sich dann anmelden und ihren Status ändern."
            }
            confirmButtonText={isSignupOpen ? "Ja, Anmeldung schließen" : "Ja, Anmeldung öffnen"}
          />

          {canGenerateTeams && (
            <ActionButton
              label="Teams generieren"
              icon={Users}
              onClick={onGenerateTeams}
              variant="primary"
              isLoading={isGeneratingTeams}
              loadingText="Teams werden generiert..."
              confirmText="Diese Aktion generiert automatisch Teams basierend auf den Spielerbewertungen. Die Anmeldung wird automatisch geschlossen und die Teams werden erstellt."
              confirmButtonText="Ja, Teams generieren"
            />
          )}

          {showMarkAsPlayedButton && (
            <ActionButton
              label="Als gespielt markieren"
              icon={CheckCircle}
              onClick={onMarkAsPlayed}
              variant="success"
              isLoading={isMarkingAsPlayed}
              loadingText="Wird markiert..."
              confirmText="Diese Aktion markiert das Spiel als gespielt und archiviert es. Das Spiel wird dann in der Historie verfügbar sein."
              confirmButtonText="Ja, als gespielt markieren"
            />
          )}

          <ActionButton
            label="Spiel abbrechen"
            icon={X}
            onClick={onCancelMatch}
            variant="danger"
            isLoading={isCancelingMatch}
            loadingText="Wird abgebrochen..."
            confirmText="Diese Aktion bricht das aktuelle Spiel ab und archiviert es als abgebrochen. Dies kann nicht rückgängig gemacht werden."
            confirmButtonText="Ja, Spiel abbrechen"
          />
        </div>
      </CardContent>
    </Card>
  );
}

interface StatusCardProps {
  icon: React.ElementType;
  title: string;
  value: string;
  variant?: "default" | "success" | "warning";
}

function StatusCard({ icon: Icon, title, value, variant = "default" }: StatusCardProps) {
  const variants = {
    default: "bg-zinc-100 dark:bg-zinc-800/50",
    success: "bg-green-50 dark:bg-green-950/30",
    warning: "bg-amber-50 dark:bg-amber-950/30",
  };

  const iconVariants = {
    default: "text-team-primary dark:text-gray-500/80",
    success: "text-green-500 dark:text-green-400",
    warning: "text-amber-500 dark:text-amber-400",
  };

  return (
    <div
      className={cn("flex items-center gap-3 rounded-lg p-4 transition-colors", variants[variant])}
    >
      <Icon className={cn("h-5 w-5", iconVariants[variant])} />
      <div>
        <p className="text-sm font-medium dark:text-zinc-300">{title}</p>
        <p className="text-sm text-zinc-600 dark:text-zinc-400">{value}</p>
      </div>
    </div>
  );
}

interface ActionButtonProps {
  label: string;
  icon: React.ElementType;
  onClick: () => void;
  variant: "primary" | "success" | "warning" | "danger";
  isLoading?: boolean;
  loadingText?: string;
  confirmText: string;
  confirmButtonText: string;
}

function ActionButton({
  label,
  icon: Icon,
  onClick,
  variant,
  isLoading,
  loadingText,
  confirmText,
  confirmButtonText,
}: ActionButtonProps) {
  const variants = {
    primary:
      "bg-team-primary/10 text-team-primary hover:bg-team-primary/20 dark:bg-gray-500/20 dark:text-gray-200/90 dark:hover:bg-team-primary/30",
    success:
      "bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:bg-green-500/20 dark:text-green-400 dark:hover:bg-green-500/30",
    warning:
      "bg-amber-500/10 text-amber-600 hover:bg-amber-500/20 dark:bg-amber-500/20 dark:text-amber-400 dark:hover:bg-amber-500/30",
    danger:
      "bg-red-500/10 text-red-600 hover:bg-red-500/20 dark:bg-red-500/20 dark:text-red-400 dark:hover:bg-red-500/30",
  };

  const confirmVariants = {
    primary:
      "bg-team-primary hover:bg-team-primary/90 dark:bg-team-primary dark:hover:bg-team-primary/90",
    success: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600",
    warning: "bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600",
    danger: "bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600",
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start h-12 text-base hover:bg-white dark:hover:bg-zinc-800/50",
            variants[variant]
          )}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-3" />
              <span>{loadingText}</span>
            </>
          ) : (
            <>
              <Icon className="mr-3 h-5 w-5" />
              <span>{label}</span>
              <ChevronRight className="ml-auto h-5 w-5 opacity-50" />
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
        <AlertDialogHeader>
          <AlertDialogTitle className="dark:text-white flex items-center gap-2">
            <Icon className="h-5 w-5" />
            {label}
          </AlertDialogTitle>
          <AlertDialogDescription className="dark:text-zinc-400">
            {confirmText}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
            Abbrechen
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onClick}
            className={cn("text-white", confirmVariants[variant])}
          >
            {confirmButtonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function GamePlanningCard(props: GamePlanningCardProps) {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <GamePlanningSection
        gameDate={props.gameDate}
        gameTime={props.gameTime}
        currentSession={props.currentSession}
        onDateChange={props.onDateChange}
        onTimeChange={props.onTimeChange}
        onSave={props.onSave}
      />
      <GameStatusSection {...props} />
    </div>
  );
}
