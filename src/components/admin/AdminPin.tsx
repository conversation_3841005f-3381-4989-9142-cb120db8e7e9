import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { Label } from "@/components/ui/label";
import { pinService } from "@/services/pinService";

const PIN_LENGTH = 4;

export default function AdminPin() {
  const [pin, setPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleUpdatePin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (pin.length !== PIN_LENGTH) {
      toast({
        title: "Fehler",
        description: `Der PIN muss ${PIN_LENGTH}-stellig sein`,
        variant: "destructive",
      });
      return;
    }

    if (pin !== confirmPin) {
      toast({
        title: "<PERSON><PERSON>",
        description: "Die PINs stimmen nicht überein",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      await pinService.updatePin(pin);

      toast({
        title: "Erfolg",
        description: "Der PIN wurde aktualisiert",
      });

      setPin("");
      setConfirmPin("");
    } catch (error) {
      console.error("Error updating PIN:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten";
      toast({
        title: "Fehler",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-team-primary text-white rounded-t-lg">
        <CardTitle className="text-center text-lg">App-PIN verwalten</CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <form onSubmit={handleUpdatePin} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="pin">Neuer PIN ({PIN_LENGTH} Ziffern)</Label>
            <Input
              id="pin"
              type="password"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={PIN_LENGTH}
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              placeholder="****"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPin">PIN bestätigen</Label>
            <Input
              id="confirmPin"
              type="password"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={PIN_LENGTH}
              value={confirmPin}
              onChange={(e) => setConfirmPin(e.target.value)}
              placeholder="****"
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-team-primary hover:bg-team-primary/90"
            disabled={isLoading}
          >
            {isLoading ? "Wird aktualisiert..." : "PIN aktualisieren"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
