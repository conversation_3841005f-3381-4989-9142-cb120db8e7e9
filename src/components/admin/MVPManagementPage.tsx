import { MVPVotingCard } from "./MVPVotingCard";
import { MVPHistoryCard } from "./MVPHistoryCard";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Award, Trophy } from "lucide-react";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/custom-card";
import { Separator } from "../ui/separator";

export default function MVPManagementPage() {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="voting" className="w-full">
        <TabsList className="w-full border-b rounded-none p-0 h-auto bg-transparent">
          <TabsTrigger
            value="voting"
            className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:text-blue-500 data-[state=active]:shadow-none rounded-none px-6 py-3 text-muted-foreground"
          >
            <Award className="h-4 w-4 mr-2" />
            Aktives MVP-Voting
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:text-blue-500 data-[state=active]:shadow-none rounded-none px-6 py-3 text-muted-foreground"
          >
            <Trophy className="h-4 w-4 mr-2" />
            MVP Historie
          </TabsTrigger>
        </TabsList>
        <TabsContent value="voting" className="mt-6">
          <Card>
            <CardHeader>
              <div className="space-y-1">
                <CardTitle>Aktives MVP-Voting</CardTitle>
                <CardDescription>
                  Verwalte die aktuelle MVP-Abstimmung für das letzte Spiel
                </CardDescription>
              </div>
              <Separator className="dark:bg-zinc-800" />
            </CardHeader>
            <CardContent className="pt-3">
              <MVPVotingCard showCloseButton={true} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <div className="space-y-1">
                <CardTitle>MVP Historie</CardTitle>
                <CardDescription>
                  Übersicht aller vergangenen MVP-Abstimmungen und deren Ergebnisse
                </CardDescription>
              </div>
              <Separator className="dark:bg-zinc-800" />
            </CardHeader>
            <CardContent className="pt-3">
              <MVPHistoryCard />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
