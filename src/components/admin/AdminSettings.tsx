import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { toast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import LoadingSpinner from "@/components/LoadingSpinner";
import { useTeamSettings } from "@/hooks/useTeamSettings";
import { Button } from "@/components/ui/button";
import { Settings, Clock, Users2, AlertTriangle, Euro, MessageSquare } from "lucide-react";
import { Separator } from "../ui/separator";
import { cn } from "@/lib/utils";
import { Label } from "../ui/label";

export default function AdminSettings() {
  const {
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    loading,
    saveSettings,
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    payPalMeLink,
    whatsAppNotificationsEnabled,
  } = useTeamSettings();

  // Local state for editing
  const [editAllow3Teams, setEditAllow3Teams] = useState(allow3Teams);
  const [edit2, setEdit2] = useState(duration2Teams);
  const [edit3, setEdit3] = useState(duration3Teams);
  const [edit4, setEdit4] = useState(duration4Teams);
  const [editCost2, setEditCost2] = useState(costPerPlayer2Teams);
  const [editCost3, setEditCost3] = useState(costPerPlayer3Teams);
  const [editCost4, setEditCost4] = useState(costPerPlayer4Teams);
  const [editPayPalMeLink, setEditPayPalMeLink] = useState(payPalMeLink);
  const [editWhatsAppNotificationsEnabled, setEditWhatsAppNotificationsEnabled] = useState(
    whatsAppNotificationsEnabled
  );
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    setEditAllow3Teams(allow3Teams);
    setEdit2(duration2Teams);
    setEdit3(duration3Teams);
    setEdit4(duration4Teams);
    setEditCost2(costPerPlayer2Teams);
    setEditCost3(costPerPlayer3Teams);
    setEditCost4(costPerPlayer4Teams);
    setEditPayPalMeLink(payPalMeLink);
    setEditWhatsAppNotificationsEnabled(whatsAppNotificationsEnabled);
  }, [
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    payPalMeLink,
    whatsAppNotificationsEnabled,
  ]);

  const handleSave = async () => {
    setSaving(true);
    try {
      await saveSettings({
        allow3Teams: editAllow3Teams,
        duration2Teams: edit2,
        duration3Teams: edit3,
        duration4Teams: edit4,
        costPerPlayer2Teams: editCost2,
        costPerPlayer3Teams: editCost3,
        costPerPlayer4Teams: editCost4,
        payPalMeLink: editPayPalMeLink,
        whatsAppNotificationsEnabled: editWhatsAppNotificationsEnabled,
      });
      toast({
        title: "Einstellungen gespeichert",
        description: "Die Einstellungen wurden erfolgreich aktualisiert.",
      });
    } catch (e) {
      toast({
        title: "Fehler",
        description: "Einstellungen konnten nicht gespeichert werden.",
        variant: "destructive",
      });
    }
    setSaving(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="space-y-1">
          <CardTitle className="mb-1 dark:text-white">Systemeinstellungen</CardTitle>
          <CardDescription>
            Verwalten Sie grundlegende Spieleinstellungen und Systemkonfigurationen
          </CardDescription>
        </div>
        <Separator className="dark:bg-zinc-800 mt-4" />
      </CardHeader>
      <CardContent className="pt-4 space-y-6">
        {loading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <>
            {/* Notifications Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium dark:text-zinc-300">Benachrichtigungen</h3>
              <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                <Switch
                  checked={editWhatsAppNotificationsEnabled}
                  onCheckedChange={setEditWhatsAppNotificationsEnabled}
                  id="whatsapp-notifications-switch"
                  className="data-[state=checked]:bg-team-primary dark:data-[state=checked]:bg-team-primary/80"
                />
                <div className="space-y-0.5">
                  <label
                    htmlFor="whatsapp-notifications-switch"
                    className="text-sm font-medium cursor-pointer dark:text-zinc-300"
                  >
                    WhatsApp Benachrichtigungen
                  </label>
                  <p className="text-sm text-muted-foreground dark:text-zinc-400">
                    Aktivieren Sie diese Option, um WhatsApp Benachrichtigungen für An-/Abmeldungen
                    und Spielereignisse zu senden
                  </p>
                </div>
              </div>
            </div>

            {/* Team Configuration Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium dark:text-zinc-300">Team-Konfiguration</h3>
              <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                <Switch
                  checked={editAllow3Teams}
                  onCheckedChange={setEditAllow3Teams}
                  id="allow-3-teams-switch"
                  className="data-[state=checked]:bg-team-primary dark:data-[state=checked]:bg-team-primary/80"
                />
                <div className="space-y-0.5">
                  <label
                    htmlFor="allow-3-teams-switch"
                    className="text-sm font-medium cursor-pointer dark:text-zinc-300"
                  >
                    3-Team-Spiele erlauben
                  </label>
                  <p className="text-sm text-muted-foreground dark:text-zinc-400">
                    Aktivieren Sie diese Option, um Spiele mit drei Teams zu ermöglichen
                  </p>
                </div>
              </div>
            </div>

            {/* Match Duration Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium dark:text-zinc-300">Spielzeit-Einstellungen</h3>
              <div className="grid gap-4">
                {/* 2 Teams Duration */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Users2 className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <label className="text-sm font-medium dark:text-zinc-300">2 Teams</label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Standardspielzeit für 2 Teams (5 vs 5)
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={30}
                          step={5}
                          value={edit2}
                          onChange={(e) => setEdit2(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">Minuten</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 3 Teams Duration */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Users2 className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <label className="text-sm font-medium dark:text-zinc-300">3 Teams</label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Spielzeit für 3-Team-Rotation (5 vs 5 vs 5)
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={30}
                          step={5}
                          value={edit3}
                          onChange={(e) => setEdit3(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">Minuten</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 4 Teams Duration */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Users2 className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <label className="text-sm font-medium dark:text-zinc-300">4 Teams</label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Spielzeit für 4-Team-Doppelrotation
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={30}
                          step={5}
                          value={edit4}
                          onChange={(e) => setEdit4(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">Minuten</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cost Configuration Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium dark:text-zinc-300">Kosten pro Spieler</h3>
              <div className="grid gap-4">
                {/* 2 Teams Cost */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Euro className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <Label className="text-sm font-medium dark:text-zinc-300">
                          2 Teams (10 Spieler)
                        </Label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Kosten pro Spieler bei 10 Teilnehmern
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={0}
                          step={0.1}
                          value={editCost2}
                          onChange={(e) => setEditCost2(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">€</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 3 Teams Cost */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Euro className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <Label className="text-sm font-medium dark:text-zinc-300">
                          3 Teams (15 Spieler)
                        </Label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Kosten pro Spieler bei 15 Teilnehmern
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={0}
                          step={0.1}
                          value={editCost3}
                          onChange={(e) => setEditCost3(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">€</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 4 Teams Cost */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                  <div className="flex items-start gap-4">
                    <Euro className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <div>
                        <Label className="text-sm font-medium dark:text-zinc-300">
                          4 Teams (20 Spieler)
                        </Label>
                        <p className="text-sm text-muted-foreground dark:text-zinc-400">
                          Kosten pro Spieler bei 20 Teilnehmern
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={0}
                          step={0.1}
                          value={editCost4}
                          onChange={(e) => setEditCost4(Number(e.target.value))}
                          className="w-24 dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                        />
                        <span className="text-sm dark:text-zinc-400">€</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* PayPal Link Configuration */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium dark:text-zinc-300">PayPal Link Konfiguration</h3>
              <div className="p-4 rounded-lg bg-gray-50 dark:bg-zinc-800/50 border dark:border-zinc-700">
                <div className="flex items-start gap-4">
                  <Settings className="h-5 w-5 mt-1 text-team-primary dark:text-gray-400" />
                  <div className="flex-1 space-y-2">
                    <div>
                      <Label
                        htmlFor="paypal-link-input"
                        className="text-sm font-medium dark:text-zinc-300"
                      >
                        PayPal.me Basis-Link
                      </Label>
                      <p className="text-sm text-muted-foreground dark:text-zinc-400">
                        Der Basis-Link für PayPal Zahlungen (z.B. https://www.paypal.me/DEINNAME/).
                      </p>
                    </div>
                    <Input
                      id="paypal-link-input"
                      type="text"
                      value={editPayPalMeLink}
                      onChange={(e) => setEditPayPalMeLink(e.target.value)}
                      placeholder="https://www.paypal.me/DEINNAME/"
                      className="dark:bg-zinc-900 dark:border-zinc-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div className="pt-4">
              <Button
                className={cn(
                  "w-full bg-team-primary hover:bg-team-primary/90 text-white",
                  "dark:bg-team-primary/80 dark:hover:bg-team-primary"
                )}
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Speichern...
                  </>
                ) : (
                  "Änderungen speichern"
                )}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
