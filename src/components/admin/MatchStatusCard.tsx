import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/dateUtils";
import { Clock, Users, Lock, Unlock, Check, X, Calendar, Timer } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { GameSession } from "@/types";

interface MatchStatusCardProps {
  currentSession: GameSession | null;
  gameFormation: string;
  canGenerateTeams: boolean;
  showMarkAsPlayedButton: boolean;
  isGeneratingTeams: boolean;
  isMarkingAsPlayed: boolean;
  isCancelingMatch: boolean;
  onToggleSignup: () => void;
  onGenerateTeams: () => void;
  onMarkAsPlayed: () => void;
  onCancelMatch: () => void;
  isPendingTeamApproval?: boolean;
  isSignupOpen: boolean;
}

export function MatchStatusCard({
  currentSession,
  gameFormation,
  canGenerateTeams,
  showMarkAsPlayedButton,
  isGeneratingTeams,
  isMarkingAsPlayed,
  isCancelingMatch,
  onToggleSignup,
  onGenerateTeams,
  onMarkAsPlayed,
  onCancelMatch,
  isPendingTeamApproval = false,
  isSignupOpen,
}: MatchStatusCardProps) {
  const isGameScheduled = currentSession?.status === "scheduled";

  if (!isGameScheduled) {
    return (
      <Card>
        <CardHeader className="bg-team-primary text-white rounded-t-lg">
          <CardTitle>Match Status</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-center w-full p-4 bg-gray-50 rounded-lg">
            <p className="text-gray-700">Derzeit ist kein Spiel geplant.</p>
            <p className="text-gray-600 text-sm mt-2">
              Bitte erstellen Sie unten ein neues Spiel, um mit der Planung zu beginnen.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="bg-team-primary text-white rounded-t-lg">
        <CardTitle>Match Status</CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="flex items-center gap-2 font-medium">
            <div className="w-5">
              <Calendar className="h-5 w-5 text-team-primary" />
            </div>
            <span>{formatDate(currentSession.date)}</span>
          </div>

          {currentSession?.duration_minutes && (
            <div className="flex items-center gap-2">
              <div className="w-5">
                <Timer className="h-5 w-5" />
              </div>
              <span>Spieldauer: {currentSession.duration_minutes} Minuten</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <div className="w-5">
              {isSignupOpen ? (
                <Unlock className="h-5 w-5 text-green-500" />
              ) : (
                <Lock className="h-5 w-5 text-red-500" />
              )}
            </div>
            <span>Anmeldung {isSignupOpen ? "geöffnet" : "geschlossen"}</span>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-5">
              <Users className="h-5 w-5" />
            </div>
            <span>{gameFormation}</span>
          </div>

          {/* Team Status Indicator */}
          {currentSession.isTeamGenerated && (
            <div className="flex items-center gap-2">
              <div className="w-5">
                <Check className="h-5 w-5 text-green-500" />
              </div>
              <span>Teams generiert</span>
              {isPendingTeamApproval && (
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
                  Ungespeichert
                </Badge>
              )}
            </div>
          )}

          <div className="flex flex-wrap gap-2 pt-2">
            <SignupToggleButton isOpen={isSignupOpen} onToggle={onToggleSignup} />

            {canGenerateTeams && (
              <GenerateTeamsButton isGenerating={isGeneratingTeams} onGenerate={onGenerateTeams} />
            )}

            {showMarkAsPlayedButton && (
              <MarkAsPlayedButton isMarking={isMarkingAsPlayed} onMark={onMarkAsPlayed} />
            )}

            <CancelMatchButton isCanceling={isCancelingMatch} onCancel={onCancelMatch} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function SignupToggleButton({ isOpen, onToggle }: { isOpen: boolean; onToggle: () => void }) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="outline"
          className={
            isOpen
              ? "border-red-500 text-red-500 hover:bg-red-50"
              : "border-green-500 text-green-500 hover:bg-green-50"
          }
        >
          {isOpen ? (
            <>
              <Lock className="h-4 w-4" />
              Anmeldung schließen
            </>
          ) : (
            <>
              <Unlock className="h-4 w-4" />
              Anmeldung öffnen
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isOpen ? "Anmeldung wirklich schließen?" : "Anmeldung öffnen?"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {isOpen
              ? "Diese Aktion schließt die Anmeldung für das aktuelle Spiel. Spieler können sich danach nicht mehr anmelden oder ihren Status ändern."
              : "Diese Aktion öffnet die Anmeldung für das aktuelle Spiel. Spieler können sich dann anmelden und ihren Status ändern."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Abbrechen</AlertDialogCancel>
          <AlertDialogAction onClick={onToggle}>
            {isOpen ? "Ja, Anmeldung schließen" : "Ja, Anmeldung öffnen"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function GenerateTeamsButton({
  isGenerating,
  onGenerate,
}: {
  isGenerating: boolean;
  onGenerate: () => void;
}) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="outline"
          className="border-team-primary text-team-primary hover:bg-team-primary/10"
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Teams werden generiert...
            </>
          ) : (
            <>
              <Users className="h-4 w-4" />
              Teams generieren
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Teams generieren?</AlertDialogTitle>
          <AlertDialogDescription>
            Diese Aktion generiert automatisch Teams basierend auf den Spielerbewertungen. Die
            Anmeldung wird automatisch geschlossen und die Teams werden erstellt. Dies kann nicht
            rückgängig gemacht werden.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Abbrechen</AlertDialogCancel>
          <AlertDialogAction onClick={onGenerate}>Ja, Teams generieren</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function MarkAsPlayedButton({ isMarking, onMark }: { isMarking: boolean; onMark: () => void }) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="outline"
          className="border-green-500 text-green-500 hover:bg-green-50"
          disabled={isMarking}
        >
          {isMarking ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Wird markiert...
            </>
          ) : (
            <>
              <Check className="h-4 w-4" />
              Als gespielt markieren
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Spiel als gespielt markieren?</AlertDialogTitle>
          <AlertDialogDescription>
            Diese Aktion markiert das Spiel als gespielt und archiviert es. Das Spiel wird dann in
            der Historie verfügbar sein und kann nicht mehr bearbeitet werden.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Abbrechen</AlertDialogCancel>
          <AlertDialogAction onClick={onMark}>Ja, als gespielt markieren</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function CancelMatchButton({
  isCanceling,
  onCancel,
}: {
  isCanceling: boolean;
  onCancel: () => void;
}) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="outline"
          className="border-red-500 text-red-500 hover:bg-red-50"
          disabled={isCanceling}
        >
          {isCanceling ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Wird abgebrochen...
            </>
          ) : (
            <>
              <X className="h-4 w-4" />
              Spiel abbrechen
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Spiel wirklich abbrechen?</AlertDialogTitle>
          <AlertDialogDescription>
            Diese Aktion bricht das aktuelle Spiel ab und archiviert es. Es wird als abgebrochen
            markiert und in der Historie verfügbar sein. Dies kann nicht rückgängig gemacht werden.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Abbrechen</AlertDialogCancel>
          <AlertDialogAction onClick={onCancel}>Ja, Spiel abbrechen</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
