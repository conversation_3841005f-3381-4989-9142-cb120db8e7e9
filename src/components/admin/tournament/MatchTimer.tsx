import { useEffect } from "react";
import { useMatchTimer } from "@/hooks/useMatchTimer";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { Play, Pause, RotateCcw, Plus, Minus, Timer, Flag } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface MatchTimerProps {
  durationMinutes: number;
  onTimeUp?: () => void;
  onMatchEnd?: () => void;
  startTime?: Date;
}

export function MatchTimer({ durationMinutes, onTimeUp, onMatchEnd, startTime }: MatchTimerProps) {
  const {
    formattedTime,
    isRunning,
    isPaused,
    progress,
    startTimer,
    pauseTimer,
    resetTimer,
    addTime,
  } = useMatchTimer({
    initialDurationSeconds: durationMinutes * 60,
    onTimeUp,
    startTime,
  });

  // Play sound when timer is up
  useEffect(() => {
    if (progress === 0 && !isRunning && !isPaused) {
      const audio = new Audio("/sounds/whistle.mp3");
      audio.play().catch((e) => console.error("Error playing sound:", e));
    }
  }, [progress, isRunning, isPaused]);

  // Automatically start timer if startTime is provided
  useEffect(() => {
    if (startTime && !isRunning && !isPaused && progress > 0) {
      startTimer();
    }
  }, [startTime, isRunning, isPaused, progress, startTimer]);

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-center gap-2">
          <Timer className="h-5 w-5" />
          Match Timer
        </CardTitle>
        <CardDescription className="text-center">
          Spielzeit-Timer für das aktuelle Match
        </CardDescription>
        <Separator />
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Circular Timer Display */}
        <div className="flex justify-center">
          <div className="relative w-60 h-60">
            {/* Circular Progress Background */}
            <div className="absolute inset-0 rounded-full border-8 border-gray-100 dark:border-gray-800"></div>

            {/* Circular Progress Indicator */}
            <svg className="absolute inset-0 w-full h-full rotate-[-90deg]" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="46"
                fill="none"
                stroke="#3b82f6" // blue-500
                strokeWidth="8"
                strokeDasharray="289.02652413026095"
                strokeDashoffset={289.02652413026095 * (1 - progress / 100)}
                strokeLinecap="round"
              />
            </svg>

            {/* Timer Text */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-6xl font-bold font-mono text-gray-900 dark:text-gray-100 tracking-wider">
                {formattedTime}
              </div>
              <div className="text-sm text-muted-foreground mt-1">Spielzeit</div>
            </div>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex justify-center gap-3">
          {!isRunning ? (
            <Button
              onClick={startTimer}
              variant="default"
              className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 px-6"
            >
              <Play className="mr-2 h-4 w-4" />
              {isPaused ? "Fortsetzen" : "Start"}
            </Button>
          ) : (
            <Button
              onClick={pauseTimer}
              variant="default"
              className="bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 px-6"
            >
              <Pause className="mr-2 h-4 w-4" />
              Pause
            </Button>
          )}

          <Button
            onClick={() => resetTimer()}
            variant="outline"
            className="border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
        </div>

        {/* End Game Button */}
        {onMatchEnd && (
          <div className="flex justify-center mt-4">
            <Button onClick={onMatchEnd} variant="destructive" className="w-full sm:w-auto px-6">
              <Flag className="mr-2 h-4 w-4" />
              Spiel beenden
            </Button>
          </div>
        )}

        {/* Time Adjustment Buttons */}
        <div className="flex justify-center gap-4 mt-4">
          <Button
            onClick={() => addTime(-60)}
            variant="outline"
            size="sm"
            className="border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30 rounded-full w-10 h-10 p-0 flex items-center justify-center"
          >
            <Minus className="h-4 w-4" />
          </Button>
          <div className="flex items-center text-sm font-medium">1 min</div>
          <Button
            onClick={() => addTime(60)}
            variant="outline"
            size="sm"
            className="border-green-200 text-green-700 hover:bg-green-50 hover:text-green-800 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/30 rounded-full w-10 h-10 p-0 flex items-center justify-center"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
