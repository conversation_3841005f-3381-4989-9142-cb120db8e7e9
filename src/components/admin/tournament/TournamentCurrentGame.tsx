import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>Header,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, Calendar, Trophy, Users, Flag, Play, Settings } from "lucide-react";
import { CompactMatchTimer } from "./CompactMatchTimer";
import { TournamentMatch, Tournament } from "@/types/tournament";
import { getTeamColor } from "@/utils/teamColors";
import { toast } from "@/components/ui/use-toast";

interface TournamentCurrentGameProps {
  tournament: Tournament | null;
  tournamentLoading: boolean;
  activeMatches: TournamentMatch[];
  recentlyCompletedMatches: TournamentMatch[];
  onMatchEnd: (match?: TournamentMatch) => void;
  onMultipleResultsOpen: () => void;
  onTabChange: (tab: string) => void;
}

export function TournamentCurrentGame({
  tournament,
  tournamentLoading,
  activeMatches,
  recentlyCompletedMatches,
  onMatchEnd,
  onMultipleResultsOpen,
  onTabChange,
}: TournamentCurrentGameProps) {
  return (
    <>
      {tournament && (activeMatches.length > 0 || recentlyCompletedMatches.length > 0) && (
        <>
          {/* Aktive Spiele */}
          {activeMatches.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Aktive Spiele</CardTitle>
                    <CardDescription>Laufende Matches</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                      {activeMatches.length} aktive{" "}
                      {activeMatches.length === 1 ? "Partie" : "Partien"}
                    </div>
                    {activeMatches.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
                        onClick={onMultipleResultsOpen}
                      >
                        <Flag className="h-3 w-3 mr-1" />
                        Alle beenden
                      </Button>
                    )}
                  </div>
                </div>
                <Separator className="mt-3" />
              </CardHeader>
              <CardContent>
                {/* Grid layout for matches - side by side on desktop, but full width if only one match */}
                <div
                  className={`grid grid-cols-1 ${activeMatches.length > 1 ? "md:grid-cols-2" : ""} gap-4`}
                >
                  {activeMatches.map((match) => (
                    <div
                      key={match.id}
                      className={`border rounded-lg p-4 pt-5 pb-3 relative bg-white dark:bg-gray-800 shadow-sm ${activeMatches.length === 1 ? "max-w-3xl mx-auto w-full" : ""}`}
                    >
                      {/* Match Badge */}
                      <div className="absolute top-0 right-0 -mt-3 -mr-2 bg-blue-50 dark:bg-blue-900/10 text-blue-700 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium border border-blue-100 dark:border-blue-800">
                        {match.field}
                      </div>

                      <div
                        className={`flex flex-col ${activeMatches.length === 1 ? "md:flex-row" : "lg:flex-row"} items-center justify-center ${activeMatches.length === 1 ? "md:justify-between" : "lg:justify-between"} px-2 md:px-4`}
                      >
                        {/* Teams und VS in der Mitte */}
                        <div className="flex items-center justify-center gap-4 mb-3 md:mb-0 md:mr-4">
                          {/* Team A */}
                          <div className="flex flex-col items-center">
                            <div
                              className={`${activeMatches.length === 1 ? "w-16 h-16" : "w-12 h-12"} rounded-full ${getTeamColor(match.teamA.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamA.id).border} ${getTeamColor(match.teamA.id).bg}`}
                            >
                              <Users
                                className={`${activeMatches.length === 1 ? "h-6 w-6" : "h-5 w-5"} ${getTeamColor(match.teamA.id).textDark}`}
                              />
                            </div>
                            <span
                              className={`${activeMatches.length === 1 ? "text-sm" : "text-xs"} font-medium mt-1 ${getTeamColor(match.teamA.id).text}`}
                            >
                              {match.teamA.name}
                            </span>
                          </div>

                          {/* VS */}
                          <div
                            className={`${activeMatches.length === 1 ? "text-xl" : "text-base"} font-medium text-gray-400 dark:text-gray-500 mx-2`}
                          >
                            VS
                          </div>

                          {/* Team B */}
                          <div className="flex flex-col items-center">
                            <div
                              className={`${activeMatches.length === 1 ? "w-16 h-16" : "w-12 h-12"} rounded-full ${getTeamColor(match.teamB.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamB.id).border} ${getTeamColor(match.teamB.id).bg}`}
                            >
                              <Users
                                className={`${activeMatches.length === 1 ? "h-6 w-6" : "h-5 w-5"} ${getTeamColor(match.teamB.id).textDark}`}
                              />
                            </div>
                            <span
                              className={`${activeMatches.length === 1 ? "text-sm" : "text-xs"} font-medium mt-1 ${getTeamColor(match.teamB.id).text}`}
                            >
                              {match.teamB.name}
                            </span>
                          </div>
                        </div>

                        {/* Timer - Compact version */}
                        <div className="flex justify-center">
                          <CompactMatchTimer
                            key={match.id}
                            durationMinutes={tournament.config.matchDurationMinutes}
                            onTimeUp={() => {
                              toast({
                                title: "Spielzeit abgelaufen",
                                description: `Die Spielzeit für ${match.teamA.name} vs ${match.teamB.name} ist abgelaufen. Bitte trage das Ergebnis ein.`,
                                variant: "destructive",
                              });
                              onMatchEnd(match);
                            }}
                            onMatchEnd={() => onMatchEnd(match)}
                            startTime={match.startTime}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Beendete Spiele */}
          {recentlyCompletedMatches.length > 0 && (
            <Card className="mt-6">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Beendete Spiele</CardTitle>
                    <CardDescription>Abgeschlossene Matches</CardDescription>
                  </div>
                  <div className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                    {recentlyCompletedMatches.length} beendete{" "}
                    {recentlyCompletedMatches.length === 1 ? "Partie" : "Partien"}
                  </div>
                </div>
                <Separator className="mt-3" />
              </CardHeader>
              <CardContent>
                {/* Turnier beendet Banner (wenn das Turnier beendet ist) */}
                {tournament && !tournament.isActive && tournament.endTime && (
                  <Alert className="border-green-200 bg-green-50/50 text-green-800 dark:border-green-900/30 dark:bg-green-900/10 dark:text-green-300 mb-8">
                    <Trophy className="h-4 w-4" />
                    <AlertTitle>Turnier abgeschlossen</AlertTitle>
                    <AlertDescription>
                      Das Turnier wurde beendet. Es können keine weiteren Spiele gestartet werden.
                    </AlertDescription>
                  </Alert>
                )}
                <div
                  className={`grid grid-cols-1 ${recentlyCompletedMatches.length > 1 ? "md:grid-cols-2" : ""} gap-4`}
                >
                  {recentlyCompletedMatches.map((match) => (
                    <div
                      key={match.id}
                      className={`p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm ${recentlyCompletedMatches.length === 1 ? "max-w-3xl mx-auto w-full" : ""}`}
                    >
                      <div className="flex items-center justify-between">
                        {/* Team A */}
                        <div className="flex items-center gap-2">
                          <div
                            className={`${recentlyCompletedMatches.length === 1 ? "w-12 h-12" : "w-10 h-10"} rounded-full ${getTeamColor(match.teamA.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamA.id).border} ${getTeamColor(match.teamA.id).bg}`}
                          >
                            <Users
                              className={`${recentlyCompletedMatches.length === 1 ? "h-5 w-5" : "h-4 w-4"} ${getTeamColor(match.teamA.id).textDark}`}
                            />
                          </div>
                          <span
                            className={`${recentlyCompletedMatches.length === 1 ? "text-base" : "text-sm"} font-medium ${getTeamColor(match.teamA.id).text}`}
                          >
                            {match.teamA.name}
                          </span>
                        </div>

                        {/* Result */}
                        <div className="flex flex-col items-center">
                          <div
                            className={`${recentlyCompletedMatches.length === 1 ? "text-2xl" : "text-xl"} font-bold`}
                          >
                            {match.goalsA} : {match.goalsB}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {match.field}
                          </div>
                        </div>

                        {/* Team B */}
                        <div className="flex items-center gap-2">
                          <span
                            className={`${recentlyCompletedMatches.length === 1 ? "text-base" : "text-sm"} font-medium ${getTeamColor(match.teamB.id).text}`}
                          >
                            {match.teamB.name}
                          </span>
                          <div
                            className={`${recentlyCompletedMatches.length === 1 ? "w-12 h-12" : "w-10 h-10"} rounded-full ${getTeamColor(match.teamB.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamB.id).border} ${getTeamColor(match.teamB.id).bg}`}
                          >
                            <Users
                              className={`${recentlyCompletedMatches.length === 1 ? "h-5 w-5" : "h-4 w-4"} ${getTeamColor(match.teamB.id).textDark}`}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {tournament &&
        activeMatches.length === 0 &&
        recentlyCompletedMatches.length === 0 &&
        tournament.isActive && (
          <Card>
            <CardHeader>
              <CardTitle>Keine aktiven Spiele</CardTitle>
              <CardDescription>Es sind derzeit keine Spiele aktiv</CardDescription>
              <Separator />
            </CardHeader>
            <CardContent className="pt-6">
              <Alert className="border-amber-200 bg-amber-50/50 text-amber-800 dark:border-amber-900/30 dark:bg-amber-900/10 dark:text-amber-300">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Keine aktiven Spiele</AlertTitle>
                <AlertDescription>
                  Es sind derzeit keine Spiele aktiv. Wähle ein oder mehrere Spiele im Spielplan
                  aus, um den Timer zu starten.
                </AlertDescription>
              </Alert>

              <div className="mt-6">
                <Button
                  onClick={() => onTabChange("schedule")}
                  className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Zum Spielplan
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

      {!tournament && !tournamentLoading && (
        <Card>
          <CardHeader>
            <CardTitle>Kein Turnier</CardTitle>
            <CardDescription>Es wurde noch kein Turnier erstellt</CardDescription>
            <Separator />
          </CardHeader>
          <CardContent className="pt-6">
            <Alert className="border-amber-200 bg-amber-50/50 text-amber-800 dark:border-amber-900/30 dark:bg-amber-900/10 dark:text-amber-300">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Kein Turnier</AlertTitle>
              <AlertDescription>
                Es wurde noch kein Turnier erstellt. Bitte erstelle zuerst ein Turnier im Bereich
                "Konfiguration".
              </AlertDescription>
            </Alert>

            <div className="mt-6">
              <Button
                onClick={() => onTabChange("config")}
                className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
              >
                <Settings className="h-4 w-4 mr-2" />
                Zur Konfiguration
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
