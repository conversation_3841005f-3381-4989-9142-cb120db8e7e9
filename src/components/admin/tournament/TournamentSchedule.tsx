import React, { use<PERSON>em<PERSON> } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tournament, TournamentMatch } from "@/types/tournament";
import {
  Play,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Trash2,
  Trophy,
  Pencil,
  Goal,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { getTeamColor } from "@/utils/teamColors";

interface TournamentScheduleProps {
  tournament: Tournament;
  onStartMatch: (matchId: string) => Promise<void>;
  onStartMultipleMatches?: (matchIds: string[]) => Promise<string | undefined>;
  onEnterResult: (match: TournamentMatch) => void;
  onAddNextMatch?: () => Promise<void>;
  onDeleteMatch?: (matchId: string) => void;
  currentMatchId?: string;
  totalPossibleMatches?: number;
  isTournamentEnded?: boolean;
}

export function TournamentSchedule({
  tournament,
  onStartMatch,
  onStartMultipleMatches,
  onEnterResult,
  onAddNextMatch,
  onDeleteMatch,
  currentMatchId,
  totalPossibleMatches,
  isTournamentEnded = false,
}: TournamentScheduleProps) {
  const { matches, config } = tournament;
  const [selectedMatches, setSelectedMatches] = React.useState<string[]>([]);

  // We now use the global getTeamColor function from teamColors.ts

  // Format time from Date object
  const formatTime = (date?: Date) => {
    if (!date) return "-";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Group matches by time slot
  const matchesByTimeSlot = useMemo(() => {
    const grouped = new Map<number, TournamentMatch[]>();

    matches.forEach((match) => {
      const timeSlot = match.timeSlot || 1; // Default to time slot 1 if not specified
      if (!grouped.has(timeSlot)) {
        grouped.set(timeSlot, []);
      }
      grouped.get(timeSlot)!.push(match);
    });

    // Convert to array of [timeSlot, matches] pairs and sort by time slot
    return Array.from(grouped.entries()).sort((a, b) => a[0] - b[0]);
  }, [matches]);

  // Check if a match can be selected for simultaneous start
  const canSelectMatch = (match: TournamentMatch) => {
    return !match.isCompleted && !match.startTime && !isTournamentEnded;
  };

  // Handle match selection for simultaneous start
  const toggleMatchSelection = (matchId: string) => {
    setSelectedMatches((prev) => {
      if (prev.includes(matchId)) {
        return prev.filter((id) => id !== matchId);
      } else {
        return [...prev, matchId];
      }
    });
  };

  // Start selected matches simultaneously
  const handleStartSelectedMatches = async () => {
    if (selectedMatches.length === 0 || !onStartMultipleMatches) return;

    const firstMatchId = await onStartMultipleMatches(selectedMatches);
    setSelectedMatches([]);

    return firstMatchId;
  };

  // Get match status badge with fixed width
  const getMatchStatusBadge = (match: TournamentMatch) => {
    if (match.isCompleted) {
      return (
        <Badge
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200 w-24 justify-center"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Beendet
        </Badge>
      );
    }

    if (match.startTime && !match.endTime) {
      return (
        <Badge
          variant="outline"
          className="bg-blue-50 text-blue-700 border-blue-200 w-24 justify-center"
        >
          <Clock className="h-3 w-3 mr-1" />
          Läuft
        </Badge>
      );
    }

    if (selectedMatches.includes(match.id)) {
      return (
        <Badge
          variant="outline"
          className="bg-purple-50 text-purple-700 border-purple-200 w-24 justify-center"
        >
          Ausgewählt
        </Badge>
      );
    }

    return (
      <Badge
        variant="outline"
        className="bg-gray-50 text-gray-700 border-gray-200 w-24 justify-center"
      >
        Ausstehend
      </Badge>
    );
  };

  return (
    <div className="relative">
      {/* Floating Action Button for Mobile - Fixed position at bottom right */}
      {onAddNextMatch && !isTournamentEnded && (
        <div className="fixed bottom-6 right-6 z-10 sm:hidden">
          <Button
            onClick={async () => {
              if (onAddNextMatch) await onAddNextMatch();
            }}
            size="icon"
            className="h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 shadow-lg"
            title="Weitere Partie hinzufügen"
          >
            <Plus className="h-6 w-6" />
          </Button>
        </div>
      )}

      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <CardTitle>Spielplan</CardTitle>
              <CardDescription>Übersicht aller geplanten Spiele im Turnier</CardDescription>
            </div>

            {isTournamentEnded && (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 px-3 py-1.5"
              >
                <Trophy className="h-4 w-4 mr-1" />
                Turnier beendet
              </Badge>
            )}
          </div>
          <Separator className="mt-3" />
        </CardHeader>
        <CardContent className="pt-3">
          {/* Tournament Ended Alert */}
          {isTournamentEnded && (
            <Alert className="mb-6 border-green-200 bg-green-50/50 text-green-800 dark:border-green-900/30 dark:bg-green-900/10 dark:text-green-300">
              <Trophy className="h-4 w-4" />
              <AlertTitle>Turnier abgeschlossen</AlertTitle>
              <AlertDescription>
                Das Turnier wurde beendet. Die Ergebnisse wurden in die Spielergebnisse übernommen.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons Container - Better alignment */}
          <div className="flex flex-col sm:flex-row gap-3 mb-5">
            {/* Start Selected Matches Button */}
            {selectedMatches.length > 0 && onStartMultipleMatches && !isTournamentEnded && (
              <Button
                onClick={handleStartSelectedMatches}
                size="default"
                className="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 flex-1 order-1 sm:order-1"
              >
                <Play className="h-4 w-4 mr-2" />
                {selectedMatches.length} ausgewählte Spiele starten
              </Button>
            )}

            {/* Desktop Add Match Button - Only visible on desktop */}
            {onAddNextMatch && !isTournamentEnded && (
              <Button
                onClick={async () => {
                  if (onAddNextMatch) await onAddNextMatch();
                }}
                size="default"
                className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 hidden sm:flex sm:ml-auto order-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Weitere Partie hinzufügen
              </Button>
            )}
          </div>

          <div className="space-y-8">
            {matchesByTimeSlot.map(([timeSlot, slotMatches]) => (
              <div key={`timeslot-${timeSlot}`} className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-gray-300 flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                    Spielrunde {timeSlot}
                  </h3>

                  {/* Show simultaneous start button for this time slot if there are multiple matches */}
                  {slotMatches.filter(canSelectMatch).length > 1 &&
                    onStartMultipleMatches &&
                    !isTournamentEnded && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-7 px-2 text-xs border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
                        onClick={() => {
                          const matchIds = slotMatches.filter(canSelectMatch).map((m) => m.id);
                          setSelectedMatches(matchIds);
                        }}
                      >
                        Alle auswählen
                      </Button>
                    )}
                </div>

                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40px] text-center">Nr.</TableHead>
                        <TableHead className="w-[300px]">Teams</TableHead>
                        <TableHead className="w-[80px] text-center">Ergebnis</TableHead>
                        <TableHead className="w-[80px] text-center hidden md:table-cell">
                          Feld
                        </TableHead>
                        <TableHead className="w-[100px] text-center hidden sm:table-cell">
                          Status
                        </TableHead>
                        <TableHead className="w-[100px] text-center hidden lg:table-cell">
                          Zeit
                        </TableHead>
                        <TableHead className="w-[120px] text-center">Aktionen</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {slotMatches.map((match) => (
                        <TableRow
                          key={match.id}
                          className={
                            selectedMatches.includes(match.id)
                              ? "bg-purple-50/50 dark:bg-purple-900/10"
                              : ""
                          }
                          onClick={() => {
                            if (canSelectMatch(match)) {
                              toggleMatchSelection(match.id);
                            }
                          }}
                          style={{ cursor: canSelectMatch(match) ? "pointer" : "default" }}
                        >
                          <TableCell className="font-medium text-center">
                            {match.matchNumber}
                          </TableCell>
                          <TableCell className="w-[300px]">
                            {/* Desktop view - Fixed width layout */}
                            <div className="hidden sm:grid grid-cols-[1fr,auto,1fr] items-center gap-2 w-full">
                              {/* Team A - Fixed width and right-aligned */}
                              <div className="text-right">
                                <div className="flex items-center justify-end gap-1">
                                  <span
                                    className={`font-medium text-sm ${getTeamColor(match.teamA.id).text}`}
                                  >
                                    {match.teamA.name}
                                  </span>
                                  <div
                                    className={`w-3 h-3 rounded-full ${getTeamColor(match.teamA.id).bg} border ${getTeamColor(match.teamA.id).border}`}
                                  ></div>
                                </div>
                              </div>

                              {/* VS - Fixed position in center */}
                              <div className="bg-gray-100 dark:bg-gray-800 w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mx-auto">
                                <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                                  VS
                                </span>
                              </div>

                              {/* Team B - Fixed width and left-aligned */}
                              <div>
                                <div className="flex items-center gap-1">
                                  <div
                                    className={`w-3 h-3 rounded-full ${getTeamColor(match.teamB.id).bg} border ${getTeamColor(match.teamB.id).border}`}
                                  ></div>
                                  <span
                                    className={`font-medium text-sm ${getTeamColor(match.teamB.id).text}`}
                                  >
                                    {match.teamB.name}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Mobile view - stacked teams */}
                            <div className="flex sm:hidden flex-col gap-1">
                              <div className="flex items-center gap-1">
                                <div
                                  className={`w-3 h-3 rounded-full ${getTeamColor(match.teamA.id).bg} border ${getTeamColor(match.teamA.id).border}`}
                                ></div>
                                <span
                                  className={`text-xs font-medium ${getTeamColor(match.teamA.id).text}`}
                                >
                                  {match.teamA.name}
                                </span>
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">vs</div>
                              <div className="flex items-center gap-1">
                                <div
                                  className={`w-3 h-3 rounded-full ${getTeamColor(match.teamB.id).bg} border ${getTeamColor(match.teamB.id).border}`}
                                ></div>
                                <span
                                  className={`text-xs font-medium ${getTeamColor(match.teamB.id).text}`}
                                >
                                  {match.teamB.name}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            {match.isCompleted ? (
                              <span className="font-medium">
                                {match.goalsA} : {match.goalsB}
                              </span>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell className="hidden md:table-cell text-center">
                            {match.field || "-"}
                          </TableCell>
                          <TableCell className="hidden sm:table-cell text-center">
                            {getMatchStatusBadge(match)}
                          </TableCell>
                          <TableCell className="hidden lg:table-cell text-center">
                            {match.startTime ? (
                              <div className="text-xs">
                                <div>Start: {formatTime(match.startTime)}</div>
                                {match.endTime && <div>Ende: {formatTime(match.endTime)}</div>}
                              </div>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell onClick={(e) => e.stopPropagation()} className="text-center">
                            <div className="flex flex-wrap justify-center gap-1">
                              {/* Start button */}
                              {!match.isCompleted && !match.startTime && !isTournamentEnded && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-7 px-1.5 sm:h-8 sm:px-1.5 text-xs border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
                                  onClick={async (e) => {
                                    e.stopPropagation();
                                    await onStartMatch(match.id);
                                  }}
                                  title="Spiel starten"
                                >
                                  <Play className="h-3 w-3" />
                                </Button>
                              )}

                              {/* Result/Edit button */}
                              {(match.startTime || match.isCompleted) && (
                                <Button
                                  size="sm"
                                  variant={match.isCompleted ? "outline" : "default"}
                                  className={
                                    match.isCompleted
                                      ? "h-7 px-1.5 sm:h-8 sm:px-1.5 text-xs border-green-200 text-green-700 hover:bg-green-50 hover:text-green-800 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/30"
                                      : "h-7 px-1.5 sm:h-8 sm:px-1.5 text-xs bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                                  }
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEnterResult(match);
                                  }}
                                  disabled={isTournamentEnded}
                                  title={
                                    match.isCompleted ? "Ergebnis bearbeiten" : "Ergebnis eintragen"
                                  }
                                >
                                  {match.isCompleted ? (
                                    <Pencil className="h-3 w-3" />
                                  ) : (
                                    <Goal className="h-3 w-3" />
                                  )}
                                </Button>
                              )}

                              {/* Delete button */}
                              {!match.isCompleted && onDeleteMatch && !isTournamentEnded && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-7 px-1.5 sm:h-8 sm:px-1.5 text-xs border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onDeleteMatch(match.id);
                                  }}
                                  title="Spiel löschen"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
