import { screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { ResultsEntry } from "../ResultsEntry";
import { TournamentMatch } from "@/types/tournament";
import { Team } from "@/types";
import { renderWithProviders } from "@/test/testUtils";

// Mock getTeamColor utility
vi.mock("@/utils/teamColors", () => ({
  getTeamColor: () => ({
    bg: "bg-blue-100",
    border: "border-blue-300",
    text: "text-blue-700",
  }),
}));

describe("ResultsEntry", () => {
  const mockTeams: Team[] = [
    { id: "team-1", name: "Team A", players: [], averageRating: 0 },
    { id: "team-2", name: "Team B", players: [], averageRating: 0 },
  ];

  const mockMatch: TournamentMatch = {
    id: "match-1",
    teamA: mockTeams[0],
    teamB: mockTeams[1],
    goalsA: null,
    goalsB: null,
    isCompleted: false,
    matchNumber: 1,
    field: "Feld 1",
    timeSlot: 1,
  };

  const mockHandlers = {
    onClose: vi.fn(),
    onSave: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the dialog with match details", () => {
    renderWithProviders(
      <ResultsEntry
        match={mockMatch}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Check if the dialog title is rendered
    expect(screen.getByText("Spielergebnis eintragen")).toBeInTheDocument();

    // Check if the match description is rendered
    expect(screen.getByText("Trage das Ergebnis für das Spiel 1 ein")).toBeInTheDocument();

    // Check if team names are rendered
    expect(screen.getByText("Team A")).toBeInTheDocument();
    expect(screen.getByText("Team B")).toBeInTheDocument();

    // Check if the "vs" text is rendered
    expect(screen.getByText("vs")).toBeInTheDocument();

    // Check if the goal input labels are rendered
    expect(screen.getByText("Tore Team A")).toBeInTheDocument();
    expect(screen.getByText("Tore Team B")).toBeInTheDocument();

    // Check if the buttons are rendered
    expect(screen.getByText("Abbrechen")).toBeInTheDocument();
    expect(screen.getByText("Speichern")).toBeInTheDocument();
  });

  it("should not render when isOpen is false", () => {
    renderWithProviders(
      <ResultsEntry
        match={mockMatch}
        isOpen={false}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // The dialog should not be visible when isOpen is false
    expect(screen.queryByText("Spielergebnis eintragen")).not.toBeInTheDocument();
  });

  it("should not render when match is null", () => {
    renderWithProviders(
      <ResultsEntry
        match={null}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // The component should return null when match is null
    expect(screen.queryByText("Spielergebnis eintragen")).not.toBeInTheDocument();
  });

  it("should call onClose when cancel button is clicked", () => {
    renderWithProviders(
      <ResultsEntry
        match={mockMatch}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find and click the cancel button
    const cancelButton = screen.getByText("Abbrechen");
    fireEvent.click(cancelButton);

    // Verify that onClose was called
    expect(mockHandlers.onClose).toHaveBeenCalledTimes(1);
  });

  it("should call onSave with correct values when save button is clicked", () => {
    renderWithProviders(
      <ResultsEntry
        match={mockMatch}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find the goal input fields
    const goalsAInput = screen.getByLabelText("Tore Team A");
    const goalsBInput = screen.getByLabelText("Tore Team B");

    // Set values for the goals
    fireEvent.change(goalsAInput, { target: { value: "3" } });
    fireEvent.change(goalsBInput, { target: { value: "2" } });

    // Find and click the save button
    const saveButton = screen.getByText("Speichern");
    fireEvent.click(saveButton);

    // Verify that onSave was called with the correct values
    expect(mockHandlers.onSave).toHaveBeenCalledWith("match-1", 3, 2);

    // Verify that onClose was called after saving
    expect(mockHandlers.onClose).toHaveBeenCalledTimes(1);
  });

  it("should initialize with existing goals if match has results", () => {
    // Create a match with existing goals
    const matchWithGoals: TournamentMatch = {
      ...mockMatch,
      goalsA: 2,
      goalsB: 1,
    };

    renderWithProviders(
      <ResultsEntry
        match={matchWithGoals}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find the goal input fields
    const goalsAInput = screen.getByLabelText("Tore Team A") as HTMLInputElement;
    const goalsBInput = screen.getByLabelText("Tore Team B") as HTMLInputElement;

    // Verify that the inputs are initialized with the existing goals
    expect(goalsAInput.value).toBe("2");
    expect(goalsBInput.value).toBe("1");
  });

  it("should handle negative values in input fields", () => {
    renderWithProviders(
      <ResultsEntry
        match={mockMatch}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find the goal input fields
    const goalsAInput = screen.getByLabelText("Tore Team A");

    // Try to set a negative value
    fireEvent.change(goalsAInput, { target: { value: "-1" } });

    // Find and click the save button
    const saveButton = screen.getByText("Speichern");
    fireEvent.click(saveButton);

    // The component currently passes the negative value through
    // This test documents the current behavior
    expect(mockHandlers.onSave).toHaveBeenCalledWith("match-1", -1, 0);

    // Note: In a future enhancement, we might want to validate and prevent negative values
    // before saving, but for now we're testing the actual implementation
  });
});
