import { screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { CompactMatchTimer } from "../CompactMatchTimer";
import { useMatchTimer } from "@/hooks/useMatchTimer";
import { renderWithProviders } from "@/test/testUtils";

// Mock the useMatchTimer hook
vi.mock("@/hooks/useMatchTimer", () => ({
  useMatchTimer: vi.fn(),
}));

// Mock the Audio constructor
const mockAudioPlay = vi.fn();
global.Audio = vi.fn().mockImplementation(() => ({
  play: mockAudioPlay,
}));

describe("CompactMatchTimer", () => {
  const mockTimerState = {
    timeRemaining: 300,
    formattedTime: "05:00",
    isRunning: false,
    isPaused: false,
    progress: 100,
    startTimer: vi.fn(),
    pauseTimer: vi.fn(),
    resetTimer: vi.fn(),
    addTime: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementation
    vi.mocked(useMatchTimer).mockReturnValue(mockTimerState);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should render the timer with correct time", () => {
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Check if the formatted time is displayed
    expect(screen.getByText("05:00")).toBeInTheDocument();

    // Check if the "Spielzeit" label is displayed
    expect(screen.getByText("Spielzeit")).toBeInTheDocument();
  });

  it("should call startTimer when start button is clicked", () => {
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Find the start button by its title
    const startButton = screen.getByTitle("Start");
    fireEvent.click(startButton);

    // Verify that startTimer was called
    expect(mockTimerState.startTimer).toHaveBeenCalledTimes(1);
  });

  it("should call pauseTimer when pause button is clicked", () => {
    // Set isRunning to true to show the pause button
    vi.mocked(useMatchTimer).mockReturnValue({
      ...mockTimerState,
      isRunning: true,
    });

    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Find the pause button by its title
    const pauseButton = screen.getByTitle("Pause");
    fireEvent.click(pauseButton);

    // Verify that pauseTimer was called
    expect(mockTimerState.pauseTimer).toHaveBeenCalledTimes(1);
  });

  it("should call resetTimer when reset button is clicked", () => {
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Find the reset button by its title
    const resetButton = screen.getByTitle("Reset");
    fireEvent.click(resetButton);

    // Verify that resetTimer was called
    expect(mockTimerState.resetTimer).toHaveBeenCalledTimes(1);
  });

  it("should attempt to play sound when timer reaches zero", () => {
    // For this test, we'll need to mock the component's useEffect
    // We'll use vi.spyOn to spy on the Audio constructor and play method

    // First, let's create a spy for the Audio constructor
    const audioSpy = vi.spyOn(global, "Audio");

    // Mock timer state with progress at 0 and not running
    vi.mocked(useMatchTimer).mockReturnValue({
      ...mockTimerState,
      progress: 0,
      isRunning: false,
      isPaused: false,
    });

    // We need to mock the component's useEffect to avoid the actual audio.play() call
    // Let's create a mock implementation of the Audio class
    const mockAudio = {
      play: vi.fn().mockResolvedValue(undefined),
    } as unknown as HTMLAudioElement;

    // Replace the global Audio constructor with our mock
    audioSpy.mockImplementation(() => mockAudio);

    // Render the component
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Verify that the Audio constructor was called with the correct sound file
    expect(audioSpy).toHaveBeenCalledWith("/sounds/whistle.mp3");

    // Clean up
    audioSpy.mockRestore();
  });

  it("should not play sound when timer is still running", () => {
    // Mock timer state with progress at 0 but still running
    vi.mocked(useMatchTimer).mockReturnValue({
      ...mockTimerState,
      progress: 0,
      isRunning: true,
    });

    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Verify that the Audio constructor was not called
    expect(global.Audio).not.toHaveBeenCalled();

    // Verify that play() was not called
    expect(mockAudioPlay).not.toHaveBeenCalled();
  });

  it("should automatically start timer if startTime is provided", () => {
    // Mock a start time
    const startTime = new Date();

    renderWithProviders(<CompactMatchTimer durationMinutes={5} startTime={startTime} />);

    // The useEffect should call startTimer if startTime is provided
    // and the timer is not running, not paused, and has progress > 0
    expect(mockTimerState.startTimer).toHaveBeenCalledTimes(1);
  });

  it("should call onTimeUp when timer completes", () => {
    // Create a mock onTimeUp function
    const onTimeUp = vi.fn();

    // Pass the mock function to the component
    renderWithProviders(<CompactMatchTimer durationMinutes={5} onTimeUp={onTimeUp} />);

    // Verify that the onTimeUp function was passed to useMatchTimer
    expect(useMatchTimer).toHaveBeenCalledWith(
      expect.objectContaining({
        onTimeUp,
      })
    );
  });

  it("should show circular progress indicator with correct progress", () => {
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Find the SVG circle element
    const progressCircle = document.querySelector("circle");

    // Verify that the circle exists
    expect(progressCircle).not.toBeNull();

    // Verify that the circle has the correct attributes
    expect(progressCircle).toHaveAttribute("cx", "50");
    expect(progressCircle).toHaveAttribute("cy", "50");
    expect(progressCircle).toHaveAttribute("r", "46");

    // The strokeDashoffset calculation is done in the component
    // We can verify that the progress value is passed correctly to the component
    expect(useMatchTimer).toHaveBeenCalledWith(
      expect.objectContaining({
        initialDurationSeconds: 5 * 60,
      })
    );

    // Test with a different progress value
    vi.mocked(useMatchTimer).mockReturnValue({
      ...mockTimerState,
      progress: 50,
    });

    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Get the updated circle element
    const updatedCircle = document.querySelector("circle");

    // Verify that the circle exists
    expect(updatedCircle).not.toBeNull();

    // We can check that the stroke color is defined by a gradient
    expect(updatedCircle).toHaveAttribute("stroke", expect.stringContaining("url(#"));
  });

  it("should render end match button when onMatchEnd is provided", () => {
    const onMatchEnd = vi.fn();

    renderWithProviders(<CompactMatchTimer durationMinutes={5} onMatchEnd={onMatchEnd} />);

    // Find the end match button by its title
    const endButton = screen.getByTitle("Beenden");

    // Verify that the button exists
    expect(endButton).toBeInTheDocument();

    // Click the button
    fireEvent.click(endButton);

    // Verify that onMatchEnd was called
    expect(onMatchEnd).toHaveBeenCalledTimes(1);
  });

  it("should not render end match button when onMatchEnd is not provided", () => {
    renderWithProviders(<CompactMatchTimer durationMinutes={5} />);

    // Try to find the end match button
    const endButton = screen.queryByTitle("Beenden");

    // Verify that the button does not exist
    expect(endButton).not.toBeInTheDocument();
  });
});
