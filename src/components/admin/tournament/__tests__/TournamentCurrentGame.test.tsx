import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { TournamentCurrentGame } from "../TournamentCurrentGame";
import { renderWithProviders } from "@/test/testUtils";
import {
  createMockTeams,
  createMockTournament,
  createMockTournamentMatches,
} from "@/test/testUtils";
import { Team } from "@/types";
import { Tournament, TournamentMatch } from "@/types/tournament";

// Mock the CompactMatchTimer component to avoid timer-related issues in tests
vi.mock("../CompactMatchTimer", () => ({
  CompactMatchTimer: ({ onMatchEnd, onTimeUp }: any) => (
    <div data-testid="mock-timer">
      <button onClick={() => onMatchEnd()}>End Match</button>
      <button onClick={() => onTimeUp()}>Time Up</button>
    </div>
  ),
}));

// Mock toast
vi.mock("@/components/ui/use-toast", () => ({
  toast: vi.fn(),
}));

describe("TournamentCurrentGame", () => {
  // Create mock teams
  const mockTeams: Team[] = createMockTeams(3).map((team) => ({
    id: team.id,
    name: team.name,
    players: [],
    averageRating: team.rating || 0,
  }));

  // Create active matches
  const activeMatches: TournamentMatch[] = createMockTournamentMatches(mockTeams, 2, {
    startTimeBase: new Date(),
    matchDurationMinutes: 10,
  });

  // Create completed matches
  const completedMatches: TournamentMatch[] = createMockTournamentMatches(mockTeams, 2, {
    startTimeBase: new Date(Date.now() - 3600000), // 1 hour ago
    matchDurationMinutes: 10,
    completedMatches: 2,
    specificGoals: [
      { index: 0, goalsA: 3, goalsB: 1 },
      { index: 1, goalsA: 2, goalsB: 0 },
    ],
  });

  // Create a mock tournament
  const mockTournament: Tournament = createMockTournament(mockTeams, 3, {
    id: "tournament-1",
    gameSessionId: "session-1",
    totalDurationMinutes: 60,
    breakBetweenMatchesMinutes: 2,
    matchDurationMinutes: 10,
    numberOfFields: 1,
    isActive: true,
    currentMatchIndex: 0,
    matches: [...activeMatches, ...completedMatches],
  });

  const mockHandlers = {
    onMatchEnd: vi.fn(),
    onMultipleResultsOpen: vi.fn(),
    onTabChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render active matches when available", () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={mockTournament}
        tournamentLoading={false}
        activeMatches={activeMatches}
        recentlyCompletedMatches={[]}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Check if the active matches section is rendered
    expect(screen.getByText("Aktive Spiele")).toBeInTheDocument();
    expect(screen.getByText("Laufende Matches")).toBeInTheDocument();

    // Check if the correct number of active matches is displayed
    expect(screen.getByText(`${activeMatches.length} aktive Partien`)).toBeInTheDocument();

    // Check if the VS text is displayed for each match
    expect(screen.getAllByText("VS").length).toBe(activeMatches.length);

    // Check if the timer components are rendered
    expect(screen.getAllByTestId("mock-timer").length).toBe(activeMatches.length);
  });

  it("should render completed matches when available", () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={mockTournament}
        tournamentLoading={false}
        activeMatches={[]}
        recentlyCompletedMatches={completedMatches}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Check if the completed matches section is rendered
    expect(screen.getByText("Beendete Spiele")).toBeInTheDocument();
    expect(screen.getByText("Abgeschlossene Matches")).toBeInTheDocument();

    // Check if the correct number of completed matches is displayed
    expect(screen.getByText(`${completedMatches.length} beendete Partien`)).toBeInTheDocument();

    // Check if the match results are displayed
    expect(screen.getByText("3 : 1")).toBeInTheDocument();
    expect(screen.getByText("2 : 0")).toBeInTheDocument();
  });

  it('should show "no active games" message when no matches are available', () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={mockTournament}
        tournamentLoading={false}
        activeMatches={[]}
        recentlyCompletedMatches={[]}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Check if the "no active games" message is displayed
    expect(screen.getAllByText("Keine aktiven Spiele")[0]).toBeInTheDocument();
    expect(screen.getByText("Es sind derzeit keine Spiele aktiv")).toBeInTheDocument();

    // Check if the button to go to schedule is displayed
    const scheduleButton = screen.getByText("Zum Spielplan");
    expect(scheduleButton).toBeInTheDocument();

    // Click the button and check if onTabChange was called
    fireEvent.click(scheduleButton);
    expect(mockHandlers.onTabChange).toHaveBeenCalledWith("schedule");
  });

  it('should show "no tournament" message when tournament is null', () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={null}
        tournamentLoading={false}
        activeMatches={[]}
        recentlyCompletedMatches={[]}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Check if the "no tournament" message is displayed
    expect(screen.getAllByText("Kein Turnier")[0]).toBeInTheDocument();
    expect(screen.getByText("Es wurde noch kein Turnier erstellt")).toBeInTheDocument();

    // Check if the button to go to config is displayed
    const configButton = screen.getByText("Zur Konfiguration");
    expect(configButton).toBeInTheDocument();

    // Click the button and check if onTabChange was called
    fireEvent.click(configButton);
    expect(mockHandlers.onTabChange).toHaveBeenCalledWith("config");
  });

  it("should call onMatchEnd when a match is ended", () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={mockTournament}
        tournamentLoading={false}
        activeMatches={activeMatches}
        recentlyCompletedMatches={[]}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Find and click the "End Match" button in the first timer
    const endMatchButtons = screen.getAllByText("End Match");
    fireEvent.click(endMatchButtons[0]);

    // Check if onMatchEnd was called with the correct match
    expect(mockHandlers.onMatchEnd).toHaveBeenCalledTimes(1);
    expect(mockHandlers.onMatchEnd).toHaveBeenCalledWith(activeMatches[0]);
  });

  it('should call onMultipleResultsOpen when "Alle beenden" button is clicked', () => {
    renderWithProviders(
      <TournamentCurrentGame
        tournament={mockTournament}
        tournamentLoading={false}
        activeMatches={activeMatches}
        recentlyCompletedMatches={[]}
        onMatchEnd={mockHandlers.onMatchEnd}
        onMultipleResultsOpen={mockHandlers.onMultipleResultsOpen}
        onTabChange={mockHandlers.onTabChange}
      />
    );

    // Find and click the "Alle beenden" button
    const endAllButton = screen.getByText("Alle beenden");
    fireEvent.click(endAllButton);

    // Check if onMultipleResultsOpen was called
    expect(mockHandlers.onMultipleResultsOpen).toHaveBeenCalledTimes(1);
  });
});
