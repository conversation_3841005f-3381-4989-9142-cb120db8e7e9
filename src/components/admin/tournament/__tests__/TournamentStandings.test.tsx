import { screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import { TournamentStandings } from "../TournamentStandings";
import { TournamentStanding } from "@/types/tournament";
import { Team } from "@/types";
import { renderWithProviders } from "@/test/testUtils";

// Mock getTeamColor utility
vi.mock("@/utils/teamColors", () => ({
  getTeamColor: () => ({
    bg: "bg-blue-100",
    border: "border-blue-300",
    text: "text-blue-700",
  }),
}));

describe("TournamentStandings", () => {
  const mockTeams: Team[] = [
    { id: "team-1", name: "Team A", players: [], averageRating: 0 },
    { id: "team-2", name: "Team B", players: [], averageRating: 0 },
    { id: "team-3", name: "Team C", players: [], averageRating: 0 },
  ];

  const mockStandings: TournamentStanding[] = [
    {
      team: mockTeams[0],
      played: 2,
      won: 2,
      drawn: 0,
      lost: 0,
      goalsFor: 5,
      goalsAgainst: 1,
      goalDifference: 4,
      points: 6,
    },
    {
      team: mockTeams[1],
      played: 2,
      won: 1,
      drawn: 0,
      lost: 1,
      goalsFor: 3,
      goalsAgainst: 3,
      goalDifference: 0,
      points: 3,
    },
    {
      team: mockTeams[2],
      played: 2,
      won: 0,
      drawn: 0,
      lost: 2,
      goalsFor: 1,
      goalsAgainst: 5,
      goalDifference: -4,
      points: 0,
    },
  ];

  it("should render the standings table with correct data", () => {
    renderWithProviders(<TournamentStandings standings={mockStandings} />);

    // Check if the title is rendered
    expect(screen.getByText("Turniertabelle")).toBeInTheDocument();

    // Check if the description is rendered
    expect(
      screen.getByText("Aktuelle Platzierungen und Statistiken der Teams")
    ).toBeInTheDocument();

    // Check if the table headers are rendered
    expect(screen.getByText("Pos.")).toBeInTheDocument();
    expect(screen.getByText("Team")).toBeInTheDocument();
    expect(screen.getByText("Sp")).toBeInTheDocument();
    expect(screen.getByText("Pkt")).toBeInTheDocument();

    // Check if team names are rendered
    expect(screen.getByText("Team A")).toBeInTheDocument();
    expect(screen.getByText("Team B")).toBeInTheDocument();
    expect(screen.getByText("Team C")).toBeInTheDocument();

    // Check if points are rendered correctly
    const rows = screen.getAllByRole("row");

    // First team (Team A) should have 6 points
    expect(rows[1]).toHaveTextContent("6"); // Header is row 0, Team A is row 1

    // Second team (Team B) should have 3 points
    expect(rows[2]).toHaveTextContent("3");

    // Third team (Team C) should have 0 points
    expect(rows[3]).toHaveTextContent("0");
  });

  it("should highlight the first place team", () => {
    renderWithProviders(<TournamentStandings standings={mockStandings} />);

    // The first place team should have a trophy icon
    const trophyIcons = screen
      .getAllByText("1")
      .filter((el) => el.parentElement?.querySelector("svg.lucide-trophy"));
    expect(trophyIcons.length).toBeGreaterThanOrEqual(1);

    // Check if the first team row has a different background class
    const rows = screen.getAllByRole("row");
    const firstTeamRow = rows[1]; // Header is row 0, Team A is row 1

    // The first place team should have a special background class
    expect(firstTeamRow.className).toContain("bg-amber");
  });

  it("should render empty standings correctly", () => {
    renderWithProviders(<TournamentStandings standings={[]} />);

    // The table should still be rendered
    expect(screen.getByText("Turniertabelle")).toBeInTheDocument();

    // Check if the table headers are rendered
    expect(screen.getByText("Pos.")).toBeInTheDocument();
    expect(screen.getByText("Team")).toBeInTheDocument();

    // There should be no team rows
    const rows = screen.getAllByRole("row");
    expect(rows.length).toBe(1); // Only the header row
  });

  it("should render standings with tied teams correctly", () => {
    // Create standings with tied teams (same points)
    const tiedStandings: TournamentStanding[] = [
      {
        team: mockTeams[0],
        played: 2,
        won: 1,
        drawn: 1,
        lost: 0,
        goalsFor: 3,
        goalsAgainst: 1,
        goalDifference: 2,
        points: 4,
      },
      {
        team: mockTeams[1],
        played: 2,
        won: 1,
        drawn: 1,
        lost: 0,
        goalsFor: 2,
        goalsAgainst: 1,
        goalDifference: 1,
        points: 4,
      },
      {
        team: mockTeams[2],
        played: 2,
        won: 0,
        drawn: 0,
        lost: 2,
        goalsFor: 0,
        goalsAgainst: 3,
        goalDifference: -3,
        points: 0,
      },
    ];

    renderWithProviders(<TournamentStandings standings={tiedStandings} />);

    // Check if the points are rendered correctly
    const rows = screen.getAllByRole("row");

    // First team should have 4 points
    expect(rows[1]).toHaveTextContent("4");

    // Second team should also have 4 points
    expect(rows[2]).toHaveTextContent("4");

    // The first team should be ranked higher due to better goal difference
    expect(rows[1]).toHaveTextContent("Team A");
    expect(rows[2]).toHaveTextContent("Team B");
  });
});
