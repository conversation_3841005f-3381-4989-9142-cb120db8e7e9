import { screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { MultiResultsEntry } from "../MultiResultsEntry";
import { TournamentMatch } from "@/types/tournament";
import { Team } from "@/types";
import { renderWithProviders } from "@/test/testUtils";

// Mock getTeamColor utility
vi.mock("@/utils/teamColors", () => ({
  getTeamColor: () => ({
    bg: "bg-blue-100",
    bgLight: "bg-blue-50",
    border: "border-blue-300",
    text: "text-blue-700",
    textDark: "text-blue-800",
  }),
}));

describe("MultiResultsEntry", () => {
  const mockTeams: Team[] = [
    { id: "team-1", name: "Team A", players: [], averageRating: 0 },
    { id: "team-2", name: "Team B", players: [], averageRating: 0 },
    { id: "team-3", name: "Team C", players: [], averageRating: 0 },
  ];

  const mockMatches: TournamentMatch[] = [
    {
      id: "match-1",
      teamA: mockTeams[0],
      teamB: mockTeams[1],
      goalsA: null,
      goalsB: null,
      isCompleted: false,
      matchNumber: 1,
      field: "Feld 1",
      timeSlot: 1,
      startTime: new Date("2023-01-01T10:00:00"),
    },
    {
      id: "match-2",
      teamA: mockTeams[0],
      teamB: mockTeams[2],
      goalsA: null,
      goalsB: null,
      isCompleted: false,
      matchNumber: 2,
      field: "Feld 2",
      timeSlot: 1,
      startTime: new Date("2023-01-01T10:00:00"),
    },
  ];

  const mockHandlers = {
    onClose: vi.fn(),
    onSave: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the dialog with multiple matches", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Check if the dialog title is rendered
    expect(screen.getByText("Ergebnisse eintragen")).toBeInTheDocument();

    // Check if the description is rendered
    expect(
      screen.getByText("Bitte trage die Ergebnisse für alle beendeten Spiele ein.")
    ).toBeInTheDocument();

    // Check if both matches are rendered
    expect(screen.getByText("Spiel 1 • Feld 1")).toBeInTheDocument();
    expect(screen.getByText("Spiel 2 • Feld 2")).toBeInTheDocument();

    // Check if all team names are rendered
    expect(screen.getAllByText("Team A")).toHaveLength(2); // Team A appears in both matches
    expect(screen.getByText("Team B")).toBeInTheDocument();
    expect(screen.getByText("Team C")).toBeInTheDocument();

    // Check if the buttons are rendered
    expect(screen.getByText("Abbrechen")).toBeInTheDocument();
    expect(screen.getByText("Ergebnisse speichern")).toBeInTheDocument();
  });

  it("should not render when isOpen is false", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={false}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // The dialog should not be visible when isOpen is false
    expect(screen.queryByText("Ergebnisse eintragen")).not.toBeInTheDocument();
  });

  it("should call onClose when cancel button is clicked", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find and click the cancel button
    const cancelButton = screen.getByText("Abbrechen");
    fireEvent.click(cancelButton);

    // Verify that onClose was called
    expect(mockHandlers.onClose).toHaveBeenCalledTimes(1);
  });

  it("should call onSave with correct values when save button is clicked", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find all input fields (4 in total, 2 for each match)
    const inputs = screen.getAllByRole("spinbutton");
    expect(inputs).toHaveLength(4);

    // Set values for the first match
    fireEvent.change(inputs[0], { target: { value: "3" } }); // Goals for Team A in match 1
    fireEvent.change(inputs[1], { target: { value: "1" } }); // Goals for Team B in match 1

    // Set values for the second match
    fireEvent.change(inputs[2], { target: { value: "2" } }); // Goals for Team A in match 2
    fireEvent.change(inputs[3], { target: { value: "2" } }); // Goals for Team C in match 2

    // Find and click the save button
    const saveButton = screen.getByText("Ergebnisse speichern");
    fireEvent.click(saveButton);

    // Verify that onSave was called with the correct values
    expect(mockHandlers.onSave).toHaveBeenCalledWith([
      { matchId: "match-1", goalsA: 3, goalsB: 1 },
      { matchId: "match-2", goalsA: 2, goalsB: 2 },
    ]);

    // Verify that onClose was called after saving
    expect(mockHandlers.onClose).toHaveBeenCalledTimes(1);
  });

  it("should initialize with default values of 0:0", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find all input fields
    const inputs = screen.getAllByRole("spinbutton") as HTMLInputElement[];

    // Verify that all inputs are initialized with 0
    inputs.forEach((input) => {
      expect(input.value).toBe("0");
    });
  });

  it("should handle empty or invalid input values as 0", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Find all input fields
    const inputs = screen.getAllByRole("spinbutton");

    // Set an invalid value (non-numeric)
    fireEvent.change(inputs[0], { target: { value: "abc" } });

    // Find and click the save button
    const saveButton = screen.getByText("Ergebnisse speichern");
    fireEvent.click(saveButton);

    // Verify that onSave was called with 0 for the invalid input
    expect(mockHandlers.onSave).toHaveBeenCalledWith([
      { matchId: "match-1", goalsA: 0, goalsB: 0 },
      { matchId: "match-2", goalsA: 0, goalsB: 0 },
    ]);
  });

  it("should show field information for each match", () => {
    renderWithProviders(
      <MultiResultsEntry
        matches={mockMatches}
        isOpen={true}
        onClose={mockHandlers.onClose}
        onSave={mockHandlers.onSave}
      />
    );

    // Check if field information is displayed for each match
    expect(screen.getByText("Spiel 1 • Feld 1")).toBeInTheDocument();
    expect(screen.getByText("Spiel 2 • Feld 2")).toBeInTheDocument();
  });
});
