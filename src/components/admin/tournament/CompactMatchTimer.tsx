import { useEffect } from "react";
import { useMatchTimer } from "@/hooks/useMatchTimer";
import { Button } from "@/components/ui/button";
import { Play, Pause, RotateCcw, Flag } from "lucide-react";
import { cn } from "@/lib/utils";

interface CompactMatchTimerProps {
  durationMinutes: number;
  onTimeUp?: () => void;
  onMatchEnd?: () => void;
  startTime?: Date;
}

export function CompactMatchTimer({
  durationMinutes,
  onTimeUp,
  onMatchEnd,
  startTime,
}: CompactMatchTimerProps) {
  const { formattedTime, isRunning, isPaused, progress, startTimer, pauseTimer, resetTimer } =
    useMatchTimer({
      initialDurationSeconds: durationMinutes * 60,
      onTimeUp,
      startTime,
    });

  // Play sound when timer is up
  useEffect(() => {
    if (progress === 0 && !isRunning && !isPaused) {
      const audio = new Audio("/sounds/whistle.mp3");
      audio.play().catch((e) => console.error("Error playing sound:", e));
    }
  }, [progress, isRunning, isPaused]);

  // Automatically start timer if startTime is provided
  useEffect(() => {
    if (startTime && !isRunning && !isPaused && progress > 0) {
      startTimer();
    }
  }, [startTime, isRunning, isPaused, progress, startTimer]);

  // Generiere eine eindeutige ID für den Farbverlauf
  const gradientId = `timerGradient-${Math.floor(Math.random() * 10000)}`;

  return (
    <div className="space-y-2">
      {/* Compact Timer Display */}
      <div className="flex justify-center">
        <div className="relative w-28 h-28">
          {/* Circular Progress Background */}
          <div className="absolute inset-0 rounded-full border-4 border-gray-100 dark:border-gray-800"></div>

          {/* Circular Progress Indicator mit Farbverlauf */}
          <svg className="absolute inset-0 w-full h-full rotate-[90deg]" viewBox="0 0 100 100">
            {/* Definiere den Farbverlauf - Blau am Anfang, Rot am Ende */}
            <defs>
              <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" /> {/* Blau (am Anfang) */}
                <stop offset="50%" stopColor="#f97316" /> {/* Orange (Mitte) */}
                <stop offset="100%" stopColor="#ef4444" /> {/* Rot (am Ende) */}
              </linearGradient>
            </defs>
            <circle
              cx="50"
              cy="50"
              r="46"
              fill="none"
              stroke={`url(#${gradientId})`}
              strokeWidth="8"
              strokeDasharray="289.02652413026095"
              strokeDashoffset={289.02652413026095 * (1 - progress / 100)}
              strokeLinecap="round"
            />
          </svg>

          {/* Timer Text */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-2xl font-bold font-mono text-gray-900 dark:text-gray-100 tracking-wider">
              {formattedTime}
            </div>
            <div className="text-xs text-muted-foreground">
              <span className="text-[10px]">Spielzeit</span>
            </div>
          </div>
        </div>
      </div>

      {/* Control Buttons - Nur Icons */}
      <div className="flex justify-center gap-2">
        {!isRunning ? (
          <Button
            onClick={startTimer}
            variant="default"
            size="icon"
            className={cn(
              "h-8 w-8 rounded-full",
              "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
            )}
            title={isPaused ? "Fortsetzen" : "Start"}
          >
            <Play className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            onClick={pauseTimer}
            variant="default"
            size="icon"
            className={cn(
              "h-8 w-8 rounded-full",
              "bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700"
            )}
            title="Pause"
          >
            <Pause className="h-4 w-4" />
          </Button>
        )}

        <Button
          onClick={() => resetTimer()}
          variant="outline"
          size="icon"
          className={cn(
            "h-8 w-8 rounded-full",
            "border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
          )}
          title="Reset"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>

        {onMatchEnd && (
          <Button
            onClick={onMatchEnd}
            variant="destructive"
            size="icon"
            className="h-8 w-8 rounded-full"
            title="Beenden"
          >
            <Flag className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
