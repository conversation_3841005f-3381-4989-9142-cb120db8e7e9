import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TournamentMatch } from "@/types/tournament";
import { Separator } from "@/components/ui/separator";
import { Trophy, Goal } from "lucide-react";
import { getTeamColor } from "@/utils/teamColors";

interface ResultsEntryProps {
  match: TournamentMatch | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (matchId: string, goalsA: number, goalsB: number) => void;
}

export function ResultsEntry({ match, isOpen, onClose, onSave }: ResultsEntryProps) {
  const [goalsA, setGoalsA] = useState<number>(0);
  const [goalsB, setGoalsB] = useState<number>(0);

  // Reset form when match changes
  useEffect(() => {
    if (match) {
      setGoalsA(match.goalsA !== null ? match.goalsA : 0);
      setGoalsB(match.goalsB !== null ? match.goalsB : 0);
    }
  }, [match]);

  const handleSave = () => {
    if (!match) return;
    onSave(match.id, goalsA, goalsB);
    onClose();
  };

  if (!match) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
            <Goal className="h-5 w-5" />
            Spielergebnis eintragen
          </DialogTitle>
          <DialogDescription>
            Trage das Ergebnis für das Spiel {match.matchNumber} ein
          </DialogDescription>
          <Separator />
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="p-4 border border-blue-100 dark:border-blue-900/30 rounded-md bg-blue-50/50 dark:bg-blue-900/10">
            <div className="text-sm text-center text-muted-foreground mb-2">
              Spiel #{match.matchNumber}
            </div>
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="text-center">
                <div className="flex items-center gap-2 mb-1">
                  <div
                    className={`w-3 h-3 rounded-full ${getTeamColor(match.teamA.name).bg} border ${getTeamColor(match.teamA.name).border}`}
                  ></div>
                  <span className={`font-medium ${getTeamColor(match.teamA.name).text}`}>
                    {match.teamA.name}
                  </span>
                </div>
              </div>
              <div className="text-xl font-bold">vs</div>
              <div className="text-center">
                <div className="flex items-center gap-2 mb-1">
                  <div
                    className={`w-3 h-3 rounded-full ${getTeamColor(match.teamB.id).bg} border ${getTeamColor(match.teamB.id).border}`}
                  ></div>
                  <span className={`font-medium ${getTeamColor(match.teamB.id).text}`}>
                    {match.teamB.name}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="goalsA"
                  className={`flex items-center gap-2 ${getTeamColor(match.teamA.id).text}`}
                >
                  <Goal className="h-4 w-4" />
                  Tore {match.teamA.name}
                </Label>
                <Input
                  id="goalsA"
                  type="number"
                  min={0}
                  value={goalsA}
                  onChange={(e) => setGoalsA(parseInt(e.target.value) || 0)}
                  className={`border-${getTeamColor(match.teamA.id).bg.replace("bg-", "")} dark:border-${getTeamColor(match.teamA.id).bg.replace("bg-", "")}/50 focus:ring-${getTeamColor(match.teamA.id).bg.replace("bg-", "")}`}
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="goalsB"
                  className={`flex items-center gap-2 ${getTeamColor(match.teamB.id).text}`}
                >
                  <Goal className="h-4 w-4" />
                  Tore {match.teamB.name}
                </Label>
                <Input
                  id="goalsB"
                  type="number"
                  min={0}
                  value={goalsB}
                  onChange={(e) => setGoalsB(parseInt(e.target.value) || 0)}
                  className={`border-${getTeamColor(match.teamB.id).bg.replace("bg-", "")} dark:border-${getTeamColor(match.teamB.id).bg.replace("bg-", "")}/50 focus:ring-${getTeamColor(match.teamB.id).bg.replace("bg-", "")}`}
                />
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="border-gray-200 dark:border-gray-700"
          >
            Abbrechen
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Speichern
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
