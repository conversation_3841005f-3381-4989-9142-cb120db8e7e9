import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { TournamentMatch } from "@/types/tournament";
import { Users } from "lucide-react";
import { getTeamColor } from "@/utils/teamColors";

interface MultiResultsEntryProps {
  matches: TournamentMatch[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (results: { matchId: string; goalsA: number; goalsB: number }[]) => void;
}

export function MultiResultsEntry({ matches, isOpen, onClose, onSave }: MultiResultsEntryProps) {
  // Initialize results state with default values (0:0)
  const [results, setResults] = useState<{ matchId: string; goalsA: number; goalsB: number }[]>(
    matches.map((match) => ({
      matchId: match.id,
      goalsA: 0,
      goalsB: 0,
    }))
  );

  // Update a specific result
  const updateResult = (index: number, team: "goalsA" | "goalsB", value: string) => {
    const numValue = parseInt(value) || 0;
    setResults((prev) => {
      const newResults = [...prev];
      newResults[index] = {
        ...newResults[index],
        [team]: numValue,
      };
      return newResults;
    });
  };

  // Handle save
  const handleSave = () => {
    onSave(results);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle>Ergebnisse eintragen</DialogTitle>
          <DialogDescription>
            Bitte trage die Ergebnisse für alle beendeten Spiele ein.
          </DialogDescription>
          <Separator className="mt-2" />
        </DialogHeader>

        <div className="space-y-6 py-4 max-h-[60vh] overflow-y-auto">
          {matches.map((match, index) => (
            <div key={match.id} className="space-y-4">
              {index > 0 && <Separator />}

              <div className="flex items-center justify-between">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Spiel {index + 1} • {match.field}
                </div>
              </div>

              <div className="flex items-center justify-between gap-4">
                {/* Team A */}
                <div className="flex items-center gap-2 flex-1">
                  <div
                    className={`w-10 h-10 rounded-full ${getTeamColor(match.teamA.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamA.id).border} ${getTeamColor(match.teamA.id).bg}`}
                  >
                    <Users className={`h-5 w-5 ${getTeamColor(match.teamA.id).textDark}`} />
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${getTeamColor(match.teamA.id).text}`}>
                      {match.teamA.name}
                    </div>
                  </div>
                </div>

                {/* Score Inputs */}
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="0"
                    max="99"
                    value={results[index].goalsA}
                    onChange={(e) => updateResult(index, "goalsA", e.target.value)}
                    className="w-14 h-10 text-center"
                  />
                  <span className="text-lg font-bold">:</span>
                  <Input
                    type="number"
                    min="0"
                    max="99"
                    value={results[index].goalsB}
                    onChange={(e) => updateResult(index, "goalsB", e.target.value)}
                    className="w-14 h-10 text-center"
                  />
                </div>

                {/* Team B */}
                <div className="flex items-center gap-2 flex-1 justify-end">
                  <div className="flex-1 text-right">
                    <div className={`text-sm font-medium ${getTeamColor(match.teamB.id).text}`}>
                      {match.teamB.name}
                    </div>
                  </div>
                  <div
                    className={`w-10 h-10 rounded-full ${getTeamColor(match.teamB.id).bgLight} flex items-center justify-center border ${getTeamColor(match.teamB.id).border} ${getTeamColor(match.teamB.id).bg}`}
                  >
                    <Users className={`h-5 w-5 ${getTeamColor(match.teamB.id).textDark}`} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={onClose}>
            Abbrechen
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Ergebnisse speichern
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
