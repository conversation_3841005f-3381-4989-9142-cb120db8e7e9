import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/custom-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TournamentConfig } from "@/types/tournament";
import { Team, GameSession } from "@/types";
import { Separator } from "@/components/ui/separator";
import {
  CalendarClock,
  Clock,
  Hourglass,
  AlertCircle,
  Trophy,
  Users,
  Calendar,
  Timer,
  Goal,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useGameSessions } from "@/hooks/useGameSessions";

interface TournamentConfigProps {
  teams: Team[];
  onCreateTournament: (config: TournamentConfig) => Promise<void>;
  currentSession?: GameSession | null;
}

export function TournamentConfigForm({
  teams,
  onCreateTournament,
  currentSession,
}: TournamentConfigProps) {
  // Get current game session if not provided
  const { currentSession: fetchedSession } = useGameSessions({
    fetchCurrent: !currentSession,
  });

  // Use the provided session or the fetched one
  const session = currentSession || fetchedSession;

  // Initialize totalDuration from session duration_minutes or default to 120
  const [totalDuration, setTotalDuration] = useState(session?.duration_minutes || 120);
  const [matchDuration, setMatchDuration] = useState(10);
  const [breakDuration, setBreakDuration] = useState(3);
  const [numberOfFields, setNumberOfFields] = useState(1);

  // Update totalDuration when session changes
  useEffect(() => {
    if (session?.duration_minutes) {
      setTotalDuration(session.duration_minutes);
    }
  }, [session]);

  const numberOfTeams = teams.length;
  const totalNumberOfMatches = (numberOfTeams * (numberOfTeams - 1)) / 2;

  // Determine max number of fields based on team count
  const getMaxFields = () => {
    if (numberOfTeams === 3) return 1;
    if (numberOfTeams === 4) return 2;
    return 4;
  };

  // Adjust number of fields if needed when team count changes
  useEffect(() => {
    const maxFields = getMaxFields();
    if (numberOfFields > maxFields) {
      setNumberOfFields(maxFields);
    }

    // For 4 teams, default to 2 fields
    if (numberOfTeams === 4 && numberOfFields < 2) {
      setNumberOfFields(2);
    }
  }, [numberOfTeams]);

  // Calculate maximum possible matches based on total time, match duration, and break time
  const calculatePossibleMatches = () => {
    if (matchDuration <= 0) return 0;

    // Time for one match cycle (match + break)
    const matchCycleDuration = matchDuration + breakDuration;

    // How many match cycles can fit in the total duration
    // We add one break duration because the last match doesn't need a break after it
    const possibleMatchCycles = Math.floor((totalDuration + breakDuration) / matchCycleDuration);

    // Adjust for multiple fields
    return possibleMatchCycles * numberOfFields;
  };

  const possibleMatches = calculatePossibleMatches();
  const canPlayAllMatches = possibleMatches >= totalNumberOfMatches;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const config: TournamentConfig = {
      totalDurationMinutes: totalDuration,
      breakBetweenMatchesMinutes: breakDuration,
      matchDurationMinutes: matchDuration,
      numberOfTeams,
      numberOfFields,
    };

    await onCreateTournament(config);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <CardTitle>Turnierkonfiguration</CardTitle>
            <CardDescription>Konfiguriere die Parameter für den Turniermodus</CardDescription>
          </div>
        </div>
        <Separator className="mt-3" />
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalDuration" className="flex items-center gap-2">
                <CalendarClock className="h-4 w-4" />
                Gesamtdauer (Minuten)
              </Label>
              <Input
                id="totalDuration"
                type="number"
                min={30}
                max={240}
                value={totalDuration}
                readOnly
                className="bg-gray-50 dark:bg-gray-800 cursor-not-allowed"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                Die Gesamtdauer wird automatisch aus der Spielsitzung übernommen.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="matchDuration" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Spieldauer pro Spiel (Minuten)
              </Label>
              <Input
                id="matchDuration"
                type="number"
                min={5}
                max={30}
                value={matchDuration}
                onChange={(e) => setMatchDuration(parseInt(e.target.value) || 5)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="breakDuration" className="flex items-center gap-2">
                <Hourglass className="h-4 w-4" />
                Pause zwischen Spielen (Minuten)
              </Label>
              <Input
                id="breakDuration"
                type="number"
                min={1}
                max={10}
                value={breakDuration}
                onChange={(e) => setBreakDuration(parseInt(e.target.value) || 1)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fields" className="flex items-center gap-2">
                Anzahl der Spielfelder
              </Label>
              <Input
                id="fields"
                type="number"
                min={1}
                max={getMaxFields()}
                value={numberOfFields}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 1;
                  const maxFields = getMaxFields();
                  setNumberOfFields(Math.min(value, maxFields));
                }}
                required
              />
              {numberOfTeams === 3 && (
                <p className="text-xs text-muted-foreground mt-1">
                  Bei 3 Teams ist nur 1 Spielfeld möglich.
                </p>
              )}
              {numberOfTeams === 4 && (
                <p className="text-xs text-muted-foreground mt-1">
                  Bei 4 Teams werden üblicherweise 2 Spielfelder verwendet.
                </p>
              )}
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-300">
              Turnierübersicht:
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {/* Teams */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Teams:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{numberOfTeams} Teams</p>
                </div>
              </div>

              {/* Required Matches */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm">
                  <Goal className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Benötigte Spiele:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {totalNumberOfMatches} Spiele insgesamt
                  </p>
                </div>
              </div>

              {/* Possible Matches */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div
                  className={`${
                    !canPlayAllMatches && possibleMatches > 0
                      ? "bg-amber-100 dark:bg-amber-900/30"
                      : "bg-blue-100 dark:bg-blue-900/30"
                  } p-1.5 rounded-full shadow-sm`}
                >
                  <Calendar
                    className={`h-5 w-5 ${
                      !canPlayAllMatches && possibleMatches > 0
                        ? "text-amber-600 dark:text-amber-400"
                        : "text-blue-600 dark:text-blue-400"
                    }`}
                  />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Mögliche Spiele:</p>
                  <p
                    className={`text-sm ${
                      !canPlayAllMatches && possibleMatches > 0
                        ? "text-amber-600 dark:text-amber-400"
                        : "text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    {possibleMatches} Spiele möglich
                  </p>
                </div>
              </div>

              {/* Total Duration */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full shadow-sm">
                  <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Gesamtdauer:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {totalDuration} Minuten
                  </p>
                </div>
              </div>

              {/* Match Duration */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full shadow-sm">
                  <Timer className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Spieldauer:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {matchDuration} Minuten pro Spiel
                  </p>
                </div>
              </div>

              {/* Break Duration */}
              <div className="flex items-center gap-3 bg-gray-50 dark:bg-zinc-800 p-3 rounded-lg">
                <div className="bg-team-primary/20 dark:bg-gray-900/40 p-1.5 rounded-full shadow-sm">
                  <Hourglass className="h-5 w-5 text-team-primary dark:text-gray-400/90" />
                </div>
                <div>
                  <p className="text-sm font-semibold dark:text-white">Pause zwischen Spielen:</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {breakDuration} Minuten
                  </p>
                </div>
              </div>
            </div>
          </div>

          {!canPlayAllMatches && possibleMatches > 0 && (
            <Alert className="border-amber-200 bg-amber-50/50 text-amber-800 dark:border-amber-900/30 dark:bg-amber-900/10 dark:text-amber-300">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Mit den aktuellen Einstellungen können nur {possibleMatches} von{" "}
                {totalNumberOfMatches} benötigten Spielen durchgeführt werden. Erhöhe die
                Gesamtdauer, reduziere die Spieldauer pro Spiel, oder füge mehr Spielfelder hinzu.
              </AlertDescription>
            </Alert>
          )}

          {possibleMatches === 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Mit den aktuellen Einstellungen können keine Spiele durchgeführt werden. Bitte passe
                die Parameter an.
              </AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
            disabled={numberOfTeams < 2 || possibleMatches === 0}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Turnierplan erstellen
          </Button>

          {numberOfTeams < 2 && (
            <p className="text-sm text-red-500">
              Mindestens 2 Teams werden benötigt, um ein Turnier zu erstellen.
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
