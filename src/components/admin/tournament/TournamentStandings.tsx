import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/custom-card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TournamentStanding } from "@/types/tournament";
import { Trophy } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { getTeamColor } from "@/utils/teamColors";

interface TournamentStandingsProps {
  standings: TournamentStanding[];
}

export function TournamentStandings({ standings }: TournamentStandingsProps) {
  // We now use the global getTeamColor function from teamColors.ts
  return (
    <Card>
      <CardHeader>
        <div>
          <CardTitle>Turniertabelle</CardTitle>
          <CardDescription>Aktuelle Platzierungen und Statistiken der Teams</CardDescription>
        </div>
        <Separator className="mt-3" />
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-blue-50/50 dark:bg-blue-900/20">
                <TableHead className="w-[50px]">Pos.</TableHead>
                <TableHead>Team</TableHead>
                <TableHead className="text-center">Sp</TableHead>
                <TableHead className="text-center hidden sm:table-cell">S</TableHead>
                <TableHead className="text-center hidden md:table-cell">U</TableHead>
                <TableHead className="text-center hidden md:table-cell">N</TableHead>
                <TableHead className="text-center hidden sm:table-cell">Tore</TableHead>
                <TableHead className="text-center hidden lg:table-cell">Diff</TableHead>
                <TableHead className="text-center">Pkt</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {standings.map((standing, index) => (
                <TableRow
                  key={standing.team.id}
                  className={
                    index === 0
                      ? "font-medium bg-amber-50/50 dark:bg-amber-900/20"
                      : index === 1
                        ? "bg-gray-50/50 dark:bg-gray-800/20"
                        : index === 2
                          ? "bg-orange-50/30 dark:bg-orange-900/10"
                          : ""
                  }
                >
                  <TableCell className="font-medium">
                    {index === 0 ? (
                      <span className="flex items-center gap-1">
                        <Trophy className="h-4 w-4 text-amber-500" />
                        {index + 1}
                      </span>
                    ) : (
                      index + 1
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-3 h-3 rounded-full ${getTeamColor(standing.team.id).bg} border ${getTeamColor(standing.team.id).border}`}
                      ></div>
                      <span className={`font-medium ${getTeamColor(standing.team.id).text}`}>
                        {standing.team.name}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">{standing.played}</TableCell>
                  <TableCell className="text-center hidden sm:table-cell">{standing.won}</TableCell>
                  <TableCell className="text-center hidden md:table-cell">
                    {standing.drawn}
                  </TableCell>
                  <TableCell className="text-center hidden md:table-cell">
                    {standing.lost}
                  </TableCell>
                  <TableCell className="text-center hidden sm:table-cell">
                    {standing.goalsFor}:{standing.goalsAgainst}
                  </TableCell>
                  <TableCell className="text-center hidden lg:table-cell">
                    <span
                      className={
                        standing.goalDifference > 0
                          ? "text-green-600 dark:text-green-400"
                          : standing.goalDifference < 0
                            ? "text-red-600 dark:text-red-400"
                            : ""
                      }
                    >
                      {standing.goalDifference > 0
                        ? `+${standing.goalDifference}`
                        : standing.goalDifference}
                    </span>
                  </TableCell>
                  <TableCell className="text-center font-bold">{standing.points}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
