import React, { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/custom-card";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Team, Player } from "@/types";
import { Users, Check, RefreshCw, Edit, Save } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface TeamEditorProps {
  teams: Team[];
  onApproveTeams: (teams: Team[]) => Promise<void>;
  onRegenerateTeams: () => Promise<void>;
  isLoading: boolean;
  isPendingApproval: boolean;
}

export function TeamEditor({
  teams,
  onApproveTeams,
  onRegenerateTeams,
  isLoading,
  isPendingApproval,
}: TeamEditorProps) {
  const [editableTeams, setEditableTeams] = useState<Team[]>([]);
  const [isApproving, setIsApproving] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);

  useEffect(() => {
    if (teams && teams.length > 0) {
      setEditableTeams(JSON.parse(JSON.stringify(teams)));
    }
  }, [teams]);

  const handleTeamNameChange = (teamIndex: number, newName: string) => {
    const updatedTeams = [...editableTeams];
    updatedTeams[teamIndex].name = newName;
    setEditableTeams(updatedTeams);
  };

  const handlePlayerTeamChange = (
    playerId: string,
    currentTeamIndex: number,
    newTeamIndex: number
  ) => {
    if (currentTeamIndex === newTeamIndex) return;

    const updatedTeams = [...editableTeams];

    const playerIndex = updatedTeams[currentTeamIndex].players.findIndex((p) => p.id === playerId);
    if (playerIndex === -1) return;

    const player = { ...updatedTeams[currentTeamIndex].players[playerIndex] };
    updatedTeams[currentTeamIndex].players.splice(playerIndex, 1);
    updatedTeams[newTeamIndex].players.push(player);

    updateTeamAverageRatings(updatedTeams);

    setEditableTeams(updatedTeams);
  };

  const updateTeamAverageRatings = (teams: Team[]) => {
    teams.forEach((team) => {
      if (team.players.length > 0) {
        const totalRating = team.players.reduce((sum, player) => sum + player.rating, 0);
        team.averageRating = totalRating / team.players.length;
      } else {
        team.averageRating = 0;
      }
    });
  };

  const handleApproveTeams = async () => {
    setIsApproving(true);
    try {
      await onApproveTeams(editableTeams);
      toast({
        title: "Teams gespeichert",
        description: "Die Teams wurden erfolgreich gespeichert und sind jetzt für alle sichtbar.",
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Beim Speichern der Teams ist ein Fehler aufgetreten.",
        variant: "destructive",
      });
      console.error("Error approving teams:", error);
    } finally {
      setIsApproving(false);
    }
  };

  const handleRegenerateTeams = async () => {
    setIsRegenerating(true);
    try {
      await onRegenerateTeams();
      toast({
        title: "Teams neu generiert",
        description:
          "Die Teams wurden neu generiert. Bitte überprüfen und genehmigen Sie die neuen Teams.",
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Beim Regenerieren der Teams ist ein Fehler aufgetreten.",
        variant: "destructive",
      });
      console.error("Error regenerating teams:", error);
    } finally {
      setIsRegenerating(false);
    }
  };

  if (isLoading || isLoadingTeams) {
    return (
      <Card className="dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Teams bearbeiten</CardTitle>
            <CardDescription>Laden der Team-Informationen...</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3 flex justify-center items-center min-h-[200px]">
          <LoadingSpinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (editableTeams.length === 0) {
    return (
      <Card className="dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader>
          <div className="space-y-1">
            <CardTitle>Teams bearbeiten</CardTitle>
            <CardDescription>Keine Teams zum Bearbeiten verfügbar</CardDescription>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="pt-3 space-y-4">
          <div className="text-center p-6 bg-gray-50 dark:bg-zinc-800 rounded-md">
            <p className="text-gray-600 dark:text-zinc-300 mb-2">
              Keine Teams zum Bearbeiten verfügbar.
            </p>
            <p className="text-gray-500 dark:text-zinc-400 text-sm">
              Generieren Sie zuerst Teams, um sie hier bearbeiten zu können.
            </p>
            <div className="mt-4">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    disabled={isRegenerating}
                    className="bg-team-primary hover:bg-team-primary/90 dark:bg-team-primary/80 dark:hover:bg-team-primary"
                  >
                    {isRegenerating ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Teams werden generiert...
                      </>
                    ) : (
                      <>
                        <Users className="h-4 w-4 mr-2" />
                        Teams generieren
                      </>
                    )}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="dark:text-white">
                      Teams generieren?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="dark:text-zinc-400">
                      Diese Aktion generiert automatisch Teams basierend auf den Spielerbewertungen.
                      Sind Sie sicher, dass Sie fortfahren möchten?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
                      Abbrechen
                    </AlertDialogCancel>
                    <AlertDialogAction onClick={onRegenerateTeams}>
                      Ja, Teams generieren
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <div className="space-y-1">
          <CardTitle className="dark:text-white">Teams bearbeiten</CardTitle>
          <CardDescription className="dark:text-zinc-400">
            Passen Sie die generierten Teams an und speichern Sie die Änderungen
          </CardDescription>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>
      <CardContent className="pt-3 space-y-4">
        <div className="mb-4 bg-amber-50 dark:bg-amber-950/30 p-3 rounded-md border border-amber-200 dark:border-amber-800 text-amber-800 dark:text-amber-400">
          <p className="text-sm">
            <strong>Hinweis:</strong> Die Teams werden erst nach dem Klicken auf "Teams speichern"
            in der Datenbank gespeichert und für alle Benutzer sichtbar.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {editableTeams.map((team, teamIndex) => (
            <div key={team.id} className="border rounded-md overflow-hidden dark:border-zinc-700">
              <div className="p-3 border-b bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700 flex justify-between items-center">
                <Input
                  value={team.name}
                  onChange={(e) => handleTeamNameChange(teamIndex, e.target.value)}
                  className="max-w-[200px] font-medium dark:bg-zinc-700 dark:border-zinc-600 dark:text-white"
                />
                <Badge
                  variant="outline"
                  className="bg-white dark:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-600"
                >
                  Ø {team.averageRating.toFixed(1)}
                </Badge>
              </div>
              <ul className="divide-y dark:divide-zinc-700">
                {team.players.map((player) => (
                  <li
                    key={player.id}
                    className="p-3 flex justify-between items-center dark:text-zinc-300"
                  >
                    <div className="flex items-center gap-2">
                      <span>{player.name}</span>
                      {player.jerseyNumber && (
                        <span className="text-xs opacity-70 dark:opacity-50">
                          #{player.jerseyNumber}
                        </span>
                      )}
                      <span className="text-xs bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded-full">
                        {player.rating}
                      </span>
                    </div>
                    <Select
                      value={teamIndex.toString()}
                      onValueChange={(value) =>
                        handlePlayerTeamChange(player.id, teamIndex, parseInt(value))
                      }
                    >
                      <SelectTrigger className="w-[120px] dark:bg-zinc-700 dark:border-zinc-600 dark:text-zinc-300">
                        <SelectValue placeholder="Team wählen" />
                      </SelectTrigger>
                      <SelectContent className="dark:bg-zinc-800 dark:border-zinc-700">
                        {editableTeams.map((t, idx) => (
                          <SelectItem
                            key={idx}
                            value={idx.toString()}
                            disabled={idx === teamIndex}
                            className="dark:text-zinc-300 dark:focus:bg-zinc-700 dark:data-[state=checked]:text-white"
                          >
                            {t.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="flex flex-wrap gap-2 justify-end">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                disabled={isRegenerating || isApproving}
                className="flex items-center gap-1 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-300 dark:hover:bg-zinc-700"
              >
                {isRegenerating ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Regeneriere...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Teams neu generieren
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
              <AlertDialogHeader>
                <AlertDialogTitle className="dark:text-white">
                  Teams neu generieren?
                </AlertDialogTitle>
                <AlertDialogDescription className="dark:text-zinc-400">
                  Diese Aktion wird neue Teams auf Basis der aktuellen Spieler generieren. Alle
                  nicht gespeicherten Änderungen werden verworfen. Möchten Sie fortfahren?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
                  Abbrechen
                </AlertDialogCancel>
                <AlertDialogAction onClick={handleRegenerateTeams}>
                  Ja, Teams neu generieren
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                disabled={isApproving || isRegenerating}
                className="bg-team-accent text-white hover:bg-team-accent/80 dark:bg-team-accent/80 dark:hover:bg-team-accent"
              >
                {isApproving ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Speichere...
                  </>
                ) : (
                  <>
                    {isPendingApproval ? (
                      <>
                        <Save className="h-4 w-4 mr-1" />
                        Teams speichern
                      </>
                    ) : (
                      <>
                        <Edit className="h-4 w-4 mr-1" />
                        Änderungen speichern
                      </>
                    )}
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
              <AlertDialogHeader>
                <AlertDialogTitle className="dark:text-white">
                  {isPendingApproval ? "Teams speichern?" : "Änderungen speichern?"}
                </AlertDialogTitle>
                <AlertDialogDescription className="dark:text-zinc-400">
                  {isPendingApproval
                    ? "Die generierten Teams werden in der Datenbank gespeichert und für alle Spieler sichtbar sein. Möchten Sie fortfahren?"
                    : "Ihre Änderungen an den Teams werden gespeichert und für alle Spieler sichtbar sein. Möchten Sie fortfahren?"}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
                  Abbrechen
                </AlertDialogCancel>
                <AlertDialogAction onClick={handleApproveTeams}>
                  {isPendingApproval ? "Ja, Teams speichern" : "Ja, Änderungen speichern"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
}
