import { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  CardDescription,
} from "@/components/ui/custom-card";
import { AdminPanelLayout } from "./AdminPanelLayout";
import { MatchStatusCard } from "./MatchStatusCard";
import { GamePlanningCard } from "./GamePlanningCard";
import { TeamEditor } from "./TeamEditor";
import { useGameSessions } from "@/hooks/useGameSessions";
import { usePlayers } from "@/hooks/usePlayers";
import { useTeamEditor } from "@/hooks/useTeamEditor";
import { useTeamSettings } from "@/hooks/useTeamSettings";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { getNextFriday } from "@/utils/dateUtils";
import { format } from "date-fns";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import LoadingSpinner from "@/components/LoadingSpinner";
import GameSessionMatchResults from "./GameSessionMatchResults";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Trophy } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {
  notifyGameRegistrationOpened,
  notifyGameRegistrationClosed,
  notifyTeamsGenerated,
} from "@/services/whatsappService";

const GameManagementPage = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(localStorage.getItem("isAdminLoggedIn") === "true");

  const {
    currentSession,
    updateGameSession,
    createGameSession,
    loading: sessionLoading,
    refetchAll,
  } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });

  const { players } = usePlayers(currentSession?.id);

  const { allow3Teams, duration2Teams, duration3Teams, duration4Teams } = useTeamSettings();

  const [gameDate, setGameDate] = useState<Date>(getNextFriday());
  const [gameTime, setGameTime] = useState("21:00");
  const [isCancelingMatch, setIsCancelingMatch] = useState(false);
  const [isMarkingAsPlayed, setIsMarkingAsPlayed] = useState(false);
  const [isSignupOpen, setIsSignupOpen] = useState(currentSession?.isSignupOpen || false);

  // Update isSignupOpen when currentSession changes
  useEffect(() => {
    if (currentSession) {
      setIsSignupOpen(currentSession.isSignupOpen);
      if (currentSession.date) {
        setGameDate(currentSession.date);
        setGameTime(format(currentSession.date, "HH:mm"));
      }
    }
  }, [currentSession]);

  const confirmedPlayers = players.filter((p) => p.status === "in");

  // Define inPlayersCount based on confirmed players
  const inPlayersCount = confirmedPlayers.length;

  const {
    generatedTeams,
    isPendingApproval,
    isGeneratingTeams,
    hasPersistedTeams,
    isLoadingTeams,
    generateTeamsFromPlayers,
    approveTeams,
    setIsPendingApproval,
  } = useTeamEditor(currentSession?.id, confirmedPlayers);

  const isGameScheduled = currentSession?.status === "scheduled";
  const canGenerateTeams = isGameScheduled && confirmedPlayers.length >= 10 && !isSignupOpen;
  const showMarkAsPlayedButton =
    isGameScheduled && !isSignupOpen && (currentSession?.isTeamGenerated || hasPersistedTeams);

  let gameFormation = "";
  let matchDuration = duration2Teams;

  if (inPlayersCount < 10) {
    gameFormation = "Noch nicht genug Spieler";
    matchDuration = duration2Teams;
  } else if (inPlayersCount === 10) {
    gameFormation = "2 Teams (5v5)";
    matchDuration = duration2Teams;
  } else if (inPlayersCount >= 11 && inPlayersCount <= 14) {
    gameFormation = `2 Teams + ${inPlayersCount - 10} Reserve`;
    matchDuration = duration2Teams;
  } else if (inPlayersCount >= 15 && inPlayersCount <= 19) {
    if (allow3Teams) {
      gameFormation = "3 Teams Rotation (5v5v5)";
      matchDuration = duration3Teams;
    } else {
      gameFormation = `2 Teams + ${inPlayersCount - 10} Reserve (3-Team deaktiviert)`;
      matchDuration = duration2Teams;
    }
  } else if (inPlayersCount >= 20) {
    gameFormation = "4 Teams doppelte Rotation";
    matchDuration = duration4Teams;
  }

  const handleToggleSignup = async () => {
    if (!currentSession) {
      toast({
        title: "Keine aktive Spielsitzung",
        description: "Bitte erstellen Sie zuerst eine Spielsitzung",
        variant: "destructive",
      });
      return;
    }

    const newSignupState = !isSignupOpen;

    setIsSignupOpen(newSignupState);

    const updated = await updateGameSession(currentSession.id!, {
      isSignupOpen: newSignupState,
    });

    if (updated) {
      toast({
        title: newSignupState ? "Anmeldung geöffnet" : "Anmeldung geschlossen",
        description: newSignupState
          ? "Spieler können sich jetzt für das Spiel anmelden"
          : "Spieler können sich nicht mehr für das Spiel anmelden",
        variant: newSignupState ? "default" : "destructive",
      });

      // Send WhatsApp notification
      try {
        if (newSignupState) {
          await notifyGameRegistrationOpened(currentSession);
        } else {
          await notifyGameRegistrationClosed(currentSession);
        }
      } catch (error) {
        console.error("Failed to send WhatsApp notification:", error);
        // Don't show error to user as it's not critical functionality
      }
    } else {
      setIsSignupOpen(!newSignupState);
    }
  };

  const handleGenerateTeams = async () => {
    if (!currentSession?.id) {
      toast({
        title: "Keine aktive Spielsitzung",
        description: "Bitte erstellen Sie zuerst eine Spielsitzung",
        variant: "destructive",
      });
      return;
    }

    if (confirmedPlayers.length < 10) {
      toast({
        title: "Nicht genug Spieler",
        description: "Es müssen mindestens 10 Spieler angemeldet sein, um Teams zu generieren",
        variant: "destructive",
      });
      return;
    }

    console.log(
      `Starting team generation for session ${currentSession.id} with ${confirmedPlayers.length} players.`
    );

    try {
      setIsSignupOpen(false);
      await updateGameSession(currentSession.id, {
        isSignupOpen: false,
      });

      const generatedTeams = await generateTeamsFromPlayers();
      console.log(
        `Teams generated: ${generatedTeams.length} teams with status isPendingApproval=${isPendingApproval}`
      );

      // After generation, check if the teams are visible in the UI
      console.log(
        `Current UI state: shouldShowTeamEditor=${isGameScheduled && (generatedTeams.length > 0 || hasPersistedTeams || currentSession?.isTeamGenerated)}`
      );
      console.log(`- isGameScheduled=${isGameScheduled}`);
      console.log(`- generatedTeams.length=${generatedTeams.length}`);
      console.log(`- hasPersistedTeams=${hasPersistedTeams}`);
      console.log(`- currentSession?.isTeamGenerated=${currentSession?.isTeamGenerated}`);

      // No auto-approval, always show message to save teams manually
      toast({
        title: "Teams generiert",
        description:
          "Die Teams wurden generiert. Bitte überprüfen und speichern Sie sie unten, um sie zu veröffentlichen.",
      });
    } catch (error) {
      console.error("Error generating teams:", error);
      toast({
        title: "Fehler",
        description: "Es gab ein Problem bei der Generierung der Teams",
        variant: "destructive",
      });
    }
  };

  const handleCancelMatch = async () => {
    if (!currentSession?.id) {
      toast({
        title: "Keine aktive Spielsitzung",
        description: "Es gibt keine aktive Spielsitzung zum Abbrechen",
        variant: "destructive",
      });
      return;
    }

    setIsCancelingMatch(true);

    try {
      await supabase.from("team_players").delete().eq("game_session_id", currentSession.id);

      await supabase.from("teams").delete().eq("game_session_id", currentSession.id);

      await supabase.from("player_signups").delete().eq("game_session_id", currentSession.id);

      await updateGameSession(currentSession.id, {
        isSignupOpen: false,
        status: "cancelled",
      });

      setIsSignupOpen(false);

      toast({
        title: "Spiel abgebrochen",
        description: "Das Spiel wurde erfolgreich abgebrochen und archiviert",
      });
    } catch (error) {
      console.error("Error canceling match:", error);
      toast({
        title: "Fehler",
        description: "Es gab ein Problem beim Abbrechen des Spiels",
        variant: "destructive",
      });
    } finally {
      setIsCancelingMatch(false);
    }
  };

  const handleMarkAsPlayed = async () => {
    if (!currentSession?.id) {
      toast({
        title: "Keine aktive Spielsitzung",
        description: "Es gibt keine aktive Spielsitzung zum Markieren als gespielt",
        variant: "destructive",
      });
      return;
    }

    // Prüfen, ob Teams verfügbar sind
    if (teamsForResults.length === 0) {
      toast({
        title: "Keine Teams verfügbar",
        description:
          "Es müssen zuerst Teams generiert werden, bevor das Spiel als gespielt markiert werden kann",
        variant: "destructive",
      });
      return;
    }

    // Direkt das Modal zum Eintragen der Ergebnisse öffnen
    setShowResultsModal(true);
  };

  // Neue Funktion zum tatsächlichen Markieren des Spiels als gespielt
  const finalizeMarkAsPlayed = async () => {
    if (!currentSession?.id) return;

    setIsMarkingAsPlayed(true);

    try {
      const updated = await updateGameSession(currentSession.id, {
        isSignupOpen: false,
        status: "played",
      });

      setIsSignupOpen(false);

      if (updated) {
        toast({
          title: "Spiel als gespielt markiert",
          description:
            "Das Spiel wurde erfolgreich als gespielt markiert und ist jetzt in der Historie verfügbar",
        });
      } else {
        throw new Error("Failed to mark match as played");
      }
    } catch (error) {
      console.error("Error marking match as played:", error);
      toast({
        title: "Fehler",
        description: "Es gab ein Problem beim Markieren des Spiels als gespielt",
        variant: "destructive",
      });
    } finally {
      setIsMarkingAsPlayed(false);
    }
  };

  const saveGameDateTime = async () => {
    const [hours, minutes] = gameTime.split(":").map(Number);
    const fullGameDate = new Date(gameDate);
    fullGameDate.setHours(hours, minutes, 0, 0);

    const signupOpensAt = new Date(fullGameDate);
    signupOpensAt.setDate(signupOpensAt.getDate() - 4);
    signupOpensAt.setHours(12, 0, 0, 0);

    if (currentSession) {
      const updated = await updateGameSession(currentSession.id!, {
        date: fullGameDate,
        signupOpensAt,
      });

      if (updated) {
        toast({
          title: "Spieldetails aktualisiert",
          description: `Nächstes Spiel geplant für ${fullGameDate.toLocaleDateString()} um ${gameTime}`,
        });

        // Use refetchAll to update state properly
        refetchAll();
      }
    } else {
      const created = await createGameSession({
        date: fullGameDate,
        signupOpensAt: signupOpensAt,
        isSignupOpen: false,
        isTeamGenerated: false,
      });

      if (created) {
        toast({
          title: "Neue Spielsitzung erstellt",
          description: `Neues Spiel geplant für ${fullGameDate.toLocaleDateString()} um ${gameTime}. Alle vorherigen Spiele wurden archiviert.`,
        });

        // Use refetchAll to update state properly
        refetchAll();
      }
    }
  };

  const handleRegenerateTeams = async () => {
    await generateTeamsFromPlayers();
  };

  const handleApproveTeams = async (teamsToApprove) => {
    console.log(`Approving ${teamsToApprove.length} teams for session ${currentSession?.id}`);
    try {
      // Disable re-fetches during approval process to prevent multiple renders
      await approveTeams(teamsToApprove);
      console.log(`Teams approval completed successfully`);

      // Send WhatsApp notification with team information
      try {
        if (currentSession) {
          await notifyTeamsGenerated(currentSession, teamsToApprove);
        }
      } catch (error) {
        console.error("Failed to send WhatsApp notification for teams:", error);
        // Don't show error to user as it's not critical functionality
      }

      return;
    } catch (error) {
      console.error("Error in handleApproveTeams:", error);
      throw error;
    }
  };

  const shouldShowTeamEditor =
    isGameScheduled &&
    (generatedTeams.length > 0 || hasPersistedTeams || currentSession?.isTeamGenerated);

  // Replace all the noisy logging with a single, more informative log when conditions change
  useEffect(() => {
    const teamEditorState = {
      shouldShowTeamEditor,
      isGameScheduled,
      generatedTeamsLength: generatedTeams.length,
      hasPersistedTeams,
      isTeamGenerated: currentSession?.isTeamGenerated,
      isPendingApproval,
    };

    // Only log when any values change to reduce noise
    console.debug("Team editor state updated:", teamEditorState);
  }, [
    shouldShowTeamEditor,
    isGameScheduled,
    generatedTeams.length,
    hasPersistedTeams,
    currentSession?.isTeamGenerated,
    isPendingApproval,
  ]);

  // Helper to get teams for the current session (persisted or generated)
  const sessionTeams =
    hasPersistedTeams &&
    generatedTeams.length === 0 &&
    currentSession?.isTeamGenerated &&
    currentSession?.id
      ? Array.isArray(currentSession.teams)
        ? currentSession.teams
        : []
      : generatedTeams;
  const teamsForResults = sessionTeams.map((t) => ({ id: t.id, name: t.name }));

  // Modal state for match results
  const [showResultsModal, setShowResultsModal] = useState(false);
  const prevSessionStatus = useRef<string | undefined>(undefined);

  useEffect(() => {
    if (
      currentSession?.status === "played" &&
      prevSessionStatus.current !== "played" &&
      currentSession?.id &&
      teamsForResults.length > 0
    ) {
      setShowResultsModal(true);
    }
    prevSessionStatus.current = currentSession?.status;
  }, [currentSession?.status, currentSession?.id, teamsForResults.length]);

  return (
    <AdminPanelLayout
      isLoggedIn={isLoggedIn}
      isLoading={sessionLoading}
      onLoginSuccess={() => setIsLoggedIn(true)}
    >
      <div className="space-y-6">
        <div className="grid gap-6 grid-cols-1">
          <GamePlanningCard
            currentSession={currentSession}
            gameDate={gameDate}
            gameTime={gameTime}
            onDateChange={(date) => date && setGameDate(date)}
            onTimeChange={setGameTime}
            onSave={saveGameDateTime}
            gameFormation={gameFormation}
            canGenerateTeams={canGenerateTeams}
            showMarkAsPlayedButton={showMarkAsPlayedButton}
            isGeneratingTeams={isGeneratingTeams}
            isMarkingAsPlayed={isMarkingAsPlayed}
            isCancelingMatch={isCancelingMatch}
            onToggleSignup={handleToggleSignup}
            onGenerateTeams={handleGenerateTeams}
            onMarkAsPlayed={handleMarkAsPlayed}
            onCancelMatch={handleCancelMatch}
            isPendingTeamApproval={isPendingApproval}
            isSignupOpen={isSignupOpen}
          />

          {/* Empty container div replaced with just a margin when needed */}
          {canGenerateTeams && <div className="mb-4" />}

          {/* Add loading spinner for when teams are being loaded */}
          {isLoadingTeams && (
            <div className="flex justify-center items-center p-8">
              <LoadingSpinner size="lg" />
              <span className="ml-3 text-muted-foreground">Teams werden geladen...</span>
            </div>
          )}

          {/* Only show TeamEditor when not loading and teams exist */}
          {!isLoadingTeams && generatedTeams.length > 0 && (
            <TeamEditor
              teams={generatedTeams}
              onApproveTeams={handleApproveTeams}
              onRegenerateTeams={handleRegenerateTeams}
              isLoading={isGeneratingTeams || sessionLoading}
              isPendingApproval={isPendingApproval}
            />
          )}

          {/* Show match results section as a modal when session is played */}
          <Dialog open={showResultsModal} onOpenChange={setShowResultsModal}>
            <DialogContent className="max-w-xl sm:max-w-2xl md:max-w-3xl lg:max-w-4xl w-full p-0 overflow-hidden">
              <DialogHeader className="p-6 pb-2">
                <div className="space-y-1">
                  <DialogTitle className="text-xl flex items-center gap-2">
                    <Trophy className="h-5 w-5 text-amber-500" />
                    Spielergebnisse erfassen
                  </DialogTitle>
                  <CardDescription>
                    Erfassen Sie hier die Ergebnisse des gespielten Matches
                  </CardDescription>
                </div>
                <Separator className="dark:bg-zinc-800 mt-4" />
              </DialogHeader>

              <div className="p-6 pt-3 space-y-4 max-h-[80vh] overflow-y-auto">
                {currentSession?.id && teamsForResults.length > 0 && (
                  <>
                    <GameSessionMatchResults
                      gameSessionId={currentSession.id}
                      teams={teamsForResults}
                    />
                    {currentSession.status !== "played" && (
                      <div className="flex justify-end mt-8 pt-4 border-t">
                        <Button
                          onClick={() => {
                            finalizeMarkAsPlayed();
                            setShowResultsModal(false);
                          }}
                          disabled={isMarkingAsPlayed}
                          className="bg-green-600 hover:bg-green-700"
                          size="lg"
                        >
                          {isMarkingAsPlayed ? (
                            <>
                              <LoadingSpinner size="sm" className="mr-2" />
                              Wird gespeichert...
                            </>
                          ) : (
                            <>Ergebnisse speichern und Spiel als gespielt markieren</>
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </DialogContent>
          </Dialog>

          {/* Only show debug info in development, not in production */}
          {process.env.NODE_ENV !== "production" &&
            !shouldShowTeamEditor &&
            generatedTeams.length > 0 && (
              <Card>
                <CardHeader>
                  <div className="space-y-1">
                    <CardTitle>Debug Info: TeamEditor sollte angezeigt werden</CardTitle>
                    <CardDescription>
                      Teams wurden generiert, aber TeamEditor wird nicht angezeigt.
                    </CardDescription>
                  </div>
                  <Separator className="dark:bg-zinc-800" />
                </CardHeader>
                <CardContent className="pt-3 space-y-4">
                  <pre className="text-xs bg-white dark:bg-zinc-900 p-2 rounded overflow-auto">
                    {JSON.stringify(
                      {
                        generatedTeamsLength: generatedTeams.length,
                        hasPersistedTeams,
                        isGameScheduled,
                        isPendingApproval,
                        isTeamGenerated: currentSession?.isTeamGenerated,
                        shouldShowTeamEditor,
                      },
                      null,
                      2
                    )}
                  </pre>
                </CardContent>
              </Card>
            )}

          {/* Only show the fallback when really needed */}
          {shouldShowTeamEditor && generatedTeams.length === 0 && (
            <Card>
              <CardHeader>
                <div className="space-y-1">
                  <CardTitle>Teams wurden generiert</CardTitle>
                  <CardDescription>
                    Die Teams wurden erfolgreich erstellt, aber das Team-Editor-Widget wird nicht
                    angezeigt.
                  </CardDescription>
                </div>
                <Separator className="dark:bg-zinc-800" />
              </CardHeader>
              <CardContent className="pt-3 space-y-4">
                <p>
                  Bitte aktualisieren Sie die Seite, um den Team-Editor zu sehen, oder drücken Sie
                  den Knopf unten.
                </p>
                <Button
                  onClick={() => {
                    setIsPendingApproval(true);
                    refetchAll();
                  }}
                  className="bg-yellow-500 hover:bg-yellow-600"
                >
                  Team-Editor anzeigen
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AdminPanelLayout>
  );
};

export default GameManagementPage;
