import { <PERSON><PERSON> } from "@/components/ui/button";
import { Award, LockIcon, Users } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { useMVPVotingAdmin } from "@/hooks/useMVPVotingAdmin";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface MVPVotingCardProps {
  showCloseButton?: boolean;
}

export function MVPVotingCard({ showCloseButton = true }: MVPVotingCardProps) {
  const { votingPeriods, voteResults, isLoading, closeVotingPeriod, closingPeriodId } =
    useMVPVotingAdmin();

  const activeVotingPeriod = votingPeriods.find((period) => period.is_open);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!activeVotingPeriod) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Users className="h-12 w-12 text-zinc-400/20" />
        <p className="mt-4 text-center text-muted-foreground dark:text-zinc-400">
          Es gibt derzeit keine aktive MVP-Abstimmung
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-blue-600/70 dark:text-blue-300/70">
          Spiel vom{" "}
          {activeVotingPeriod.game_sessions.date
            ? new Date(activeVotingPeriod.game_sessions.date).toLocaleDateString("de-DE")
            : "Unbekannt"}
        </p>
        {showCloseButton && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => closeVotingPeriod(activeVotingPeriod.id)}
            disabled={closingPeriodId === activeVotingPeriod.id}
            className="bg-red-500/10 text-red-500 hover:bg-red-500/20 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30"
          >
            {closingPeriodId === activeVotingPeriod.id ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <LockIcon className="h-4 w-4 mr-2" />
            )}
            {closingPeriodId === activeVotingPeriod.id
              ? "Wird geschlossen..."
              : "Abstimmung schließen"}
          </Button>
        )}
      </div>

      <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 border border-blue-100 dark:border-blue-800/30">
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="bg-blue-100 text-blue-500 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"
          >
            Offen
          </Badge>
          <span className="text-sm text-blue-600/70 dark:text-blue-300/70">
            Endet am:{" "}
            {activeVotingPeriod.ends_at
              ? new Date(activeVotingPeriod.ends_at).toLocaleDateString("de-DE", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })
              : "Unbekannt"}
          </span>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 flex items-center gap-2">
          <Award className="h-4 w-4 text-blue-500" />
          Aktuelle Ergebnisse
        </h4>

        <div className="space-y-3">
          {voteResults[activeVotingPeriod.id]?.map((result) => {
            // Find the highest vote count to determine if this would be an MVP
            const maxVotes = Math.max(
              ...voteResults[activeVotingPeriod.id].map((r) => r.vote_count)
            );
            const isCurrentLeader = result.vote_count === maxVotes && maxVotes > 0;

            return (
              <div key={result.player_id} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-blue-900 dark:text-blue-100 flex items-center gap-1.5">
                    {isCurrentLeader && <Award className="h-3.5 w-3.5 text-blue-500" />}
                    {result.player_name}
                  </span>
                  <span className="text-blue-600/70 dark:text-blue-300/70">
                    {result.vote_count} Stimmen
                  </span>
                </div>
                <Progress
                  value={Number(result.vote_percentage)}
                  className="h-2 bg-blue-100 dark:bg-blue-900/20"
                  indicatorClassName="bg-blue-500 dark:bg-blue-400"
                />
              </div>
            );
          })}
          {(!voteResults[activeVotingPeriod.id] ||
            voteResults[activeVotingPeriod.id].length === 0) && (
            <div className="text-center py-4 text-blue-600/70 dark:text-blue-300/70">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-500/30" />
              Noch keine Stimmen
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
