import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { adminService } from "@/services/adminService";
import { Lock } from "lucide-react";

interface AdminLoginProps {
  onLoginSuccess: () => void;
}

interface FormErrors {
  username?: string;
  password?: string;
}

export default function AdminLogin({ onLoginSuccess }: AdminLoginProps) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!username.trim()) {
      newErrors.username = "Benutzername ist erforderlich";
    }

    if (!password) {
      newErrors.password = "Passwort ist erforderlich";
    } else if (password.length < 6) {
      newErrors.password = "Passwort muss mindestens 6 Zeichen lang sein";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const isValid = await adminService.verifyAdminCredentials(username, password);

      if (isValid) {
        localStorage.setItem("isAdminLoggedIn", "true");
        toast({
          title: "Login erfolgreich",
          description: "Willkommen im Admin-Bereich",
        });
        onLoginSuccess();
      } else {
        throw new Error("Ungültige Anmeldedaten");
      }
    } catch (error) {
      toast({
        title: "Login fehlgeschlagen",
        description:
          error instanceof Error ? error.message : "Ungültiger Benutzername oder Passwort",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleLogin} className="space-y-5" noValidate>
      <div className="space-y-2">
        <label htmlFor="username" className="text-sm font-medium dark:text-zinc-200">
          Benutzername
        </label>
        <Input
          id="username"
          type="text"
          value={username}
          onChange={(e) => {
            setUsername(e.target.value);
            if (errors.username) {
              setErrors((prev) => ({ ...prev, username: undefined }));
            }
          }}
          required
          placeholder="Admin-Benutzername"
          className="dark:bg-zinc-800 dark:border-zinc-700"
          aria-invalid={!!errors.username}
          aria-describedby={errors.username ? "username-error" : undefined}
        />
        {errors.username && (
          <p id="username-error" className="text-sm text-red-500 dark:text-red-400">
            {errors.username}
          </p>
        )}
      </div>
      <div className="space-y-2">
        <label htmlFor="password" className="text-sm font-medium dark:text-zinc-200">
          Passwort
        </label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => {
            setPassword(e.target.value);
            if (errors.password) {
              setErrors((prev) => ({ ...prev, password: undefined }));
            }
          }}
          required
          placeholder="Admin-Passwort"
          className="dark:bg-zinc-800 dark:border-zinc-700"
          aria-invalid={!!errors.password}
          aria-describedby={errors.password ? "password-error" : undefined}
        />
        {errors.password && (
          <p id="password-error" className="text-sm text-red-500 dark:text-red-400">
            {errors.password}
          </p>
        )}
      </div>
      <Button
        type="submit"
        className="w-full bg-team-primary text-white hover:bg-team-primary/90 dark:bg-team-primary/90 dark:hover:bg-team-primary mt-4 flex items-center justify-center"
        size="lg"
        disabled={isLoading}
        aria-busy={isLoading}
      >
        {isLoading ? (
          "Anmeldung läuft..."
        ) : (
          <>
            <Lock className="w-4 h-4 mr-2" />
            Anmelden
          </>
        )}
      </Button>
    </form>
  );
}
