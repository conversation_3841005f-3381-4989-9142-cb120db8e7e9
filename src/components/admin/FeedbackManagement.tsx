import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp, Filter, Calendar, ArrowRight, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Feedback } from "@/types/feedback";

export function FeedbackManagement() {
  const [feedbackList, setFeedbackList] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | "game" | "general">("all");
  const [expandedFeedback, setExpandedFeedback] = useState<string | null>(null);

  useEffect(() => {
    fetchFeedback();
  }, []);

  const fetchFeedback = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("feedback")
        .select(
          `
          *,
          game_sessions (
            id,
            date
          )
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;

      const formattedFeedback = data.map((item) => ({
        id: item.id,
        createdAt: new Date(item.created_at),
        content: item.content,
        submittedBy: item.submitted_by || "Anonym",
        isGameFeedback: item.is_game_feedback,
        gameSessionId: item.game_session_id,
        gameSession: item.game_sessions
          ? {
              id: item.game_sessions.id,
              date: new Date(item.game_sessions.date),
              formattedDate: new Date(item.game_sessions.date).toLocaleDateString("de-DE"),
            }
          : undefined,
        rating: item.rating,
        positives: item.positives,
        improvements: item.improvements,
        notes: item.notes,
      })) as Feedback[];

      setFeedbackList(formattedFeedback);
    } catch (error) {
      console.error("Error fetching feedback:", error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredFeedback = () => {
    if (activeTab === "all") return feedbackList;
    if (activeTab === "game") return feedbackList.filter((f) => f.isGameFeedback);
    return feedbackList.filter((f) => !f.isGameFeedback);
  };

  const toggleExpand = (id: string) => {
    if (expandedFeedback === id) {
      setExpandedFeedback(null);
    } else {
      setExpandedFeedback(id);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Card className="w-full dark:bg-zinc-900 dark:border-zinc-800">
      <CardHeader>
        <div className="space-y-1">
          <CardTitle>Feedback-Verwaltung</CardTitle>
          <CardDescription>Einsicht und Verwaltung von Nutzer-Feedback</CardDescription>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>
      <CardContent className="pt-3">
        <Tabs
          defaultValue="all"
          className="w-full"
          onValueChange={(value) => setActiveTab(value as any)}
        >
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
            <div className="w-full overflow-x-auto">
              <TabsList className="w-max min-w-full whitespace-nowrap">
                <TabsTrigger value="all">
                  <span className="inline sm:hidden">Alle</span>
                  <span className="hidden sm:inline">Alle Feedbacks</span>
                </TabsTrigger>
                <TabsTrigger value="game">
                  <span className="inline sm:hidden">Spiel</span>
                  <span className="hidden sm:inline">Spiel-Feedback</span>
                </TabsTrigger>
                <TabsTrigger value="general">
                  <span className="inline sm:hidden">Allgemein</span>
                  <span className="hidden sm:inline">Allgemeines Feedback</span>
                </TabsTrigger>
              </TabsList>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchFeedback}
              className="w-full sm:w-auto"
            >
              Aktualisieren
            </Button>
          </div>

          <div className="border rounded-md overflow-x-auto">
            <Table className="min-w-[600px] text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Datum</TableHead>
                  <TableHead>Typ</TableHead>
                  <TableHead>Eingereicht von</TableHead>
                  <TableHead>Inhalt</TableHead>
                  <TableHead>Aktionen</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6">
                      Feedback wird geladen...
                    </TableCell>
                  </TableRow>
                ) : getFilteredFeedback().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6">
                      Kein Feedback gefunden
                    </TableCell>
                  </TableRow>
                ) : (
                  getFilteredFeedback().map((feedback) => (
                    <React.Fragment key={feedback.id}>
                      <TableRow>
                        <TableCell className="align-top break-words max-w-[120px]">
                          {formatDate(feedback.createdAt)}
                        </TableCell>
                        <TableCell className="align-top break-words max-w-[90px]">
                          <Badge variant="secondary">
                            {feedback.isGameFeedback ? "Spiel" : "Allgemein"}
                          </Badge>
                        </TableCell>
                        <TableCell className="align-top break-words max-w-[120px]">
                          {feedback.submittedBy}
                        </TableCell>
                        <TableCell className="max-w-[180px] break-words">
                          <div className="line-clamp-2">
                            {feedback.isGameFeedback
                              ? feedback.notes || "Spiel-Feedback"
                              : feedback.content}
                          </div>
                        </TableCell>
                        <TableCell className="align-top">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => toggleExpand(feedback.id!)}
                          >
                            {expandedFeedback === feedback.id ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </Button>
                        </TableCell>
                      </TableRow>

                      {expandedFeedback === feedback.id && (
                        <TableRow className="bg-muted/50">
                          <TableCell colSpan={5} className="p-4">
                            <div className="space-y-4">
                              {feedback.isGameFeedback && feedback.gameSession && (
                                <div className="flex items-center justify-between border-b pb-3 mb-3">
                                  <div className="flex items-center space-x-2">
                                    <Calendar className="h-4 w-4 text-blue-500" />
                                    <span className="font-medium">
                                      Spiel vom {feedback.gameSession.formattedDate}
                                    </span>
                                  </div>
                                  <Link to={`/history?gameId=${feedback.gameSessionId}`}>
                                    <Button variant="ghost" size="sm" className="flex items-center">
                                      <Users className="h-4 w-4 mr-2" />
                                      Zum Spiel
                                      <ArrowRight className="h-4 w-4 ml-1" />
                                    </Button>
                                  </Link>
                                </div>
                              )}

                              {feedback.isGameFeedback ? (
                                <>
                                  {feedback.rating !== null && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-1">Bewertung</h4>
                                      <div className="flex gap-1">
                                        {[1, 2, 3, 4, 5].map((star) => (
                                          <div
                                            key={star}
                                            className={`w-6 h-6 flex items-center justify-center rounded-full ${
                                              star <= feedback.rating!
                                                ? "bg-blue-600 text-white"
                                                : "bg-gray-200 text-gray-500"
                                            }`}
                                          >
                                            {star}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {feedback.positives && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-1">Was gut lief</h4>
                                      <p className="text-sm">{feedback.positives}</p>
                                    </div>
                                  )}

                                  {feedback.improvements && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-1">
                                        Was verbessert werden könnte
                                      </h4>
                                      <p className="text-sm">{feedback.improvements}</p>
                                    </div>
                                  )}

                                  {feedback.notes && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-1">
                                        Zusätzliche Anmerkungen
                                      </h4>
                                      <p className="text-sm">{feedback.notes}</p>
                                    </div>
                                  )}
                                </>
                              ) : (
                                <div>
                                  <h4 className="text-sm font-medium mb-1">Feedback</h4>
                                  <p className="text-sm">{feedback.content}</p>
                                </div>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
