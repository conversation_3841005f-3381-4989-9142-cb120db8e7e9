import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/custom-card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import LoadingSpinner from "@/components/LoadingSpinner";
import { usePlayers } from "@/hooks/usePlayers";
import { Player, Team, PlayerRole } from "@/types";
import {
  Users,
  RefreshCw,
  PlusCircle,
  MinusCircle,
  BarChart,
  Search,
  Award,
  Clock,
  X,
  Check,
  Edit,
  ArrowRight,
  Wand,
  Info,
  AlertTriangle,
  Save,
  FolderOpen,
  Calendar,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useGameSessions } from "@/hooks/useGameSessions";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { generateTeams } from "@/utils/teamUtils";
import {
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  Tooltip,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
} from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { supabase } from "@/integrations/supabase/client";
// Add import for PlayerName component and PlayerProfile context
import { PlayerName } from "@/components/player-profile/PlayerName";
import { usePlayerProfile } from "@/context/PlayerProfileContext";

interface PlayerWithStats extends Player {
  totalGames: number;
  mvpCount: number;
  participationRate: number;
}

interface TeamStats {
  avgMvps: number;
  avgTotalGames: number;
  avgGamesPerMonth: number;
}

// Add interface for chart data
interface ChartData {
  stat: string;
  [key: string]: string | number;
}

// Add interface for saved setups
interface SavedTeamSetup {
  id: string;
  name: string;
  timestamp: number;
  teams: Team[];
  selectedPlayerIds: string[];
}

export default function TeamPlayground() {
  // Fetch all players and sessions
  const { players, loading: playersLoading } = usePlayers(null);
  const {
    currentSession,
    pastSessions,
    loading: sessionsLoading,
  } = useGameSessions({
    fetchCurrent: true,
    fetchPast: true,
  });

  // Add player profile hook
  const { openProfile } = usePlayerProfile();

  // State for player pool and teams
  const [availablePlayers, setAvailablePlayers] = useState<PlayerWithStats[]>([]);
  const [allPlayers, setAllPlayers] = useState<PlayerWithStats[]>([]);
  const [selectedPlayerIds, setSelectedPlayerIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [teams, setTeams] = useState<Team[]>([
    { id: "team-1", name: "Team A", players: [], averageRating: 0 },
    { id: "team-2", name: "Team B", players: [], averageRating: 0 },
  ]);

  // Add state for team name editing
  const [editingTeamId, setEditingTeamId] = useState<string | null>(null);
  const [editingTeamName, setEditingTeamName] = useState("");

  // Add state for team statistics
  const [teamStats, setTeamStats] = useState<Record<string, TeamStats>>({});

  // Add state for player details dialog
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerWithStats | null>(null);
  const [playerDetailsOpen, setPlayerDetailsOpen] = useState(false);

  // Add state for imbalance detection
  const [imbalancedStats, setImbalancedStats] = useState<{
    hasImbalance: boolean;
    imbalancedStat: string | null;
    maxTeam: string | null;
    difference: number;
  }>({
    hasImbalance: false,
    imbalancedStat: null,
    maxTeam: null,
    difference: 0,
  });

  // Add state for saved setups
  const [setupName, setSetupName] = useState("");
  const [savedSetups, setSavedSetups] = useState<SavedTeamSetup[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);

  // Add state for current session players
  const [isLoadingCurrentPlayers, setIsLoadingCurrentPlayers] = useState(false);
  const [currentSessionPlayers, setCurrentSessionPlayers] = useState<Player[]>([]);

  // Load saved setups from local storage
  useEffect(() => {
    const storedSetups = localStorage.getItem("teamPlaygroundSetups");
    if (storedSetups) {
      try {
        const parsedSetups = JSON.parse(storedSetups) as SavedTeamSetup[];
        setSavedSetups(parsedSetups);
      } catch (error) {
        console.error("Failed to parse saved team setups:", error);
      }
    }
  }, []);

  // Save current setup to local storage
  const saveCurrentSetup = () => {
    if (!setupName.trim()) return;

    const newSetup: SavedTeamSetup = {
      id: `setup-${Date.now()}`,
      name: setupName,
      timestamp: Date.now(),
      teams: teams.map((team) => ({
        ...team,
        // Clone players to ensure we have independent objects
        players: team.players.map((player) => ({ ...player })),
      })),
      selectedPlayerIds: [...selectedPlayerIds],
    };

    const updatedSetups = [...savedSetups, newSetup];
    setSavedSetups(updatedSetups);

    // Save to local storage
    localStorage.setItem("teamPlaygroundSetups", JSON.stringify(updatedSetups));

    // Reset and close dialog
    setSetupName("");
    setShowSaveDialog(false);
  };

  // Load a saved setup
  const loadSetup = (setupId: string) => {
    const setup = savedSetups.find((s) => s.id === setupId);
    if (!setup) return;

    // Restore teams
    setTeams(
      setup.teams.map((team) => ({
        ...team,
        // Clone players to ensure we have independent objects
        players: team.players.map((player) => ({ ...player })),
      }))
    );

    // Restore selected player IDs
    setSelectedPlayerIds(setup.selectedPlayerIds);

    // Close dialog
    setShowLoadDialog(false);
  };

  // Delete a saved setup
  const deleteSetup = (setupId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    const updatedSetups = savedSetups.filter((setup) => setup.id !== setupId);
    setSavedSetups(updatedSetups);

    // Update local storage
    localStorage.setItem("teamPlaygroundSetups", JSON.stringify(updatedSetups));
  };

  // Format date for display
  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString("de-DE", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to add a new team
  const addTeam = () => {
    // Only allow up to 4 teams
    if (teams.length >= 4) return;

    const newTeamId = `team-${teams.length + 1}`;
    const newTeamName = `Team ${String.fromCharCode(65 + teams.length)}`; // A, B, C, D

    setTeams((prev) => [
      ...prev,
      { id: newTeamId, name: newTeamName, players: [], averageRating: 0 },
    ]);
  };

  // Function to remove a team
  const removeTeam = (teamId: string) => {
    // Don't allow fewer than 2 teams
    if (teams.length <= 2) return;

    // Move players from this team back to the pool if they're still selected
    const teamToRemove = teams.find((t) => t.id === teamId);
    if (teamToRemove) {
      const playersToReturn = teamToRemove.players
        .filter((player) => selectedPlayerIds.includes(player.id || ""))
        .map((player) => allPlayers.find((p) => p.id === player.id))
        .filter(Boolean) as PlayerWithStats[];

      setAvailablePlayers((prev) => [...prev, ...playersToReturn]);
    }

    // Remove the team
    setTeams((prev) => prev.filter((team) => team.id !== teamId));
  };

  // Function to start editing a team name
  const startEditingTeamName = (teamId: string, currentName: string) => {
    setEditingTeamId(teamId);
    setEditingTeamName(currentName);
  };

  // Function to save edited team name
  const saveTeamName = () => {
    if (!editingTeamId || !editingTeamName.trim()) {
      setEditingTeamId(null);
      return;
    }

    setTeams((prev) =>
      prev.map((team) =>
        team.id === editingTeamId ? { ...team, name: editingTeamName.trim() } : team
      )
    );

    setEditingTeamId(null);
  };

  // Process players with stats when data is loaded
  useEffect(() => {
    if (players && !playersLoading && !sessionsLoading) {
      // Function to fetch MVP count for a player
      const fetchMVPCount = async (playerId: string | undefined): Promise<number> => {
        if (!playerId) return 0;

        try {
          // Get all voting periods where this player is the MVP winner
          const { data: winners, error } = await supabase
            .from("mvp_winners")
            .select("id")
            .eq("player_id", playerId);

          if (error) {
            console.error("Error fetching MVP winners:", error);
            return 0;
          }

          // Return the count of MVP wins
          return winners?.length || 0;
        } catch (error) {
          console.error("Error calculating MVP count:", error);
          return 0;
        }
      };

      // Process players with proper stats calculation
      const fetchPlayersWithStats = async () => {
        try {
          // Use Promise.all to fetch MVP counts for all players in parallel
          const playersWithStatsPromises = players
            .filter((player) => player.rating !== undefined)
            .map(async (player) => {
              // Calculate player stats
              const participatedSessions = pastSessions.filter((session) => {
                if (!session.teams) return false;
                return session.teams.some((team) => team.players.some((p) => p.id === player.id));
              });

              // Fetch MVP count from database
              const mvpCount = await fetchMVPCount(player.id);

              // Calculate participation rate
              const totalGames = participatedSessions.length;
              const participationRate =
                totalGames > 0
                  ? totalGames / 1 // placeholder for games/month calculation
                  : 0;

              return {
                ...player,
                totalGames,
                mvpCount,
                participationRate,
              };
            });

          // Wait for all promises to resolve
          const playersWithStats = await Promise.all(playersWithStatsPromises);

          // Sort by rating
          const sortedPlayers = playersWithStats.sort((a, b) => b.rating - a.rating);

          // Update state with the fetched data
          setAllPlayers(sortedPlayers);
        } catch (error) {
          console.error("Error fetching player stats:", error);

          // Fallback to minimal stats if error occurs
          const minimalStats = players
            .filter((player) => player.rating !== undefined)
            .map((player) => ({
              ...player,
              totalGames: 0,
              mvpCount: 0,
              participationRate: 0,
            }))
            .sort((a, b) => b.rating - a.rating);

          setAllPlayers(minimalStats);
        }
      };

      // Fetch stats
      fetchPlayersWithStats();
    }
  }, [players, playersLoading, pastSessions, sessionsLoading]);

  // Filtered players based on search and selection
  const filteredPlayers = useMemo(() => {
    if (!searchQuery) return allPlayers;

    return allPlayers.filter((player) =>
      player.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [allPlayers, searchQuery]);

  // Update available players when selection changes or teams change
  useEffect(() => {
    // Get all player IDs that are currently in teams
    const teamPlayerIds = teams.flatMap((team) => team.players.map((player) => player.id || ""));

    // Filter selected players who are not already in teams
    const selectedAndNotInTeams = allPlayers.filter(
      (player) =>
        selectedPlayerIds.includes(player.id || "") && !teamPlayerIds.includes(player.id || "")
    );

    setAvailablePlayers(selectedAndNotInTeams);
  }, [selectedPlayerIds, allPlayers, teams]);

  // Toggle player selection
  const togglePlayerSelection = (playerId: string) => {
    setSelectedPlayerIds((prev) => {
      if (prev.includes(playerId)) {
        return prev.filter((id) => id !== playerId);
      } else {
        return [...prev, playerId];
      }
    });
  };

  // Remove player from selection
  const removePlayerFromSelection = (playerId: string) => {
    setSelectedPlayerIds((prev) => prev.filter((id) => id !== playerId));
  };

  // Select all visible players
  const selectAllVisible = () => {
    const visibleIds = filteredPlayers.map((p) => p.id || "");
    setSelectedPlayerIds((prev) => {
      const newSelection = [...prev];

      visibleIds.forEach((id) => {
        if (!newSelection.includes(id)) {
          newSelection.push(id);
        }
      });

      return newSelection;
    });
  };

  // Deselect all players
  const deselectAll = () => {
    setSelectedPlayerIds([]);
  };

  // Calculate team stats
  const calculateTeamStats = (team: Team) => {
    if (!team.players || team.players.length === 0) {
      return { averageRating: 0 };
    }

    const totalRating = team.players.reduce((sum, player) => sum + (player.rating || 0), 0);
    const averageRating = totalRating / team.players.length;

    return { averageRating };
  };

  // Handle adding player to a team
  const addPlayerToTeam = (player: Player, teamId: string) => {
    // Remove from available players pool
    setAvailablePlayers((prev) => prev.filter((p) => p.id !== player.id));

    // Add to selected team
    setTeams((prev) =>
      prev.map((team) => {
        if (team.id === teamId) {
          const updatedPlayers = [...team.players, player];
          const { averageRating } = calculateTeamStats({
            ...team,
            players: updatedPlayers,
          });

          return {
            ...team,
            players: updatedPlayers,
            averageRating,
          };
        }
        return team;
      })
    );
  };

  // Handle removing player from a team
  const removePlayerFromTeam = (player: Player, teamId: string) => {
    // Find player with stats
    const playerWithStats = allPlayers.find((p) => p.id === player.id);

    if (playerWithStats && selectedPlayerIds.includes(player.id || "")) {
      // Add back to available players if still selected
      setAvailablePlayers((prev) => [...prev, playerWithStats]);
    }

    // Remove from team
    setTeams((prev) =>
      prev.map((team) => {
        if (team.id === teamId) {
          const updatedPlayers = team.players.filter((p) => p.id !== player.id);
          const { averageRating } = calculateTeamStats({
            ...team,
            players: updatedPlayers,
          });

          return {
            ...team,
            players: updatedPlayers,
            averageRating,
          };
        }
        return team;
      })
    );
  };

  // Reset teams (move all players back to the pool)
  const resetTeams = () => {
    // Get all players from teams
    const allTeamPlayers = teams.flatMap((team) => team.players);

    // Find players with stats that were in teams and are still selected
    const teamPlayersWithStats = allTeamPlayers
      .filter((player) => selectedPlayerIds.includes(player.id || ""))
      .map((player) => allPlayers.find((p) => p.id === player.id))
      .filter(Boolean) as PlayerWithStats[];

    // Add back to available players if still selected
    setAvailablePlayers((prev) => [...prev, ...teamPlayersWithStats]);

    // Reset teams
    setTeams((prev) =>
      prev.map((team) => ({
        ...team,
        players: [],
        averageRating: 0,
      }))
    );
  };

  // Auto balance teams based on ratings
  const autoBalanceTeams = () => {
    // Combine all players (from pool and existing teams)
    const allActivePoolPlayers = [...availablePlayers, ...teams.flatMap((team) => team.players)];

    if (allActivePoolPlayers.length === 0 || teams.length === 0) return;

    // Sort players by rating (highest to lowest)
    const sortedPlayers = [...allActivePoolPlayers].sort(
      (a, b) => (b.rating || 0) - (a.rating || 0)
    );

    // Create balanced teams using serpentine draft order
    const balancedTeams: Player[][] = teams.map(() => []);

    sortedPlayers.forEach((player, index) => {
      // For serpentine draft
      const teamCount = teams.length;
      const roundNum = Math.floor(index / teamCount);
      const teamIndex =
        roundNum % 2 === 0 ? index % teamCount : teamCount - 1 - (index % teamCount);

      balancedTeams[teamIndex].push(player);
    });

    // Update teams with new balanced rosters
    setTeams((prev) =>
      prev.map((team, index) => {
        const updatedPlayers = balancedTeams[index] || [];
        const { averageRating } = calculateTeamStats({
          ...team,
          players: updatedPlayers,
        });

        return {
          ...team,
          players: updatedPlayers,
          averageRating,
        };
      })
    );

    // Clear available players pool since all are assigned
    setAvailablePlayers([]);
  };

  // Function to generate balanced teams using the teamUtils function
  const generateBalancedTeams = () => {
    // Combine all available players (from pool and existing teams)
    const allAvailablePlayers = [...availablePlayers, ...teams.flatMap((team) => team.players)];

    if (allAvailablePlayers.length === 0) {
      return;
    }

    // The teamUtils.generateTeams function works best with player counts
    // of exactly 10, 15, or 20 players and will automatically optimize
    // for those team configurations

    // Generate optimal teams using the team generation algorithm
    const generatedTeams = generateTeams(allAvailablePlayers, teams.length);

    if (!generatedTeams || generatedTeams.length === 0) {
      // If generation fails or returns empty, fall back to the autoBalanceTeams method
      autoBalanceTeams();
      return;
    }

    // Update team states while preserving their IDs and names
    setTeams((prevTeams) => {
      // Create new teams array
      const updatedTeams = [...prevTeams];

      // Adjust team count if needed based on what the algorithm returned
      const targetTeamCount = generatedTeams.length;

      if (targetTeamCount > prevTeams.length) {
        // Add new teams if we need more
        const teamsToAdd = targetTeamCount - prevTeams.length;
        for (let i = 0; i < teamsToAdd; i++) {
          const newTeamId = `team-${prevTeams.length + i + 1}`;
          const newTeamName = `Team ${String.fromCharCode(65 + prevTeams.length + i)}`; // A, B, C, D
          updatedTeams.push({
            id: newTeamId,
            name: newTeamName,
            players: [],
            averageRating: 0,
          });
        }
      } else if (targetTeamCount < prevTeams.length) {
        // We need fewer teams than we currently have
        // Just preserve the first N teams and keep their names/IDs
        return updatedTeams.slice(0, targetTeamCount).map((team, index) => ({
          ...team, // Keep original id and name
          players: generatedTeams[index].players,
          averageRating: generatedTeams[index].averageRating,
        }));
      }

      // Update players and ratings for each team
      return updatedTeams.map((team, index) => {
        if (index < generatedTeams.length) {
          return {
            ...team, // Keep original ID and name
            players: generatedTeams[index].players,
            averageRating: generatedTeams[index].averageRating,
          };
        }
        return team;
      });
    });

    // Empty the player pool since all players are now in teams
    setAvailablePlayers([]);
  };

  // Calculate team statistics
  useEffect(() => {
    // Create a new stats object
    const newTeamStats: Record<string, TeamStats> = {};

    // Calculate stats for each team
    teams.forEach((team) => {
      if (team.players.length === 0) {
        newTeamStats[team.id] = {
          avgMvps: 0,
          avgTotalGames: 0,
          avgGamesPerMonth: 0,
        };
        return;
      }

      // Find each player's stats from allPlayers
      const playersWithStats = team.players
        .map((player) => allPlayers.find((p) => p.id === player.id))
        .filter(Boolean) as PlayerWithStats[];

      // Calculate averages
      const mvpSum = playersWithStats.reduce((sum, player) => sum + player.mvpCount, 0);
      const gamesSum = playersWithStats.reduce((sum, player) => sum + player.totalGames, 0);
      const rateSum = playersWithStats.reduce((sum, player) => sum + player.participationRate, 0);

      newTeamStats[team.id] = {
        avgMvps:
          playersWithStats.length > 0 ? Number((mvpSum / playersWithStats.length).toFixed(1)) : 0,
        avgTotalGames:
          playersWithStats.length > 0 ? Number((gamesSum / playersWithStats.length).toFixed(1)) : 0,
        avgGamesPerMonth:
          playersWithStats.length > 0 ? Number((rateSum / playersWithStats.length).toFixed(1)) : 0,
      };
    });

    setTeamStats(newTeamStats);
  }, [teams, allPlayers]);

  // Determine grid layout class based on number of teams
  const getGridLayoutClasses = () => {
    const teamCount = teams.length;

    if (teamCount <= 2) {
      // 1-2 teams: simple 3-column grid (pool, team1, team2)
      return "grid-cols-1 lg:grid-cols-3 gap-6";
    } else {
      // 3-4 teams: 3-column grid with player pool spanning 2 rows
      return "grid-cols-1 lg:grid-cols-3 lg:grid-rows-2 gap-6";
    }
  };

  // Add click outside handler to close the export menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById("exportOptions");
      const button = event.target as Element;

      if (
        dropdown &&
        !dropdown.classList.contains("hidden") &&
        !dropdown.contains(button) &&
        !button.closest("button")?.textContent?.includes("Teams exportieren")
      ) {
        dropdown.classList.add("hidden");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Export functions
  const exportAsJSON = () => {
    // Create export data with teams and their statistics
    const exportData = teams.map((team) => ({
      name: team.name,
      averageRating: team.averageRating,
      stats: teamStats[team.id] || {
        avgMvps: 0,
        avgTotalGames: 0,
        avgGamesPerMonth: 0,
      },
      players: team.players.map((player) => ({
        name: player.name,
        rating: player.rating,
        jerseyNumber: player.jerseyNumber,
        role: player.role,
      })),
    }));

    // Convert to JSON string
    const jsonString = JSON.stringify(exportData, null, 2);

    // Create blob and download link
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    // Create download link and trigger download
    const a = document.createElement("a");
    a.href = url;
    a.download = `team-playground-export-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Hide dropdown
    document.getElementById("exportOptions")?.classList.add("hidden");
  };

  const exportAsCSV = () => {
    // CSV Headers
    let csvContent = "Team,Player,Rating,Jersey Number,Role\n";

    // Add data for each team and player
    teams.forEach((team) => {
      if (team.players.length === 0) {
        csvContent += `${team.name},No players,,,\n`;
      } else {
        team.players.forEach((player, index) => {
          // For first player in team, add team name
          const teamName = index === 0 ? team.name : "";

          csvContent += `"${teamName}","${player.name}",${player.rating},${
            player.jerseyNumber || ""
          },${player.role || ""}\n`;
        });
      }

      // Add empty row and team statistics
      csvContent += `\n`;
      csvContent += `"${team.name} Statistics",,,\n`;
      csvContent += `"Average Rating",${team.averageRating.toFixed(1)},,\n`;

      if (teamStats[team.id]) {
        csvContent += `"Average MVPs",${teamStats[team.id].avgMvps},,\n`;
        csvContent += `"Average Games",${teamStats[team.id].avgTotalGames},,\n`;
        csvContent += `"Average Games/Month",${teamStats[team.id].avgGamesPerMonth},,\n`;
      }

      csvContent += `\n`;
    });

    // Create blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);

    // Create download link and trigger download
    const a = document.createElement("a");
    a.href = url;
    a.download = `team-playground-export-${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Hide dropdown
    document.getElementById("exportOptions")?.classList.add("hidden");
  };

  const printTeams = () => {
    // Create a new window for the print view
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      alert("Please allow popups to use the print function.");
      return;
    }

    // Generate HTML content for the print window
    let printContent = `
      <html>
      <head>
        <title>Team Playground - ${new Date().toLocaleDateString()}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { text-align: center; color: #333; }
          .timestamp { text-align: center; color: #777; margin-bottom: 30px; }
          .teams-container { display: flex; flex-wrap: wrap; justify-content: space-around; }
          .team { width: 45%; margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
          .team-header { background: #f3f4f6; padding: 10px; border-bottom: 1px solid #ddd; }
          .team-name { font-size: 18px; font-weight: bold; margin: 0; }
          .team-stats { display: flex; justify-content: space-between; background: #f9fafb; padding: 8px; border-bottom: 1px solid #eee; }
          .stat { font-size: 12px; color: #555; }
          .players { padding: 0; }
          .player { padding: 8px 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; }
          .player:last-child { border-bottom: none; }
          .player-name { font-weight: 500; }
          .player-rating { background: #eee; padding: 2px 8px; border-radius: 12px; font-size: 12px; }
          @media print {
            .team { page-break-inside: avoid; }
            body { -webkit-print-color-adjust: exact; }
          }
        </style>
      </head>
      <body>
        <h1>Team Playground - Teamzusammensetzung</h1>
        <div class="timestamp">Erstellt am ${new Date().toLocaleDateString()} um ${new Date().toLocaleTimeString()}</div>
        <div class="teams-container">
    `;

    // Add each team
    teams.forEach((team) => {
      printContent += `
        <div class="team">
          <div class="team-header">
            <h2 class="team-name">${team.name}</h2>
          </div>
          <div class="team-stats">
            <div class="stat">Durchschn. Rating: <strong>${team.averageRating.toFixed(
              1
            )}</strong></div>
      `;

      if (teamStats[team.id]) {
        printContent += `
            <div class="stat">Durchschn. MVPs: <strong>${teamStats[team.id].avgMvps}</strong></div>
            <div class="stat">Durchschn. Spiele: <strong>${
              teamStats[team.id].avgTotalGames
            }</strong></div>
        `;
      }

      printContent += `
          </div>
          <div class="players">
      `;

      // Add each player
      if (team.players.length === 0) {
        printContent += `<div class="player" style="text-align: center; color: #777;">Keine Spieler</div>`;
      } else {
        team.players.forEach((player) => {
          printContent += `
            <div class="player">
              <span class="player-name">${player.name}${
                player.jerseyNumber ? ` (${player.jerseyNumber})` : ""
              }</span>
              <span class="player-rating">${player.rating}</span>
            </div>
          `;
        });
      }

      printContent += `
          </div>
        </div>
      `;
    });

    printContent += `
        </div>
      </body>
      </html>
    `;

    // Write to the print window, print, and close
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for content to load before printing
    printWindow.onload = function () {
      printWindow.print();
    };

    // Hide dropdown
    document.getElementById("exportOptions")?.classList.add("hidden");
  };

  // Format data for radar chart
  const formatChartData = (): ChartData[] => {
    if (teams.length === 0 || Object.keys(teamStats).length === 0) {
      return [];
    }

    const radarData: ChartData[] = [
      { stat: "Durchschnittliches Rating" },
      { stat: "MVPs pro Spieler" },
      { stat: "Spiele pro Spieler" },
      { stat: "Aktivitätsrate" },
    ];

    teams.forEach((team) => {
      if (team.players.length > 0) {
        radarData[0][team.name] = Number(team.averageRating.toFixed(1));
        radarData[1][team.name] = teamStats[team.id]?.avgMvps || 0;
        radarData[2][team.name] = teamStats[team.id]?.avgTotalGames || 0;
        radarData[3][team.name] = teamStats[team.id]?.avgGamesPerMonth || 0;
      } else {
        radarData[0][team.name] = 0;
        radarData[1][team.name] = 0;
        radarData[2][team.name] = 0;
        radarData[3][team.name] = 0;
      }
    });

    return radarData;
  };

  // Format data for bar comparison
  const formatBarData = (): ChartData[] => {
    if (teams.length === 0) {
      return [];
    }

    return teams.map((team) => {
      const stats = teamStats[team.id] || {
        avgMvps: 0,
        avgTotalGames: 0,
        avgGamesPerMonth: 0,
      };
      return {
        stat: team.name,
        name: team.name,
        rating: Number(team.averageRating.toFixed(1)),
        mvps: stats.avgMvps,
        games: stats.avgTotalGames,
        activity: stats.avgGamesPerMonth,
      };
    });
  };

  // Check for team imbalances
  useEffect(() => {
    // Only check when we have enough teams with players
    const teamsWithPlayers = teams.filter((team) => team.players.length > 0);
    if (teamsWithPlayers.length < 2 || Object.keys(teamStats).length === 0) {
      setImbalancedStats({
        hasImbalance: false,
        imbalancedStat: null,
        maxTeam: null,
        difference: 0,
      });
      return;
    }

    // Compare ratings
    let maxRatingTeam = teamsWithPlayers[0];
    let minRatingTeam = teamsWithPlayers[0];
    teamsWithPlayers.forEach((team) => {
      if (team.averageRating > maxRatingTeam.averageRating) maxRatingTeam = team;
      if (team.averageRating < minRatingTeam.averageRating) minRatingTeam = team;
    });

    const ratingDiff = maxRatingTeam.averageRating - minRatingTeam.averageRating;

    // Compare MVPs
    let maxMvpTeam = null;
    let minMvpTeam = null;
    let maxMvp = 0;
    let minMvp = Infinity;

    teamsWithPlayers.forEach((team) => {
      const mvps = teamStats[team.id]?.avgMvps || 0;
      if (mvps > maxMvp) {
        maxMvp = mvps;
        maxMvpTeam = team;
      }
      if (mvps < minMvp) {
        minMvp = mvps;
        minMvpTeam = team;
      }
    });

    const mvpDiff = maxMvp - minMvp;

    // Compare total games
    let maxGamesTeam = null;
    let minGamesTeam = null;
    let maxGames = 0;
    let minGames = Infinity;

    teamsWithPlayers.forEach((team) => {
      const games = teamStats[team.id]?.avgTotalGames || 0;
      if (games > maxGames) {
        maxGames = games;
        maxGamesTeam = team;
      }
      if (games < minGames) {
        minGames = games;
        minGamesTeam = team;
      }
    });

    const gamesDiff = maxGames - minGames;

    // Check which has the biggest relative difference
    const ratingRelativeDiff =
      maxRatingTeam.averageRating !== 0 ? ratingDiff / maxRatingTeam.averageRating : 0;
    const mvpRelativeDiff = maxMvp !== 0 ? mvpDiff / maxMvp : 0;
    const gamesRelativeDiff = maxGames !== 0 ? gamesDiff / maxGames : 0;

    let hasImbalance = false;
    let imbalancedStat = null;
    let maxTeam = null;
    let difference = 0;

    // Imbalance thresholds
    const RATING_THRESHOLD = 0.15; // 15% difference in rating
    const STAT_THRESHOLD = 0.3; // 30% difference in other stats

    if (ratingRelativeDiff >= RATING_THRESHOLD) {
      hasImbalance = true;
      imbalancedStat = "Rating";
      maxTeam = maxRatingTeam.name;
      difference = ratingDiff;
    } else if (mvpRelativeDiff >= STAT_THRESHOLD) {
      hasImbalance = true;
      imbalancedStat = "MVPs";
      maxTeam = maxMvpTeam?.name || "";
      difference = mvpDiff;
    } else if (gamesRelativeDiff >= STAT_THRESHOLD) {
      hasImbalance = true;
      imbalancedStat = "Spielerfahrung";
      maxTeam = maxGamesTeam?.name || "";
      difference = gamesDiff;
    }

    setImbalancedStats({
      hasImbalance,
      imbalancedStat,
      maxTeam,
      difference: Number(difference.toFixed(1)),
    });
  }, [teams, teamStats]);

  // Function to load players from current session
  const loadCurrentSessionPlayers = async () => {
    if (!currentSession) {
      return;
    }

    setIsLoadingCurrentPlayers(true);
    try {
      // Get player signups for current session
      const { data: signups, error: signupsError } = await supabase
        .from("player_signups")
        .select("*, player:players(*)")
        .eq("game_session_id", currentSession.id)
        .eq("status", "in");

      if (signupsError) throw signupsError;

      if (signups && signups.length > 0) {
        // Map the player data from signups
        const confirmedPlayers = signups.map((signup) => ({
          id: signup.player.id,
          name: signup.player.name,
          jerseyNumber: signup.player.jersey_number,
          rating: signup.player.rating,
          role: (signup.player.role || undefined) as PlayerRole,
          status: "in" as const, // Use const assertion to fix type
        }));

        // Set the players and select them all
        const playerIds = confirmedPlayers.map((p) => p.id || "");
        setCurrentSessionPlayers(confirmedPlayers as Player[]); // Cast to Player type
        setSelectedPlayerIds(playerIds);

        // Reset teams
        resetTeams();

        // Add players to available pool
        const playersWithStats = confirmedPlayers.map((player) => {
          const playerWithStats = allPlayers.find((p) => p.id === player.id);
          return (
            playerWithStats ||
            ({
              ...player,
              totalGames: 0,
              mvpCount: 0,
              participationRate: 0,
            } as PlayerWithStats)
          );
        });

        setAvailablePlayers(playersWithStats);
      }
    } catch (error) {
      console.error("Error loading current session players:", error);
    } finally {
      setIsLoadingCurrentPlayers(false);
    }
  };

  // Function to simulate teams with current session players
  const simulateTeamsWithCurrentPlayers = async () => {
    if (currentSessionPlayers.length === 0) {
      await loadCurrentSessionPlayers();
    }

    // Generate teams with the current players
    if (availablePlayers.length > 0) {
      generateBalancedTeams();
    }
  };

  if (playersLoading || sessionsLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Team Playground</CardTitle>
        </CardHeader>
        <CardContent className="pt-6 flex justify-center items-center min-h-[200px]">
          <LoadingSpinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="dark:bg-zinc-900 dark:border-zinc-800">
        <CardHeader>
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div className="space-y-1">
                <CardTitle className="text-2xl font-semibold dark:text-white">
                  Team Playground
                </CardTitle>
                <CardDescription className="dark:text-zinc-400">
                  Simulieren und analysieren Sie verschiedene Teamkombinationen
                </CardDescription>
              </div>

              <div className="flex flex-wrap gap-2 sm:justify-end">
                {currentSession ? (
                  <div className="w-full sm:w-auto">
                    <Button
                      variant="outline"
                      className="w-full flex items-center gap-2 bg-green-50 text-green-700 border-green-200 hover:bg-green-100 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-900/40"
                      onClick={simulateTeamsWithCurrentPlayers}
                      disabled={isLoadingCurrentPlayers}
                    >
                      {isLoadingCurrentPlayers ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <Calendar className="h-4 w-4" />
                      )}
                      <div className="flex flex-col items-start text-left">
                        <span className="text-sm font-medium">Aktuelle Anmeldungen</span>
                        <span className="text-xs opacity-90">
                          {new Date(currentSession.date).toLocaleDateString()}
                          {currentSessionPlayers.length > 0
                            ? ` • ${currentSessionPlayers.length} Spieler`
                            : " • Keine Spieler"}
                        </span>
                      </div>
                    </Button>
                  </div>
                ) : null}
                <div className="flex gap-2 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    className="flex-1 sm:flex-none flex items-center gap-1 dark:border-zinc-700 dark:hover:bg-zinc-800"
                    onClick={() => setShowSaveDialog(true)}
                  >
                    <Save className="h-4 w-4" />
                    <span>Speichern</span>
                  </Button>

                  {savedSetups.length > 0 && (
                    <Button
                      variant="outline"
                      className="flex-1 sm:flex-none flex items-center gap-1 dark:border-zinc-700 dark:hover:bg-zinc-800"
                      onClick={() => setShowLoadDialog(true)}
                    >
                      <FolderOpen className="h-4 w-4" />
                      <span>Laden</span>
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>

        <CardContent className="pt-4">
          {/* Save Setup Dialog */}
          {showSaveDialog && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-zinc-900 p-6 rounded-lg shadow-lg max-w-md w-full">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium dark:text-white">Team-Setup speichern</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setSetupName("");
                      setShowSaveDialog(false);
                    }}
                    className="h-6 w-6 dark:hover:bg-zinc-800"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-500 dark:text-gray-400 mb-1.5 block">
                      Name des Setups
                    </label>
                    <Input
                      value={setupName}
                      onChange={(e) => setSetupName(e.target.value)}
                      placeholder="z.B. Training 15.03.2024"
                      className="dark:bg-zinc-800 dark:border-zinc-700"
                    />
                  </div>
                  <div className="pt-2 flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSetupName("");
                        setShowSaveDialog(false);
                      }}
                      className="dark:border-zinc-700 dark:hover:bg-zinc-800"
                    >
                      Abbrechen
                    </Button>
                    <Button
                      onClick={saveCurrentSetup}
                      disabled={!setupName.trim()}
                      className="bg-team-primary hover:bg-team-primary/90"
                    >
                      Speichern
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Load Setup Dialog */}
          {showLoadDialog && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-zinc-900 p-6 rounded-lg shadow-lg max-w-md w-full">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium dark:text-white">Team-Setup laden</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowLoadDialog(false)}
                    className="h-6 w-6 dark:hover:bg-zinc-800"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-2">
                    {savedSetups.length > 0 ? (
                      savedSetups
                        .sort((a, b) => b.timestamp - a.timestamp)
                        .map((setup) => (
                          <div
                            key={setup.id}
                            className="border border-gray-200 dark:border-zinc-700 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-zinc-800 cursor-pointer"
                            onClick={() => loadSetup(setup.id)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium dark:text-white">{setup.name}</h4>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatDate(setup.timestamp)}
                                </p>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {setup.teams.map((team) => (
                                    <Badge
                                      key={team.id}
                                      variant="outline"
                                      className="text-xs bg-gray-100 dark:bg-zinc-800 dark:border-zinc-700"
                                    >
                                      {team.name}: {team.players.length} Spieler
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 text-red-500 hover:bg-red-500/10 dark:hover:bg-red-500/20"
                                onClick={(e) => deleteSetup(setup.id, e)}
                                title="Löschen"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))
                    ) : (
                      <p className="text-center text-gray-500 dark:text-gray-400 py-4">
                        Keine gespeicherten Setups vorhanden
                      </p>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}

          {/* Player Selection */}
          <Card className="mb-6 dark:bg-zinc-800/50 dark:border-zinc-700">
            <CardHeader className="pb-2">
              <div className="space-y-1">
                <CardTitle className="text-lg dark:text-white">Spieler auswählen</CardTitle>
                <CardDescription className="dark:text-zinc-400">
                  Wählen Sie Spieler aus, um sie dem Spielerpool hinzuzufügen
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-zinc-500 h-4 w-4" />
                    <Input
                      placeholder="Spieler suchen..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={selectAllVisible}
                    className="dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-800"
                  >
                    Alle anzeigen
                  </Button>
                  <Button
                    variant="outline"
                    onClick={deselectAll}
                    className="dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-800"
                  >
                    Keine
                  </Button>
                </div>

                <div className="border rounded-md dark:border-zinc-700">
                  <ScrollArea className="h-[200px]">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 p-2">
                      {filteredPlayers.map((player) => (
                        <div
                          key={player.id}
                          className={cn(
                            "flex items-center space-x-2 rounded-md border px-3 py-2 text-sm",
                            selectedPlayerIds.includes(player.id || "")
                              ? "border-blue-500 dark:border-blue-600 bg-blue-50 dark:bg-blue-950/20"
                              : "border-gray-200 dark:border-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-800/50"
                          )}
                        >
                          <Checkbox
                            id={`player-${player.id}`}
                            checked={selectedPlayerIds.includes(player.id || "")}
                            onCheckedChange={() => togglePlayerSelection(player.id || "")}
                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                          />
                          <label htmlFor={`player-${player.id}`} className="flex-1 cursor-pointer">
                            <div className="flex justify-between dark:text-zinc-300">
                              <span>{player.name}</span>
                              <span className="text-xs bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded-full">
                                {player.rating}
                              </span>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                <div className="flex flex-wrap gap-2">
                  {selectedPlayerIds.length > 0 && (
                    <div className="w-full">
                      <p className="text-sm font-medium mb-2 dark:text-zinc-400">
                        Ausgewählte Spieler ({selectedPlayerIds.length}):
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {selectedPlayerIds.map((id) => {
                          const player = allPlayers.find((p) => p.id === id);
                          if (!player) return null;

                          return (
                            <Badge
                              key={id}
                              variant="secondary"
                              className="flex items-center gap-1 bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300"
                            >
                              {player.name}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 p-0 ml-1 hover:bg-gray-300 dark:hover:bg-zinc-600 rounded-full"
                                onClick={() => removePlayerFromSelection(id)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </Badge>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Configuration */}
          <Card className="mb-6 dark:bg-zinc-800/50 dark:border-zinc-700">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <div className="space-y-1">
                  <CardTitle className="text-lg dark:text-white">Teams konfigurieren</CardTitle>
                  <CardDescription className="dark:text-zinc-400">
                    Teams hinzufügen oder entfernen und Namen bearbeiten
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={addTeam}
                    variant="outline"
                    size="sm"
                    disabled={teams.length >= 4}
                    className="dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-800"
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Team hinzufügen
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {teams.map((team) => (
                  <div
                    key={team.id}
                    className="border rounded-md p-3 dark:border-zinc-700 dark:bg-zinc-800/50"
                  >
                    <div className="flex justify-between items-center mb-2">
                      {editingTeamId === team.id ? (
                        <div className="flex gap-2 w-full">
                          <Input
                            value={editingTeamName}
                            onChange={(e) => setEditingTeamName(e.target.value)}
                            className="h-8 dark:bg-zinc-700 dark:border-zinc-600"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") saveTeamName();
                              if (e.key === "Escape") setEditingTeamId(null);
                            }}
                            autoFocus
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 dark:hover:bg-zinc-700"
                            onClick={saveTeamName}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2 items-center">
                          <span className="font-medium dark:text-white">{team.name}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 dark:hover:bg-zinc-700"
                            onClick={() => startEditingTeamName(team.id, team.name)}
                          >
                            <Edit className="h-3 w-3 text-zinc-400" />
                          </Button>
                        </div>
                      )}

                      {teams.length > 2 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-red-500 hover:bg-red-500/10 dark:hover:bg-red-500/20"
                          onClick={() => removeTeam(team.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <Badge
                      className={cn(
                        "flex items-center gap-1 w-full justify-center py-1",
                        team.averageRating > 0
                          ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"
                      )}
                    >
                      <BarChart className="h-3 w-3" />
                      <span>Ø {team.averageRating.toFixed(1)}</span>
                      <span className="ml-1 text-xs">({team.players.length} Spieler)</span>
                    </Badge>

                    {/* Team Statistics */}
                    {teamStats[team.id] && team.players.length > 0 && (
                      <div className="mt-2 flex items-center gap-1.5 flex-wrap">
                        <div className="inline-flex items-center text-[10px] bg-amber-50 dark:bg-amber-950/30 text-amber-700 dark:text-amber-400 px-1 py-0.5 rounded-full">
                          <Award className="h-2.5 w-2.5 mr-0.5 text-amber-500" />
                          <span>MVP:{teamStats[team.id].avgMvps}</span>
                        </div>
                        <div className="inline-flex items-center text-[10px] bg-blue-50 dark:bg-blue-950/30 text-blue-700 dark:text-blue-400 px-1 py-0.5 rounded-full">
                          <Users className="h-2.5 w-2.5 mr-0.5 text-blue-600" />
                          <span>Spiele:{teamStats[team.id].avgTotalGames}</span>
                        </div>
                        <div className="inline-flex items-center text-[10px] bg-green-50 dark:bg-green-950/30 text-green-700 dark:text-green-400 px-1 py-0.5 rounded-full">
                          <Clock className="h-2.5 w-2.5 mr-0.5 text-green-500" />
                          <span>Rate:{teamStats[team.id].avgGamesPerMonth}/M</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Team Actions Card - Moved here after Team Configuration */}
          <Card className="mb-6 dark:bg-zinc-800/50 dark:border-zinc-700">
            <CardHeader className="pb-2">
              <div className="space-y-1">
                <CardTitle className="text-lg dark:text-white">Team Aktionen</CardTitle>
                <CardDescription className="dark:text-zinc-400">
                  Generieren und verwalten Sie Ihre Teams
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  onClick={generateBalancedTeams}
                  className="bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-500 h-auto py-4 flex flex-col items-center gap-2"
                  title="Generiert ausgewogene Teams basierend auf Spieler-Rating und Positionen"
                >
                  <div className="flex items-center gap-2">
                    <Wand className="h-5 w-5" />
                    <span className="font-semibold">Ausgewogene Teams</span>
                  </div>
                  <span className="text-xs opacity-90">
                    Optimiert für 5er, 10er oder 15er Teams
                  </span>
                </Button>

                <Button
                  onClick={autoBalanceTeams}
                  className="bg-team-primary hover:bg-team-primary/90 dark:bg-gray-400/80 dark:hover:bg-team-primary h-auto py-4 flex flex-col items-center gap-2"
                  title="Verteilt Spieler gleichmäßig basierend auf Rating"
                >
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-5 w-5" />
                    <span className="font-semibold">Teams ausbalancieren</span>
                  </div>
                  <span className="text-xs opacity-90">Schnelle Verteilung nach Rating</span>
                </Button>

                <Button
                  onClick={resetTeams}
                  variant="outline"
                  className="dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-800 h-auto py-4 flex flex-col items-center gap-2"
                >
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    <span className="font-semibold">Teams zurücksetzen</span>
                  </div>
                  <span className="text-xs opacity-90">Alle Spieler in den Pool</span>
                </Button>

                <div className="relative">
                  <Button
                    variant="outline"
                    className="w-full bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100 dark:bg-emerald-900/20 dark:border-emerald-800 dark:text-emerald-400 dark:hover:bg-emerald-900/40 h-auto py-4 flex flex-col items-center gap-2"
                    onClick={() =>
                      document.getElementById("exportOptions")?.classList.toggle("hidden")
                    }
                  >
                    <div className="flex items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="7 10 12 15 17 10" />
                        <line x1="12" y1="15" x2="12" y2="3" />
                      </svg>
                      <span className="font-semibold">Teams exportieren</span>
                    </div>
                    <span className="text-xs opacity-90">Als JSON, CSV oder PDF</span>
                  </Button>
                  <div
                    id="exportOptions"
                    className="absolute z-10 right-0 mt-2 w-56 origin-top-right rounded-md bg-white dark:bg-zinc-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                  >
                    <div className="py-1" role="none">
                      <button
                        onClick={exportAsJSON}
                        className="text-gray-700 dark:text-zinc-300 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-zinc-700"
                      >
                        Als JSON exportieren
                      </button>
                      <button
                        onClick={exportAsCSV}
                        className="text-gray-700 dark:text-zinc-300 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-zinc-700"
                      >
                        Als CSV exportieren
                      </button>
                      <button
                        onClick={printTeams}
                        className="text-gray-700 dark:text-zinc-300 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-zinc-700"
                      >
                        Druckansicht
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Info Box */}
              <div className="mt-6 p-4 bg-blue-50/30 dark:bg-blue-950/10 border border-blue-200 dark:border-blue-800/30 rounded-lg">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="grid sm:grid-cols-2 gap-4 w-full text-sm">
                    <div>
                      <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">
                        Ausgewogene Teams
                      </h4>
                      <ul className="space-y-1 text-blue-600/90 dark:text-blue-400/90 list-disc list-inside">
                        <li>Berücksichtigt Spielerpositionen</li>
                        <li>Optimiert für 5er Teams</li>
                        <li>Automatische Teamanzahl</li>
                        <li>Ideal für 10-20 Spieler</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">
                        Teams ausbalancieren
                      </h4>
                      <ul className="space-y-1 text-blue-600/90 dark:text-blue-400/90 list-disc list-inside">
                        <li>Verteilung nach Rating</li>
                        <li>Behält Teamanzahl bei</li>
                        <li>Flexible Spieleranzahl</li>
                        <li>Schnelle Anpassungen</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Grid */}
          <div className={`grid ${getGridLayoutClasses()}`}>
            {/* Player Pool */}
            <Card className="dark:bg-zinc-900 dark:border-zinc-800 lg:row-span-2">
              <CardHeader className="p-3 border-b bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700 rounded-t-lg">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg font-medium dark:text-white">Spielerpool</CardTitle>
                  <Badge
                    variant="outline"
                    className="bg-white dark:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-600"
                  >
                    {availablePlayers.length} Spieler
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-[60vh] lg:h-[70vh]">
                  <ul className="divide-y dark:divide-zinc-700">
                    {availablePlayers.length > 0 ? (
                      availablePlayers.map((player) => (
                        <li key={player.id} className="p-3 hover:bg-gray-50 dark:hover:bg-zinc-800">
                          <div className="flex flex-col gap-2 dark:text-zinc-300">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <PlayerName
                                  player={player}
                                  playerId={player.id}
                                  playerName={player.name}
                                  className="font-medium"
                                />
                                {player.jerseyNumber && (
                                  <span className="text-xs opacity-70 dark:opacity-50">
                                    #{player.jerseyNumber}
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center">
                                <span className="text-xs bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded-full mr-2">
                                  {player.rating}
                                </span>
                                <div className="flex gap-1">
                                  {teams.map((team) => (
                                    <Button
                                      key={team.id}
                                      size="icon"
                                      variant="ghost"
                                      className="h-7 w-7 rounded-full hover:bg-team-primary/10 dark:hover:bg-team-primary/20"
                                      onClick={() => addPlayerToTeam(player, team.id)}
                                      title={`Zu ${team.name} hinzufügen`}
                                    >
                                      <PlusCircle className="h-4 w-4 text-team-primary" />
                                    </Button>
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* Player Stats */}
                            <div className="grid grid-cols-3 gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1 w-full">
                              <div className="flex items-center gap-1 justify-between bg-gray-50 dark:bg-zinc-800/80 px-2 py-1 rounded">
                                <div className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  <span>Spiele:</span>
                                </div>
                                <span className="font-semibold">{player.totalGames}</span>
                              </div>
                              <div className="flex items-center gap-1 justify-between bg-gray-50 dark:bg-zinc-800/80 px-2 py-1 rounded">
                                <div className="flex items-center gap-1">
                                  <Award className="h-3 w-3" />
                                  <span>MVP:</span>
                                </div>
                                <span className="font-semibold">{player.mvpCount}</span>
                              </div>
                              <div className="flex items-center gap-1 justify-between bg-gray-50 dark:bg-zinc-800/80 px-2 py-1 rounded">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  <span>Rate:</span>
                                </div>
                                <span className="font-semibold">{player.participationRate}</span>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))
                    ) : (
                      <li className="p-6 text-center text-gray-500 dark:text-gray-400">
                        Keine Spieler im Pool verfügbar
                      </li>
                    )}
                  </ul>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Teams */}
            {teams.map((team, index) => (
              <Card key={team.id} className="dark:bg-zinc-900 dark:border-zinc-800">
                <CardHeader className="p-3 border-b bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700 rounded-t-lg">
                  <div className="flex justify-between items-center">
                    <div className="flex gap-2 items-center">
                      {editingTeamId === team.id ? (
                        <div className="flex gap-2 items-center">
                          <Input
                            value={editingTeamName}
                            onChange={(e) => setEditingTeamName(e.target.value)}
                            className="h-8 dark:bg-zinc-700 dark:border-zinc-600"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") saveTeamName();
                              if (e.key === "Escape") setEditingTeamId(null);
                            }}
                            autoFocus
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 dark:hover:bg-zinc-700"
                            onClick={saveTeamName}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <>
                          <CardTitle className="text-lg font-medium dark:text-white">
                            {team.name}
                          </CardTitle>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 dark:hover:bg-zinc-700"
                            onClick={() => startEditingTeamName(team.id, team.name)}
                          >
                            <Edit className="h-3 w-3 text-zinc-400" />
                          </Button>
                        </>
                      )}
                    </div>
                    <Badge
                      className={cn(
                        "flex items-center gap-1",
                        team.averageRating > 0
                          ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"
                      )}
                    >
                      <BarChart className="h-3 w-3" />Ø {team.averageRating.toFixed(1)}
                    </Badge>
                  </div>

                  {/* Team Statistics - Improved layout */}
                  {teamStats[team.id] && team.players.length > 0 && (
                    <div className="mt-2 grid grid-cols-3 gap-2">
                      <div className="flex items-center justify-between bg-gray-100 dark:bg-zinc-700/50 px-2 py-1 rounded text-xs">
                        <div className="flex items-center text-gray-600 dark:text-gray-400">
                          <Award className="h-3 w-3 mr-1 text-amber-500" />
                          <span>MVP:</span>
                        </div>
                        <span className="font-semibold dark:text-white">
                          {teamStats[team.id].avgMvps}
                        </span>
                      </div>
                      <div className="flex items-center justify-between bg-gray-100 dark:bg-zinc-700/50 px-2 py-1 rounded text-xs">
                        <div className="flex items-center text-gray-600 dark:text-gray-400">
                          <Users className="h-3 w-3 mr-1 text-blue-600" />
                          <span>Spiele:</span>
                        </div>
                        <span className="font-semibold dark:text-white">
                          {teamStats[team.id].avgTotalGames}
                        </span>
                      </div>
                      <div className="flex items-center justify-between bg-gray-100 dark:bg-zinc-700/50 px-2 py-1 rounded text-xs">
                        <div className="flex items-center text-gray-600 dark:text-gray-400">
                          <Clock className="h-3 w-3 mr-1 text-green-500" />
                          <span>Rate:</span>
                        </div>
                        <span className="font-semibold dark:text-white">
                          {teamStats[team.id].avgGamesPerMonth}/M
                        </span>
                      </div>
                    </div>
                  )}
                </CardHeader>
                <CardContent className="p-0">
                  <ScrollArea className="h-[60vh] lg:h-[40vh]">
                    <ul className="divide-y dark:divide-zinc-700">
                      {team.players.length > 0 ? (
                        team.players.map((player) => (
                          <li
                            key={player.id}
                            className="p-3 hover:bg-gray-50 dark:hover:bg-zinc-800"
                          >
                            <div className="flex justify-between items-center dark:text-zinc-300">
                              <div className="flex items-center gap-2">
                                <PlayerName
                                  player={player}
                                  playerId={player.id}
                                  playerName={player.name}
                                />
                                {player.jerseyNumber && (
                                  <span className="text-xs opacity-70 dark:opacity-50">
                                    #{player.jerseyNumber}
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center">
                                <span className="text-xs bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded-full mr-2">
                                  {player.rating}
                                </span>
                                <div className="flex gap-1">
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    className="h-7 w-7 rounded-full hover:bg-red-500/10 dark:hover:bg-red-500/20"
                                    onClick={() => removePlayerFromTeam(player, team.id)}
                                    title="Aus Team entfernen"
                                  >
                                    <MinusCircle className="h-4 w-4 text-red-500" />
                                  </Button>
                                  {teams.length > 1 && (
                                    <Select
                                      onValueChange={(targetTeamId) => {
                                        if (targetTeamId !== team.id) {
                                          // Move player to new team
                                          removePlayerFromTeam(player, team.id);
                                          const targetTeam = teams.find(
                                            (t) => t.id === targetTeamId
                                          );
                                          if (targetTeam) {
                                            addPlayerToTeam(player, targetTeamId);
                                          }
                                        }
                                      }}
                                    >
                                      <SelectTrigger className="h-7 w-7 p-0 border-0 bg-transparent hover:bg-gray-100 dark:hover:bg-zinc-700 rounded-full">
                                        <SelectValue
                                          placeholder={
                                            <ArrowRight className="h-4 w-4 text-gray-500" />
                                          }
                                        />
                                      </SelectTrigger>
                                      <SelectContent className="min-w-[8rem] dark:bg-zinc-800 dark:border-zinc-700">
                                        <SelectGroup>
                                          <SelectLabel className="dark:text-zinc-400">
                                            Zu Team verschieben
                                          </SelectLabel>
                                          {teams
                                            .filter((t) => t.id !== team.id)
                                            .map((targetTeam) => (
                                              <SelectItem
                                                key={targetTeam.id}
                                                value={targetTeam.id}
                                                className="dark:text-zinc-300 dark:focus:bg-zinc-700"
                                              >
                                                {targetTeam.name}
                                              </SelectItem>
                                            ))}
                                        </SelectGroup>
                                      </SelectContent>
                                    </Select>
                                  )}
                                </div>
                              </div>
                            </div>
                          </li>
                        ))
                      ) : (
                        <li className="p-6 text-center text-gray-500 dark:text-gray-400">
                          Keine Spieler im Team
                        </li>
                      )}
                    </ul>
                  </ScrollArea>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Add Team Comparison Panel */}
          {teams.filter((team) => team.players.length > 0).length >= 2 && (
            <Card className="mt-8 dark:bg-zinc-900 dark:border-zinc-800">
              <CardHeader className="pb-2 rounded-t-lg">
                <div className="flex justify-between items-center">
                  <div className="space-y-1">
                    <CardTitle className="text-lg dark:text-white">Team Vergleich</CardTitle>
                    <CardDescription className="dark:text-zinc-400">
                      Vergleichen Sie die Stärken und Schwächen der Teams
                    </CardDescription>
                  </div>
                </div>

                {/* Add imbalance warning if necessary
                {imbalancedStats.hasImbalance && (
                  <div className="mt-2 bg-yellow-50 dark:bg-yellow-900/30 p-3 rounded-md border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-400">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium">
                          Mögliche Team-Unausgeglichenheit erkannt
                        </p>
                        <p className="text-xs mt-0.5">
                          <strong>{imbalancedStats.maxTeam}</strong> hat einen
                          deutlich höheren Wert bei{" "}
                          <strong>{imbalancedStats.imbalancedStat}</strong>
                          (Differenz: {imbalancedStats.difference}).
                        </p>
                      </div>
                    </div>
                  </div>
                )} */}
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Radar Chart */}
                  <div className="h-80">
                    <p className="text-sm font-medium mb-2 dark:text-zinc-400 text-center">
                      Teamstärken im Vergleich
                    </p>
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart cx="50%" cy="50%" outerRadius="80%" data={formatChartData()}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="stat" />
                        <PolarRadiusAxis />
                        {teams.map(
                          (team, index) =>
                            team.players.length > 0 && (
                              <Radar
                                key={team.id}
                                name={team.name}
                                dataKey={team.name}
                                stroke={getTeamColor(index)}
                                fill={getTeamColor(index)}
                                fillOpacity={0.2}
                              />
                            )
                        )}
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Bar Chart */}
                  <div className="h-80">
                    <p className="text-sm font-medium mb-2 dark:text-zinc-400 text-center">
                      Statistische Übersicht
                    </p>
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsBarChart
                        data={formatBarData()}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="stat" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="rating" name="Rating" fill="#a5b4fc" />
                        <Bar dataKey="mvps" name="MVPs" fill="#a7f3d0" />
                        <Bar dataKey="games" name="Spiele" fill="#fcd34d" />
                      </RechartsBarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Function to get team color based on index - using pastel colors
function getTeamColor(index: number): string {
  const colors = [
    "#a5b4fc", // Pastel indigo
    "#a7f3d0", // Pastel emerald
    "#fcd34d", // Pastel amber
    "#fda4af", // Pastel rose
    "#a5f3fc", // Pastel cyan
    "#c4b5fd", // Pastel violet
    "#fdba74", // Pastel orange
    "#86efac", // Pastel green
  ];
  return colors[index % colors.length];
}

// Custom tooltip interface
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    name: string;
    value: number;
    color: string;
    payload?: {
      stat: string;
      [key: string]: string | number;
    };
  }>;
}

// Custom tooltip for radar chart
function CustomTooltip({ active, payload }: CustomTooltipProps) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-zinc-800 p-2 border border-gray-200 dark:border-zinc-700 rounded-md shadow-md">
        <p className="font-medium text-xs">{payload[0].payload?.stat}</p>
        {payload.map(
          (entry, index: number) =>
            entry.value > 0 && (
              <p key={`item-${index}`} className="text-xs" style={{ color: entry.color }}>
                {`${entry.name}: ${entry.value}`}
              </p>
            )
        )}
      </div>
    );
  }
  return null;
}
