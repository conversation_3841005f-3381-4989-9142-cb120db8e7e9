import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import AdminLogin from "@/components/admin/AdminLogin";
import LoadingSpinner from "@/components/LoadingSpinner";

interface AdminPanelLayoutProps {
  children: React.ReactNode;
  isLoggedIn: boolean;
  isLoading: boolean;
  onLoginSuccess: () => void;
}

export function AdminPanelLayout({
  children,
  isLoggedIn,
  isLoading,
  onLoginSuccess,
}: AdminPanelLayoutProps) {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!isLoggedIn) {
    return <AdminLogin onLoginSuccess={onLoginSuccess} />;
  }

  return <div className="space-y-6">{children}</div>;
}
