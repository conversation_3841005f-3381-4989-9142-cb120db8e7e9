import { useState, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { usePlayers } from "@/hooks/usePlayers";
import { Edit, Trash2, Search, Users, Plus, UserPlus2 } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useGameSessions } from "@/hooks/useGameSessions";
import { AdminPanelLayout } from "./AdminPanelLayout";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Separator } from "../ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { PlayerRole } from "@/types";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SlidersHorizontal, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const PlayerManagementPage = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(localStorage.getItem("isAdminLoggedIn") === "true");

  const { currentSession, loading: sessionLoading } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });

  const {
    players,
    createPlayer,
    updatePlayer,
    deletePlayer,
    loading: playersLoading,
  } = usePlayers(currentSession?.id);

  const [newPlayer, setNewPlayer] = useState({
    name: "",
    jerseyNumber: "",
    rating: "70",
    role: undefined as PlayerRole,
    is_active: true,
  });
  const [editingPlayer, setEditingPlayer] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const playersPerPage = 10;
  const [playerToDelete, setPlayerToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [isProcessingAction, setIsProcessingAction] = useState(false);
  const [isAddPlayerOpen, setIsAddPlayerOpen] = useState(false);
  const [filters, setFilters] = useState({
    role: undefined as PlayerRole | undefined,
    isMvp: undefined as boolean | undefined,
  });
  const [mvpPlayers, setMvpPlayers] = useState<Set<string>>(new Set());

  const roleOptions = [
    { value: "goalkeeper", label: "Torwart", color: "bg-yellow-500" },
    { value: "defender", label: "Verteidiger", color: "bg-blue-500" },
    { value: "midfielder", label: "Mittelfeld", color: "bg-green-500" },
    { value: "striker", label: "Stürmer", color: "bg-red-500" },
    { value: "allrounder", label: "Allrounder", color: "bg-purple-500" },
  ];

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Fetch MVP players when component mounts
  useEffect(() => {
    const fetchMvpPlayers = async () => {
      try {
        const { data: winners, error } = await supabase
          .from("mvp_winners")
          .select("player_id")
          .not("player_id", "is", null);

        if (error) throw error;

        // Create a Set of player IDs who have been MVP at least once
        const mvpPlayerIds = new Set(winners.map((w: any) => w.player_id));
        setMvpPlayers(mvpPlayerIds);
      } catch (error) {
        console.error("Error fetching MVP players:", error);
      }
    };

    fetchMvpPlayers();
  }, []);

  const handleCreate = async () => {
    if (!newPlayer.name) {
      toast({
        title: "Fehler",
        description: "Spielername ist erforderlich",
        variant: "destructive",
      });
      return;
    }

    setIsProcessingAction(true);
    try {
      await createPlayer({
        name: newPlayer.name,
        jerseyNumber: newPlayer.jerseyNumber ? parseInt(newPlayer.jerseyNumber) : undefined,
        rating: parseInt(newPlayer.rating),
        role: newPlayer.role,
        is_active: newPlayer.is_active,
      });

      setNewPlayer({ name: "", jerseyNumber: "", rating: "70", role: undefined, is_active: true });
      setIsAddPlayerOpen(false);
      toast({
        title: "Spieler erstellt",
        description: "Der Spieler wurde erfolgreich erstellt",
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Es ist ein Fehler beim Erstellen des Spielers aufgetreten",
        variant: "destructive",
      });
    } finally {
      setIsProcessingAction(false);
    }
  };

  const handleUpdate = async () => {
    if (!editingPlayer?.id) return;

    setIsProcessingAction(true);
    try {
      await updatePlayer(editingPlayer.id, {
        name: editingPlayer.name,
        jerseyNumber: editingPlayer.jerseyNumber ? parseInt(editingPlayer.jerseyNumber) : undefined,
        rating: parseInt(editingPlayer.rating),
        role: editingPlayer.role,
        is_active: editingPlayer.is_active,
      });

      setEditingPlayer(null);
      toast({
        title: "Spieler aktualisiert",
        description: "Spielerdaten wurden erfolgreich aktualisiert",
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Es ist ein Fehler beim Aktualisieren des Spielers aufgetreten",
        variant: "destructive",
      });
    } finally {
      setIsProcessingAction(false);
    }
  };

  const handleDelete = (id: string, name: string) => {
    setPlayerToDelete({ id, name });
  };

  const confirmDelete = async () => {
    if (!playerToDelete) return;

    setIsProcessingAction(true);
    try {
      await deletePlayer(playerToDelete.id);
      toast({
        title: "Spieler gelöscht",
        description: `${playerToDelete.name} wurde erfolgreich entfernt.`,
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Beim Löschen des Spielers ist ein Fehler aufgetreten.",
        variant: "destructive",
      });
    } finally {
      setPlayerToDelete(null);
      setIsProcessingAction(false);
    }
  };

  const getRoleDisplayName = (role?: string) => {
    const roleOption = roleOptions.find((option) => option.value === role);
    return roleOption ? roleOption.label : "—";
  };

  const getRoleColor = (role?: string) => {
    const roleOption = roleOptions.find((option) => option.value === role);
    return roleOption ? roleOption.color : "bg-gray-500";
  };

  const filteredPlayers = useMemo(() => {
    return players.filter((player) => {
      // Text search
      const matchesSearch =
        searchQuery === "" ||
        player.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (player.jerseyNumber?.toString() || "").includes(searchQuery);

      // Role filter
      const matchesRole = !filters.role || player.role === filters.role;

      // MVP filter
      const matchesMvp =
        filters.isMvp === undefined ||
        (filters.isMvp === true ? mvpPlayers.has(player.id!) : !mvpPlayers.has(player.id!));

      return matchesSearch && matchesRole && matchesMvp;
    });
  }, [players, searchQuery, filters, mvpPlayers]);

  const paginationData = useMemo(() => {
    const indexOfLastPlayer = currentPage * playersPerPage;
    const indexOfFirstPlayer = indexOfLastPlayer - playersPerPage;
    const currentPlayers = filteredPlayers.slice(indexOfFirstPlayer, indexOfLastPlayer);
    const totalPages = Math.ceil(filteredPlayers.length / playersPerPage);

    return { currentPlayers, totalPages };
  }, [filteredPlayers, currentPage, playersPerPage]);

  const { currentPlayers, totalPages } = paginationData;

  const clearFilters = () => {
    setFilters({
      role: undefined,
      isMvp: undefined,
    });
  };

  const hasActiveFilters = filters.role !== undefined || filters.isMvp !== undefined;

  return (
    <AdminPanelLayout
      isLoggedIn={isLoggedIn}
      isLoading={sessionLoading || playersLoading}
      onLoginSuccess={() => setIsLoggedIn(true)}
    >
      <div className="space-y-6">
        {/* Header Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="dark:bg-zinc-900 dark:border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium dark:text-zinc-400">
                Gesamtspieler
              </CardTitle>
              <Users className="h-4 w-4 text-team-accent dark:text-team-accent/80" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold dark:text-white">{players.length}</div>
              <p className="text-xs text-muted-foreground dark:text-zinc-500">
                Aktive Spieler im Kader
              </p>
            </CardContent>
          </Card>
          {roleOptions.map((role) => (
            <Card key={role.value} className="dark:bg-zinc-900 dark:border-zinc-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium dark:text-zinc-400">
                  {role.label}
                </CardTitle>
                <div className={`h-2 w-2 rounded-full ${role.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold dark:text-white">
                  {players.filter((p) => p.role === role.value).length}
                </div>
                <p className="text-xs text-muted-foreground dark:text-zinc-500">
                  Registrierte {role.label}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content */}
        <Card className="dark:bg-zinc-900 dark:border-zinc-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-bold dark:text-white">
                  Spielerverwaltung
                </CardTitle>
                <CardDescription className="dark:text-zinc-400">
                  Verwalten Sie Spielerprofile, Positionen, Bewertungen und Trikotnummern
                </CardDescription>
              </div>
              <Sheet open={isAddPlayerOpen} onOpenChange={setIsAddPlayerOpen}>
                <SheetTrigger asChild>
                  <Button className="bg-team-accent hover:bg-team-accent/90 text-white">
                    <UserPlus2 className="md:mr-2 h-4 w-4" />
                    <span className="hidden md:inline">Spieler hinzufügen</span>
                  </Button>
                </SheetTrigger>
                <SheetContent className="dark:bg-zinc-900 dark:border-zinc-800">
                  <SheetHeader>
                    <SheetTitle className="dark:text-white">Neuen Spieler hinzufügen</SheetTitle>
                    <SheetDescription className="dark:text-zinc-400">
                      Fügen Sie die Details des neuen Spielers hinzu
                    </SheetDescription>
                  </SheetHeader>
                  <div className="space-y-4 mt-6">
                    <div className="space-y-2">
                      <Input
                        placeholder="Spielername"
                        value={newPlayer.name}
                        onChange={(e) =>
                          setNewPlayer((prev) => ({ ...prev, name: e.target.value }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Input
                        placeholder="Trikotnummer (optional)"
                        type="number"
                        value={newPlayer.jerseyNumber}
                        onChange={(e) =>
                          setNewPlayer((prev) => ({
                            ...prev,
                            jerseyNumber: e.target.value,
                          }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Input
                        placeholder="Bewertung (1-99)"
                        type="number"
                        min="1"
                        max="99"
                        value={newPlayer.rating}
                        onChange={(e) =>
                          setNewPlayer((prev) => ({ ...prev, rating: e.target.value }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Select
                        value={newPlayer.role}
                        onValueChange={(value) =>
                          setNewPlayer((prev) => ({ ...prev, role: value as PlayerRole }))
                        }
                      >
                        <SelectTrigger className="w-full dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300">
                          <SelectValue placeholder="Position (optional)" />
                        </SelectTrigger>
                        <SelectContent className="dark:bg-zinc-800 dark:border-zinc-700">
                          {roleOptions.map((role) => (
                            <SelectItem
                              key={role.value}
                              value={role.value}
                              className="dark:text-zinc-300 dark:hover:bg-zinc-700"
                            >
                              {role.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      onClick={handleCreate}
                      className="w-full bg-team-accent text-white hover:bg-team-accent/80"
                      disabled={isProcessingAction}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Spieler hinzufügen
                    </Button>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            <Separator className="dark:bg-zinc-800 mt-4" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-zinc-500 h-4 w-4" />
                  <Input
                    placeholder="Spieler suchen..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      >
                        <SlidersHorizontal className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="dark:bg-zinc-800 dark:border-zinc-700">
                      <DropdownMenuLabel className="dark:text-zinc-300">Position</DropdownMenuLabel>
                      <DropdownMenuSeparator className="dark:bg-zinc-700" />
                      {roleOptions.map((role) => (
                        <DropdownMenuItem
                          key={role.value}
                          className="dark:text-zinc-300 dark:hover:bg-zinc-700 cursor-pointer"
                          onClick={() =>
                            setFilters((prev) => ({ ...prev, role: role.value as PlayerRole }))
                          }
                        >
                          <div className={`h-2 w-2 rounded-full ${role.color} mr-2`} />
                          {role.label}
                        </DropdownMenuItem>
                      ))}
                      <DropdownMenuSeparator className="dark:bg-zinc-700" />
                      <DropdownMenuLabel className="dark:text-zinc-300">
                        MVP Status
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator className="dark:bg-zinc-700" />
                      <DropdownMenuItem
                        className="dark:text-zinc-300 dark:hover:bg-zinc-700 cursor-pointer"
                        onClick={() => setFilters((prev) => ({ ...prev, isMvp: true }))}
                      >
                        MVP
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="dark:text-zinc-300 dark:hover:bg-zinc-700 cursor-pointer"
                        onClick={() => setFilters((prev) => ({ ...prev, isMvp: false }))}
                      >
                        Nicht MVP
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  {hasActiveFilters && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={clearFilters}
                      className="dark:text-zinc-400 dark:hover:bg-zinc-800"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {hasActiveFilters && (
                <div className="flex flex-wrap gap-2">
                  {filters.role && (
                    <Badge
                      variant="secondary"
                      className="dark:bg-zinc-800"
                      onClick={() => setFilters((prev) => ({ ...prev, role: undefined }))}
                    >
                      {getRoleDisplayName(filters.role)}
                      <X className="h-3 w-3 ml-1 cursor-pointer" />
                    </Badge>
                  )}
                  {filters.isMvp !== undefined && (
                    <Badge
                      variant="secondary"
                      className="dark:bg-zinc-800"
                      onClick={() => setFilters((prev) => ({ ...prev, isMvp: undefined }))}
                    >
                      {filters.isMvp ? "MVP" : "Nicht MVP"}
                      <X className="h-3 w-3 ml-1 cursor-pointer" />
                    </Badge>
                  )}
                </div>
              )}
            </div>

            <div className="overflow-x-auto -mx-6 px-6 mt-6">
              <Table>
                <TableHeader className="dark:bg-zinc-800/50">
                  <TableRow className="dark:border-zinc-700 hover:dark:bg-zinc-800">
                    <TableHead className="dark:text-zinc-300">Name</TableHead>
                    <TableHead className="dark:text-zinc-300 hidden sm:table-cell">#</TableHead>
                    <TableHead className="dark:text-zinc-300">Ø</TableHead>
                    <TableHead className="dark:text-zinc-300 hidden md:table-cell">
                      Position
                    </TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPlayers.length === 0 ? (
                    <TableRow className="dark:border-zinc-700 hover:dark:bg-zinc-800">
                      <TableCell colSpan={5} className="text-center py-8 dark:text-zinc-400">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <Users className="h-8 w-8 text-zinc-400" />
                          <p>Keine Spieler gefunden</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentPlayers.map((player) => (
                      <TableRow
                        key={player.id}
                        className={`dark:border-zinc-700 hover:dark:bg-zinc-800 ${!player.is_active ? "opacity-50" : ""}`}
                      >
                        <TableCell className="dark:text-zinc-300 font-medium">
                          {player.name}
                          {!player.is_active && (
                            <Badge variant="outline" className="ml-2 dark:border-zinc-700">
                              Inaktiv
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="dark:text-zinc-300 hidden sm:table-cell">
                          {player.jerseyNumber ? (
                            <Badge variant="outline" className="dark:border-zinc-700">
                              #{player.jerseyNumber}
                            </Badge>
                          ) : (
                            "—"
                          )}
                        </TableCell>
                        <TableCell className="dark:text-zinc-300">
                          <Badge
                            className={`${
                              player.rating >= 90
                                ? "bg-emerald-500"
                                : player.rating >= 80
                                  ? "bg-green-500"
                                  : player.rating >= 70
                                    ? "bg-yellow-500"
                                    : player.rating >= 60
                                      ? "bg-orange-500"
                                      : "bg-red-500"
                            } text-white`}
                          >
                            {player.rating}
                          </Badge>
                        </TableCell>
                        <TableCell className="dark:text-zinc-300 hidden md:table-cell">
                          {player.role ? (
                            <Badge className={`${getRoleColor(player.role)} text-white`}>
                              {getRoleDisplayName(player.role)}
                            </Badge>
                          ) : (
                            "—"
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingPlayer(player)}
                              disabled={isProcessingAction}
                              className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-700"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(player.id!, player.name)}
                              disabled={isProcessingAction}
                              className="dark:bg-red-950/50 dark:hover:bg-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="mt-6">
                <Pagination className="justify-center">
                  <PaginationContent className="flex-wrap gap-1">
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        className={
                          currentPage === 1
                            ? "pointer-events-none opacity-50"
                            : "dark:text-zinc-300 dark:border-zinc-700 dark:hover:bg-zinc-800"
                        }
                      />
                    </PaginationItem>

                    {Array.from({ length: totalPages }).map((_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => setCurrentPage(i + 1)}
                          className={
                            currentPage === i + 1
                              ? "dark:bg-zinc-800 dark:text-white"
                              : "dark:text-zinc-300 dark:hover:bg-zinc-800"
                          }
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                        className={
                          currentPage === totalPages
                            ? "pointer-events-none opacity-50"
                            : "dark:text-zinc-300 dark:border-zinc-700 dark:hover:bg-zinc-800"
                        }
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}

            {editingPlayer && (
              <Sheet open={!!editingPlayer} onOpenChange={() => setEditingPlayer(null)}>
                <SheetContent className="dark:bg-zinc-900 dark:border-zinc-800">
                  <SheetHeader>
                    <SheetTitle className="dark:text-white">Spieler bearbeiten</SheetTitle>
                    <SheetDescription className="dark:text-zinc-400">
                      Bearbeiten Sie die Details von {editingPlayer.name}
                    </SheetDescription>
                  </SheetHeader>
                  <div className="space-y-4 mt-6">
                    <div className="space-y-2">
                      <Input
                        placeholder="Spielername"
                        value={editingPlayer.name}
                        onChange={(e) =>
                          setEditingPlayer((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Input
                        placeholder="Trikotnummer"
                        type="number"
                        value={editingPlayer.jerseyNumber || ""}
                        onChange={(e) =>
                          setEditingPlayer((prev) => ({
                            ...prev,
                            jerseyNumber: e.target.value,
                          }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Input
                        placeholder="Bewertung (1-99)"
                        type="number"
                        min="1"
                        max="99"
                        value={editingPlayer.rating}
                        onChange={(e) =>
                          setEditingPlayer((prev) => ({
                            ...prev,
                            rating: e.target.value,
                          }))
                        }
                        className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <Select
                        value={editingPlayer.role}
                        onValueChange={(value) =>
                          setEditingPlayer((prev) => ({ ...prev, role: value }))
                        }
                      >
                        <SelectTrigger className="dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300">
                          <SelectValue placeholder="Position wählen" />
                        </SelectTrigger>
                        <SelectContent className="dark:bg-zinc-800 dark:border-zinc-700">
                          {roleOptions.map((role) => (
                            <SelectItem
                              key={role.value}
                              value={role.value}
                              className="dark:text-zinc-300 dark:hover:bg-zinc-700"
                            >
                              {role.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is-active"
                        checked={editingPlayer.is_active}
                        onCheckedChange={(checked) =>
                          setEditingPlayer((prev) => ({
                            ...prev,
                            is_active: checked,
                          }))
                        }
                      />
                      <Label htmlFor="is-active">Aktiver Spieler</Label>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={handleUpdate}
                        disabled={isProcessingAction}
                        className="flex-1 bg-team-accent hover:bg-team-accent/90 text-white"
                      >
                        Aktualisieren
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setEditingPlayer(null)}
                        className="flex-1 dark:bg-zinc-800 dark:border-zinc-700 dark:text-zinc-300 dark:hover:bg-zinc-700"
                      >
                        Abbrechen
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            )}
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={!!playerToDelete} onOpenChange={() => setPlayerToDelete(null)}>
        <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
          <AlertDialogHeader>
            <AlertDialogTitle className="dark:text-white">Spieler löschen</AlertDialogTitle>
            <AlertDialogDescription className="dark:text-zinc-400">
              Möchten Sie den Spieler "{playerToDelete?.name}" wirklich löschen? Diese Aktion kann
              nicht rückgängig gemacht werden.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
              Abbrechen
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-900 dark:hover:bg-red-800"
            >
              Löschen
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminPanelLayout>
  );
};

export default PlayerManagementPage;
