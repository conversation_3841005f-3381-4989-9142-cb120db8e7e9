import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  History,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Menu,
} from "lucide-react";

interface SidebarLayoutProps {
  children: React.ReactNode;
  activePage: string;
  onNavigate: (page: string) => void;
  onLogout: () => void;
}

export function SidebarLayout({ children, activePage, onNavigate, onLogout }: SidebarLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      setCollapsed(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const navItems = [
    { id: "dashboard", icon: <LayoutDashboard className="h-5 w-5" />, label: "Dashboard" },
    { id: "game-management", icon: <Calendar className="h-5 w-5" />, label: "Spielplanung" },
    { id: "player-management", icon: <Users className="h-5 w-5" />, label: "Spielerverwaltung" },
    { id: "history", icon: <History className="h-5 w-5" />, label: "Historie" },
    { id: "settings", icon: <Settings className="h-5 w-5" />, label: "Einstellungen" },
  ];

  return (
    <div className="flex min-h-[calc(100vh-theme(spacing.32))] w-full bg-white">
      <div
        className={cn(
          "h-full bg-white border-r border-gray-200 transition-all duration-300",
          collapsed ? "w-16" : "w-64"
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!collapsed && <h2 className="font-semibold text-lg">Navigation</h2>}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className={cn("p-1", collapsed && "mx-auto")}
          >
            {collapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
          </Button>
        </div>

        <nav className="flex-1 p-2">
          <ul className="space-y-1">
            {navItems.map((item) => (
              <li key={item.id}>
                <Button
                  variant={activePage === item.id ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    collapsed ? "px-2" : "px-4",
                    activePage === item.id &&
                      "bg-team-primary text-white hover:bg-team-accent hover:text-white"
                  )}
                  onClick={() => onNavigate(item.id)}
                >
                  {item.icon}
                  {!collapsed && <span className="ml-3">{item.label}</span>}
                </Button>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-2 border-t border-gray-200">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
              collapsed ? "px-2" : "px-4"
            )}
            onClick={onLogout}
          >
            <LogOut className="h-5 w-5" />
            {!collapsed && <span className="ml-3">Abmelden</span>}
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">{children}</div>
    </div>
  );
}
