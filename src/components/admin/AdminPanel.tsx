import { useState, useEffect } from "react";
import { useGameSessions } from "@/hooks/useGameSessions";
import { usePlayers } from "@/hooks/usePlayers";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { AdminPanelLayout } from "./AdminPanelLayout";
import TeamDisplay from "../TeamDisplay";
import { formatDate } from "@/utils/dateUtils";
import LoadingSpinner from "../LoadingSpinner";
import { MVPVotingCard } from "./MVPVotingCard";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { Calendar, Users, UserCheck, UserX, HelpCircle, Settings, Award } from "lucide-react";
import { Separator } from "../ui/separator";

export default function AdminPanel() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const navigate = useNavigate();

  const { currentSession, loading } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });

  const { players, loading: playersLoading } = usePlayers(currentSession?.id);

  useEffect(() => {
    const adminLoggedIn = localStorage.getItem("isAdminLoggedIn") === "true";
    setIsLoggedIn(adminLoggedIn);
  }, []);

  const getPlayerStatusPercentage = (status) => {
    if (!players?.length) return 0;
    return Math.round((players.filter((p) => p.status === status).length / players.length) * 100);
  };

  const handleNavigation = (path: string) => {
    window.location.href = path;
  };

  return (
    <AdminPanelLayout
      isLoggedIn={isLoggedIn}
      isLoading={loading}
      onLoginSuccess={() => setIsLoggedIn(true)}
    >
      <div className="space-y-6">
        {/* Main Grid */}
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {/* Quick Stats */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="dark:bg-zinc-900/50">
                <CardContent className="p-4">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Gesamt Spieler</span>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-primary" />
                      <span className="text-2xl font-bold">{players?.length || 0}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Angemeldet</span>
                    <div className="flex items-center gap-2">
                      <UserCheck className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <span className="text-2xl font-bold">
                        {players?.filter((p) => p.status === "in").length || 0}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="dark:bg-zinc-900/50">
                <CardContent className="p-4">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Abgemeldet</span>
                    <div className="flex items-center gap-2">
                      <UserX className="h-4 w-4 text-rose-600 dark:text-rose-500" />
                      <span className="text-2xl font-bold">
                        {players?.filter((p) => p.status === "out").length || 0}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="dark:bg-zinc-900/50">
                <CardContent className="p-4">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Enthalten</span>
                    <div className="flex items-center gap-2">
                      <HelpCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-500" />
                      <span className="text-2xl font-bold">
                        {players?.filter((p) => p.status === "pending").length || 0}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Game Status Card */}
          <Card className="lg:row-span-2">
            <CardHeader>
              <div className="space-y-1">
                <CardTitle className="flex items-center justify-between">
                  <span>Spielstatus</span>
                  {currentSession?.isSignupOpen ? (
                    <Badge
                      variant="secondary"
                      className="bg-green-500/10 text-green-500 hover:bg-green-500/20 border-0"
                    >
                      Geöffnet
                    </Badge>
                  ) : (
                    <Badge variant="destructive">Geschlossen</Badge>
                  )}
                </CardTitle>
                <CardDescription>Übersicht des aktuellen Spielstatus</CardDescription>
              </div>
              <Separator className="dark:bg-zinc-800" />
            </CardHeader>
            <CardContent className="pt-3 space-y-4">
              {loading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : !currentSession ? (
                <div className="text-center p-6">
                  <p className="text-muted-foreground">Kein Spiel geplant</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => navigate("/admin/game-management")}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    Spiel planen
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Datum & Zeit</span>
                      <span className="font-medium">{formatDate(currentSession.date)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Spieldauer</span>
                      <span className="font-medium">{currentSession.duration_minutes} Minuten</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Teams</span>
                      <span className="font-medium">
                        {currentSession.isTeamGenerated ? "Generiert" : "Ausstehend"}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-muted-foreground">Anmeldungen</span>
                        <span className="text-sm font-medium">
                          {getPlayerStatusPercentage("in")}%
                        </span>
                      </div>
                      <Progress value={getPlayerStatusPercentage("in")} className="h-2" />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Player Statistics */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="space-y-1">
                <CardTitle>Spielerstatistiken</CardTitle>
                <CardDescription>Detaillierte Übersicht der Anmeldungen</CardDescription>
              </div>
              <Separator className="dark:bg-zinc-800" />
            </CardHeader>
            <CardContent className="pt-3 space-y-4">
              {playersLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Angemeldet</span>
                        <span className="text-sm text-muted-foreground">
                          {getPlayerStatusPercentage("in")}%
                        </span>
                      </div>
                      <Progress
                        value={getPlayerStatusPercentage("in")}
                        className="h-2 bg-blue-100 dark:bg-blue-900/30"
                        indicatorClassName="bg-blue-600 dark:bg-blue-400"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Abgemeldet</span>
                        <span className="text-sm text-muted-foreground">
                          {getPlayerStatusPercentage("out")}%
                        </span>
                      </div>
                      <Progress
                        value={getPlayerStatusPercentage("out")}
                        className="h-2 bg-rose-100 dark:bg-rose-900/30"
                        indicatorClassName="bg-rose-600 dark:bg-rose-500"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Enthalten</span>
                        <span className="text-sm text-muted-foreground">
                          {getPlayerStatusPercentage("pending")}%
                        </span>
                      </div>
                      <Progress
                        value={getPlayerStatusPercentage("pending")}
                        className="h-2 bg-yellow-100 dark:bg-yellow-900/30"
                        indicatorClassName="bg-yellow-600 dark:bg-yellow-500"
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Team Preview Section */}
        {currentSession?.isTeamGenerated && (
          <div className="space-y-4">
            <TeamDisplay isAdminView={true} />
          </div>
        )}

        {/* MVP Voting Section */}
        <Card>
          <CardHeader>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <CardTitle>MVP-Voting</CardTitle>
              </div>
              <CardDescription>
                Überwache die aktuelle MVP-Abstimmung für das letzte Spiel
              </CardDescription>
            </div>
            <Separator className="dark:bg-zinc-800" />
          </CardHeader>
          <CardContent className="pt-3 space-y-4">
            <MVPVotingCard showCloseButton={false} />
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}
