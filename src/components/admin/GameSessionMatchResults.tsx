import { useState, useEffect } from "react";
import {
  getMatchResultsForSession,
  addMatchResult,
  updateMatchResult,
  deleteMatchResult,
} from "@/services/matchResultsService";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MatchResult } from "@/types/match-results";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { Trophy, Plus, Pencil, Trash2, X, Hash, Download } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTournament } from "@/hooks/useTournament";
import { toast } from "@/components/ui/use-toast";
import { syncTournamentResultsToMatchResults } from "@/services/tournamentResultsService";

// Modernes, vereinfachtes Formular
function SimpleMatchResultForm({
  teams,
  onSubmit,
  onCancel,
  initialData,
}: {
  teams: { id: string; name: string }[];
  onSubmit: (data: Partial<MatchResult>) => void;
  onCancel: () => void;
  initialData?: Partial<MatchResult>;
}) {
  const [teamA, setTeamA] = useState(initialData?.team_a_id || "");
  const [teamB, setTeamB] = useState(initialData?.team_b_id || "");
  const [goalsA, setGoalsA] = useState(initialData?.goals_a ?? "");
  const [goalsB, setGoalsB] = useState(initialData?.goals_b ?? "");
  const [field, setField] = useState(initialData?.field || "");
  const [matchOrder, setMatchOrder] = useState(initialData?.match_order || "");
  const [timeSlot, setTimeSlot] = useState(initialData?.time_slot || "");

  const getTeamName = (id: string) => teams.find((t) => t.id === id)?.name || "";
  const [activeTab, setActiveTab] = useState<string>("basic");

  return (
    <Card className="mb-4 border-0 shadow-lg">
      <CardContent className="p-0">
        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-900 border-b rounded-t-lg">
          <h3 className="font-medium text-lg">
            {initialData ? "Ergebnis bearbeiten" : "Neues Ergebnis"}
          </h3>
          <Button variant="ghost" size="sm" onClick={onCancel} className="h-8 w-8 p-0 rounded-full">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form
          className="p-6"
          onSubmit={(e) => {
            e.preventDefault();
            onSubmit({
              team_a_id: teamA,
              team_b_id: teamB,
              goals_a: Number(goalsA),
              goals_b: Number(goalsB),
              field: field || null,
              match_order: matchOrder ? Number(matchOrder) : null,
              time_slot: timeSlot || null,
            });
          }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="basic">Grundlegende Infos</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              {/* Spielreihenfolge */}
              <div className="w-full">
                <Label className="text-sm" htmlFor="matchOrder">
                  Spielreihenfolge
                </Label>
                <div className="relative mt-1.5">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                    <Hash className="h-4 w-4" />
                  </span>
                  <Input
                    id="matchOrder"
                    type="number"
                    value={matchOrder}
                    onChange={(e) => setMatchOrder(e.target.value)}
                    className="pl-10"
                    min={1}
                    placeholder="1, 2, 3, ..."
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1.5">
                  Spielreihenfolge zur besseren Sortierung (z.B. 1 für das erste Spiel)
                </p>
              </div>

              {/* Teams und Ergebnis */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                {/* Team A */}
                <div className="space-y-1.5">
                  <Label className="text-sm" htmlFor="teamA">
                    Team A
                  </Label>
                  <Select value={teamA} onValueChange={setTeamA}>
                    <SelectTrigger id="teamA">
                      <SelectValue placeholder="Team auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Score */}
                <div className="flex items-center justify-center gap-2">
                  <div className="space-y-1.5 w-20">
                    <Label className="text-sm" htmlFor="goalsA">
                      Tore A
                    </Label>
                    <Input
                      id="goalsA"
                      type="number"
                      value={goalsA}
                      onChange={(e) => setGoalsA(e.target.value)}
                      min={0}
                      placeholder="0"
                      className="text-center"
                    />
                  </div>
                  <span className="mt-6 mx-1 font-bold text-xl">:</span>
                  <div className="space-y-1.5 w-20">
                    <Label className="text-sm" htmlFor="goalsB">
                      Tore B
                    </Label>
                    <Input
                      id="goalsB"
                      type="number"
                      value={goalsB}
                      onChange={(e) => setGoalsB(e.target.value)}
                      min={0}
                      placeholder="0"
                      className="text-center"
                    />
                  </div>
                </div>

                {/* Team B */}
                <div className="space-y-1.5">
                  <Label className="text-sm" htmlFor="teamB">
                    Team B
                  </Label>
                  <Select value={teamB} onValueChange={setTeamB}>
                    <SelectTrigger id="teamB">
                      <SelectValue placeholder="Team auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <div className="space-y-1.5">
                <Label className="text-sm" htmlFor="field">
                  Spielfeld (optional)
                </Label>
                <Input
                  id="field"
                  type="text"
                  value={field}
                  onChange={(e) => setField(e.target.value)}
                  placeholder="z.B. Feld A"
                />
              </div>

              <div className="space-y-1.5">
                <Label className="text-sm" htmlFor="timeSlot">
                  Zeitfenster (optional)
                </Label>
                <Input
                  id="timeSlot"
                  type="datetime-local"
                  value={timeSlot ?? ""}
                  onChange={(e) => setTimeSlot(e.target.value)}
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex gap-2 justify-end pt-6 mt-6 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              Abbrechen
            </Button>
            <Button type="submit">{initialData ? "Aktualisieren" : "Speichern"}</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// Haupt-Komponente
export default function GameSessionMatchResults({
  gameSessionId,
  teams,
}: {
  gameSessionId: string;
  teams: { id: string; name: string }[];
}) {
  const [results, setResults] = useState<MatchResult[]>([]);
  const [editing, setEditing] = useState<MatchResult | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get tournament data if available
  const { tournament, updateMatchResult: updateTournamentMatch } = useTournament({
    gameSessionId,
  });

  // Ergebnisse laden
  useEffect(() => {
    if (gameSessionId) {
      console.debug(
        `[MatchResults] Loading results for session: ${gameSessionId}, found ${teams.length} teams`
      );
      setLoading(true);
      getMatchResultsForSession(gameSessionId)
        .then((data) => {
          console.debug(`[MatchResults] Loaded ${data.length} results`);
          setResults(data);
          setError(null);
        })
        .catch((err) => {
          console.error("[MatchResults] Error loading results:", err);
          setError("Fehler beim Laden der Ergebnisse");
        })
        .finally(() => setLoading(false));
    }
  }, [gameSessionId, teams.length]);

  // Validiere, ob Teams verfügbar sind
  useEffect(() => {
    if (teams.length === 0) {
      console.warn("[MatchResults] Keine Teams verfügbar für Ergebniseintragung");
      setError("Keine Teams verfügbar. Bitte sicherstellen, dass Teams generiert wurden.");
    } else {
      setError(null);
    }
  }, [teams]);

  const handleAdd = async (data: Partial<MatchResult>) => {
    setLoading(true);
    try {
      const result = await addMatchResult({ ...data, game_session_id: gameSessionId } as any);
      console.debug(`[MatchResults] Added new result: ${result.id}`);
      setResults((prev) => [...prev, result]);
      setShowForm(false);
      setError(null);
    } catch (err) {
      console.error("[MatchResults] Error adding result:", err);
      setError("Fehler beim Hinzufügen des Ergebnisses");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = async (id: string, data: Partial<MatchResult>) => {
    setLoading(true);
    try {
      const result = await updateMatchResult(id, data);
      console.debug(`[MatchResults] Updated result: ${id}`);
      setResults((prev) => prev.map((r) => (r.id === id ? result : r)));
      setEditing(null);
      setError(null);
    } catch (err) {
      console.error("[MatchResults] Error updating result:", err);
      setError("Fehler beim Aktualisieren des Ergebnisses");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    setLoading(true);
    try {
      await deleteMatchResult(id);
      console.debug(`[MatchResults] Deleted result: ${id}`);
      setResults((prev) => prev.filter((r) => r.id !== id));
      setError(null);
    } catch (err) {
      console.error("[MatchResults] Error deleting result:", err);
      setError("Fehler beim Löschen des Ergebnisses");
    } finally {
      setLoading(false);
    }
  };

  const getTeamName = (id: string) => teams.find((t) => t.id === id)?.name || id;

  // Ergebnisse nach Spielreihenfolge sortieren
  const sortedResults = [...results].sort((a, b) => {
    // Zuerst nach match_order sortieren (falls vorhanden)
    if (a.match_order && b.match_order) {
      return a.match_order - b.match_order;
    }
    // Falls match_order nicht gesetzt ist, nach Erstellungsdatum sortieren
    if (a.match_order && !b.match_order) return -1;
    if (!a.match_order && b.match_order) return 1;

    // Beide haben keine match_order, sortiere nach created_at
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });

  // Der neu gestaltete, mobilfreundliche UI-Bereich
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-2 sm:items-center justify-between">
        <h2 className="text-lg font-bold">Spielergebnisse eintragen</h2>

        <div className="flex flex-wrap gap-2">
          {/* Import from tournament button */}
          {tournament &&
            tournament.matches.some((m) => m.isCompleted) &&
            teams.length > 0 &&
            !showForm &&
            !editing && (
              <Button
                onClick={async () => {
                  setLoading(true);
                  try {
                    // Sync tournament results to match results
                    const success = await syncTournamentResultsToMatchResults(tournament);
                    if (success) {
                      // Reload results
                      const updatedResults = await getMatchResultsForSession(gameSessionId);
                      setResults(updatedResults);

                      toast({
                        title: "Turnierergebnisse importiert",
                        description:
                          "Die Ergebnisse aus dem Turniermodus wurden erfolgreich übernommen.",
                      });
                    }
                  } catch (err) {
                    console.error("[MatchResults] Error importing tournament results:", err);
                    setError("Fehler beim Importieren der Turnierergebnisse");
                  } finally {
                    setLoading(false);
                  }
                }}
                className="flex items-center"
                size="sm"
                variant="outline"
              >
                <Download className="mr-1 h-4 w-4" />
                Aus Turnier importieren
              </Button>
            )}

          {/* Add result button */}
          {teams.length > 0 && !showForm && !editing && (
            <Button
              onClick={() => {
                setShowForm(true);
                setEditing(null);
              }}
              className="flex items-center"
              size="sm"
            >
              <Plus className="mr-1 h-4 w-4" />
              Ergebnis hinzufügen
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {results.length === 0 && !error && !showForm && (
        <Alert className="mb-4">
          <AlertDescription>
            Trage bitte die Ergebnisse des Spiels ein. Klicke auf "Ergebnis hinzufügen", um ein
            Spielergebnis zu erfassen.
          </AlertDescription>
        </Alert>
      )}

      {/* Formular */}
      {showForm && (
        <SimpleMatchResultForm
          teams={teams}
          onSubmit={handleAdd}
          onCancel={() => setShowForm(false)}
        />
      )}

      {editing && (
        <SimpleMatchResultForm
          teams={teams}
          initialData={editing}
          onSubmit={(data) => handleEdit(editing.id, data)}
          onCancel={() => setEditing(null)}
        />
      )}

      {/* Loading-Indicator */}
      {loading && (
        <div className="flex justify-center items-center py-6">
          <LoadingSpinner size="md" />
          <span className="ml-2 text-muted-foreground">Wird geladen...</span>
        </div>
      )}

      {/* Ergebnisliste */}
      {!loading && results.length > 0 && (
        <div className="space-y-3">
          {sortedResults.map((result, index) => (
            <Card key={result.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                  <div className="flex flex-col sm:flex-row w-full items-center gap-4">
                    {/* Team A */}
                    <div className="w-full sm:w-2/5 text-center sm:text-right">
                      <span className="font-medium">{getTeamName(result.team_a_id)}</span>
                    </div>

                    {/* Score */}
                    <div className="flex items-center justify-center w-full sm:w-1/5">
                      <Badge
                        variant={result.goals_a > result.goals_b ? "default" : "outline"}
                        className="text-lg px-2"
                      >
                        {result.goals_a}
                      </Badge>
                      <span className="mx-2 font-bold">:</span>
                      <Badge
                        variant={result.goals_b > result.goals_a ? "default" : "outline"}
                        className="text-lg px-2"
                      >
                        {result.goals_b}
                      </Badge>
                    </div>

                    {/* Team B */}
                    <div className="w-full sm:w-2/5 text-center sm:text-left">
                      <span className="font-medium">{getTeamName(result.team_b_id)}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 mt-2 sm:mt-0">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditing(result)}
                      className="h-8 w-8 p-0"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(result.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Additional Info (Optional) */}
                <div className="flex flex-wrap gap-2 mt-3 text-xs justify-center sm:justify-start">
                  {result.match_order && (
                    <Badge variant="outline" className="font-normal">
                      Spiel #{result.match_order}
                    </Badge>
                  )}
                  {result.field && (
                    <Badge variant="outline" className="font-normal">
                      Feld: {result.field}
                    </Badge>
                  )}
                  {result.time_slot && (
                    <Badge variant="outline" className="font-normal">
                      Zeit:{" "}
                      {new Date(result.time_slot).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
