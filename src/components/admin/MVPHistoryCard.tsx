import { useState } from "react";
import { Calendar, Trophy } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { useMVPVotingAdmin } from "@/hooks/useMVPVotingAdmin";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card } from "@/components/ui/card";

export function MVPHistoryCard() {
  const { votingPeriods, voteResults, isLoading } = useMVPVotingAdmin();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const closedVotingPeriods = votingPeriods.filter((period) => !period.is_open);
  const totalPages = Math.ceil(closedVotingPeriods.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = closedVotingPeriods.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <Pagination className="mt-6">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>

          {Array.from({ length: totalPages }).map((_, index) => {
            const pageNumber = index + 1;
            if (
              pageNumber === 1 ||
              pageNumber === totalPages ||
              (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
            ) {
              return (
                <PaginationItem key={pageNumber}>
                  <PaginationLink
                    isActive={pageNumber === currentPage}
                    onClick={() => handlePageChange(pageNumber)}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            }
            return null;
          })}

          <PaginationItem>
            <PaginationNext
              onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (closedVotingPeriods.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Trophy className="h-12 w-12 text-blue-500/30" />
        <p className="mt-4 text-center text-muted-foreground">
          Es gibt keine vergangenen MVP-Abstimmungen
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {currentItems.map((period) => (
        <Card key={period.id} className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-500 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400"
              >
                <Calendar className="h-4 w-4 mr-1" />
                {period.game_sessions.date
                  ? format(new Date(period.game_sessions.date), "dd.MM.yyyy", { locale: de })
                  : "Unbekannt"}
              </Badge>
            </div>
          </div>

          {period.mvp_winners && period.mvp_winners.length > 0 ? (
            <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 border border-blue-100 dark:border-blue-800/30">
              <div className="flex items-center gap-3 mb-2">
                <Trophy className="h-5 w-5 text-blue-500" />
                <Badge
                  variant="secondary"
                  className="bg-blue-100 text-blue-500 dark:bg-blue-900/30 dark:text-blue-400"
                >
                  {period.mvp_winners.length > 1 ? "MVPs" : "MVP"}
                </Badge>
              </div>

              <div className="space-y-2 pl-8">
                {period.mvp_winners.map((winner, index) => (
                  <p
                    key={winner.player_id}
                    className="text-sm text-blue-900 dark:text-blue-100 flex items-center justify-between"
                  >
                    <span>{winner.player_name}</span>
                    <span className="text-xs text-blue-600/70 dark:text-blue-300/70">
                      {winner.vote_count} Stimmen
                    </span>
                  </p>
                ))}
              </div>
            </div>
          ) : (
            <div className="rounded-lg bg-blue-50/50 dark:bg-blue-900/10 p-4">
              <div className="flex items-center gap-3">
                <Trophy className="h-5 w-5 text-blue-300 dark:text-blue-700" />
                <p className="text-sm text-blue-600/70 dark:text-blue-300/70">
                  Kein MVP festgelegt
                </p>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {voteResults[period.id]?.map((result) => (
              <div key={result.player_id} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium flex items-center gap-2">
                    {period.mvp_winners &&
                      period.mvp_winners.some(
                        (winner) => winner.player_id === result.player_id
                      ) && <Trophy className="h-3 w-3 text-blue-500" />}
                    {result.player_name}
                  </span>
                  <span className="text-muted-foreground text-xs">{result.vote_count} Stimmen</span>
                </div>
                <Progress
                  value={Number(result.vote_percentage)}
                  className="h-1.5 bg-blue-100 dark:bg-blue-900/20"
                  indicatorClassName="bg-blue-500 dark:bg-blue-400"
                />
              </div>
            ))}
          </div>

          {period !== currentItems[currentItems.length - 1] && <Separator className="mt-6" />}
        </Card>
      ))}
      {renderPagination()}
    </div>
  );
}
