import { usePlayerProfile } from "@/context/PlayerProfileContext";
import { Player } from "@/types";
import { usePlayers } from "@/hooks/usePlayers";

interface PlayerNameProps {
  playerId?: string;
  playerName: string;
  className?: string;
  jerseyNumber?: number | null;
  // If full player object is already available
  player?: Player;
}

export const PlayerName = ({
  playerId,
  playerName,
  className = "",
  jerseyNumber,
  player,
}: PlayerNameProps) => {
  const { openProfile } = usePlayerProfile();
  const { players } = usePlayers();

  const handleClick = () => {
    // If we have a full player object, use it directly
    if (player) {
      openProfile(player);
      return;
    }

    // Otherwise, try to find the player by ID or name
    let playerToOpen: Player | undefined;

    if (playerId) {
      playerToOpen = players.find((p) => p.id === playerId);
    }

    // Fallback to name match if ID isn't found or provided
    if (!playerToOpen) {
      playerToOpen = players.find((p) => p.name === playerName);
    }

    // If we found a player object, open the profile
    if (playerToOpen) {
      openProfile(playerToOpen);
    } else {
      // Create a minimal player object if we can't find the full record
      openProfile({
        name: playerName,
        jerseyNumber: jerseyNumber || undefined,
        status: "in",
        rating: 0,
      });
    }
  };

  return (
    <span
      onClick={handleClick}
      className={`cursor-pointer hover:underline ${className}`}
      title={`Klicken um Spielerprofil von ${playerName} anzuzeigen`}
    >
      {playerName}
    </span>
  );
};
