import { useEffect, useRef } from "react";
import { usePlayerProfile } from "@/context/PlayerProfileContext";
import { usePlayerStats } from "@/hooks/usePlayerStats";
import {
  X,
  Users,
  Award,
  Clock,
  Calendar,
  ArrowRight,
  Trophy,
  Star,
  Hash,
  Percent,
  Medal,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlayerName } from "@/components/player-profile/PlayerName";
import LoadingSpinner from "@/components/LoadingSpinner";
import { formatDate } from "@/utils/dateUtils";
import { useAuth } from "@/hooks/useAuth";
import { cn } from "@/lib/utils";

const StatBox = ({
  icon,
  label,
  value,
  color = "blue",
}: {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  color?: "blue" | "green" | "amber" | "purple";
}) => {
  const colorClasses = {
    blue: "bg-blue-50 dark:bg-blue-950/30 text-blue-600 dark:text-blue-400",
    green: "bg-green-50 dark:bg-green-950/30 text-green-600 dark:text-green-400",
    amber: "bg-amber-50 dark:bg-amber-950/30 text-amber-600 dark:text-amber-400",
    purple: "bg-purple-50 dark:bg-purple-950/30 text-purple-600 dark:text-purple-400",
  };

  return (
    <div className="flex flex-col items-center p-4 rounded-xl bg-white dark:bg-zinc-800/50 border border-gray-100 dark:border-zinc-700/50 hover:border-gray-200 dark:hover:border-zinc-700 transition-colors">
      <div className={cn("p-3 rounded-full mb-2", colorClasses[color])}>{icon}</div>
      <p className="text-2xl font-bold dark:text-white mb-1">{value}</p>
      <p className="text-sm text-gray-500 dark:text-gray-400">{label}</p>
    </div>
  );
};

export const PlayerProfileModal = () => {
  const { isOpen, selectedPlayer, closeProfile } = usePlayerProfile();
  const { stats, loading } = usePlayerStats(selectedPlayer);
  const { isAdmin } = useAuth();
  const modalContentRef = useRef<HTMLDivElement>(null);

  const navigateToLastGame = () => {
    if (stats.lastGameId) {
      closeProfile();
      window.location.href = `/history?gameId=${stats.lastGameId}`;
    }
  };

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (isOpen && event.key === "Escape") {
        closeProfile();
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => document.removeEventListener("keydown", handleEscapeKey);
  }, [isOpen, closeProfile]);

  const handleOutsideClick = (event: React.MouseEvent) => {
    if (modalContentRef.current && !modalContentRef.current.contains(event.target as Node)) {
      closeProfile();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black/60 p-0 sm:p-4 animate-in fade-in backdrop-blur-sm touch-none"
      onClick={handleOutsideClick}
    >
      <div
        ref={modalContentRef}
        className="relative bg-gray-50 dark:bg-zinc-900 rounded-t-2xl sm:rounded-2xl shadow-2xl w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] flex flex-col overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-br from-zinc-700 to-zinc-800 rounded-t-2xl">
          {/* Top bar with close button */}
          <div className="h-14 flex justify-end px-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                closeProfile();
              }}
              className="my-3 rounded-full h-8 w-8 bg-white/10 hover:bg-white/20 flex items-center justify-center"
            >
              <X className="h-4 w-4 text-white" />
            </button>
          </div>

          {/* Player info */}
          <div className="px-6 pb-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 border-2 border-white/20 bg-white/10">
                <AvatarFallback className="text-2xl font-bold text-zinc-800 bg-white/90">
                  {selectedPlayer?.jerseyNumber
                    ? "#" + selectedPlayer.jerseyNumber
                    : selectedPlayer?.name?.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>

              <div className="min-w-0">
                <h2 className="text-2xl font-bold text-white truncate">{selectedPlayer?.name}</h2>
                <div className="flex gap-2 mt-2">
                  <Badge className="bg-white/10 border-white/20">
                    #{selectedPlayer?.jerseyNumber}
                  </Badge>
                  {stats.mvpCount > 0 && (
                    <Badge className="bg-amber-500/20 border-amber-400/30">
                      <Trophy className="h-3.5 w-3.5 mr-1" /> {stats.mvpCount} MVP
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-6 py-6 overflow-y-auto">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <LoadingSpinner size="lg" />
              <p className="text-gray-500 dark:text-gray-400 mt-4">Lade Spielerstatistiken...</p>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Stats Cards */}
              <div
                className={cn(
                  "grid gap-4",
                  isAdmin ? "grid-cols-1 sm:grid-cols-4" : "grid-cols-1 sm:grid-cols-3"
                )}
              >
                <div className="rounded-xl border border-gray-100 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="p-3 rounded-full bg-blue-50 dark:bg-blue-900/20 mb-4">
                      <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                      {stats.totalGames}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Spiele gesamt</div>
                  </div>
                </div>

                <div className="rounded-xl border border-gray-100 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="p-3 rounded-full bg-green-50 dark:bg-green-900/20 mb-4">
                      <Percent className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                      {stats.participationPercentage}%
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Teilnahme</div>
                  </div>
                </div>

                <div className="rounded-xl border border-gray-100 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="p-3 rounded-full bg-amber-50 dark:bg-amber-900/20 mb-4">
                      <Trophy className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                      {stats.mvpCount}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">MVP</div>
                  </div>
                </div>

                {isAdmin && (
                  <div className="rounded-xl border border-gray-100 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className="p-3 rounded-full bg-purple-50 dark:bg-purple-900/20 mb-4">
                        <Star className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                      </div>
                      <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
                        {stats.averageRating.toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Rating</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Last Game Card */}
              <Card className="overflow-hidden border-gray-100 dark:border-zinc-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="flex items-center gap-4">
                      <div className="p-3 rounded-full bg-blue-50 dark:bg-blue-950/30">
                        <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold dark:text-white mb-1">
                          Letztes Spiel
                        </h3>
                        {stats.lastGameDate ? (
                          <p className="text-gray-600 dark:text-gray-300">
                            {formatDate(stats.lastGameDate)}
                          </p>
                        ) : (
                          <p className="text-gray-500 dark:text-gray-400">Noch kein Spiel</p>
                        )}
                      </div>
                    </div>
                    {stats.lastGameDate && (
                      <Button variant="outline" onClick={navigateToLastGame} className="gap-2">
                        Details
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Co-Players Section */}
              {stats.coPlayers && stats.coPlayers.length > 0 && (
                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold dark:text-white">
                    Top Mitspieler
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                    {stats.coPlayers.map((coPlayer, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 rounded-xl bg-white dark:bg-zinc-800/50 border border-gray-100 dark:border-zinc-700/50"
                      >
                        <Avatar className="h-10 sm:h-12 w-10 sm:w-12 border-2 border-gray-100 dark:border-zinc-700">
                          <AvatarFallback className="bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-200">
                            {coPlayer.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <PlayerName
                            playerId={coPlayer.id}
                            playerName={coPlayer.name}
                            className="font-medium dark:text-white hover:text-blue-600 dark:hover:text-blue-400 truncate"
                          />
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {coPlayer.gamesCount} {coPlayer.gamesCount === 1 ? "Spiel" : "Spiele"}{" "}
                            zusammen
                          </p>
                        </div>
                        <Badge
                          variant="outline"
                          className="bg-gray-50 dark:bg-zinc-800 flex-shrink-0"
                        >
                          #{index + 1}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Positions Grid */}
              {Object.keys(stats.positions).length > 0 && (
                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold dark:text-white">Positionen</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4">
                    {Object.entries(stats.positions).map(([position, count], index) => (
                      <div
                        key={index}
                        className="p-3 sm:p-4 rounded-xl bg-white dark:bg-zinc-800/50 border border-gray-100 dark:border-zinc-700/50 text-center"
                      >
                        <p className="text-base sm:text-lg font-medium dark:text-white mb-1">
                          {position}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {count} {count === 1 ? "Spiel" : "Spiele"}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
