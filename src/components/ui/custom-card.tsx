import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Card as BaseCard,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>ardFooter,
  CardTitle as BaseCardTitle,
  CardDescription as BaseCardDescription,
  CardContent as BaseCardContent,
} from "@/components/ui/card";

const Card = React.forwardRef<
  React.ElementRef<typeof BaseCard>,
  React.ComponentPropsWithoutRef<typeof BaseCard>
>(({ className, ...props }, ref) => (
  <BaseCard
    ref={ref}
    className={cn("bg-white dark:bg-zinc-900 border-0 shadow-md overflow-hidden", className)}
    {...props}
  />
));
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  React.ElementRef<typeof BaseCardHeader>,
  React.ComponentPropsWithoutRef<typeof BaseCardHeader> & {
    gradient?: boolean;
    spacing?: "default" | "compact";
  }
>(({ className, gradient = true, spacing = "default", ...props }, ref) => (
  <BaseCardHeader
    ref={ref}
    className={cn(
      "px-3 py-3",
      spacing === "default" ? "space-y-3" : "space-y-1",
      gradient && "bg-gradient-to-b from-gray-50/80 to-transparent dark:from-zinc-800/20",
      className
    )}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  React.ElementRef<typeof BaseCardTitle>,
  React.ComponentPropsWithoutRef<typeof BaseCardTitle> & {
    icon?: React.ReactNode;
  }
>(({ className, children, icon, ...props }, ref) => (
  <BaseCardTitle
    ref={ref}
    className={cn(
      "text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",
      className
    )}
    {...props}
  >
    {icon && <span className="text-gray-600 dark:text-gray-400">{icon}</span>}
    {children}
  </BaseCardTitle>
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  React.ElementRef<typeof BaseCardDescription>,
  React.ComponentPropsWithoutRef<typeof BaseCardDescription>
>(({ className, ...props }, ref) => (
  <BaseCardDescription
    ref={ref}
    className={cn("text-gray-600 dark:text-gray-400", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  React.ElementRef<typeof BaseCardContent>,
  React.ComponentPropsWithoutRef<typeof BaseCardContent>
>(({ className, ...props }, ref) => (
  <BaseCardContent ref={ref} className={cn("px-3 py-4", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  React.ElementRef<typeof BaseCardFooter>,
  React.ComponentPropsWithoutRef<typeof BaseCardFooter>
>(({ className, ...props }, ref) => (
  <BaseCardFooter ref={ref} className={cn("px-3 py-4 pt-0", className)} {...props} />
));
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
