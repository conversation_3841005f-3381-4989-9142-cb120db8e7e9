import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const CustomPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: CustomPaginationProps) => {
  if (totalPages <= 1) return null;

  return (
    <Pagination className="mt-6">
      <PaginationContent className="flex-wrap gap-1">
        <PaginationItem>
          <PaginationPrevious
            onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
            className={cn(
              currentPage === 1 ? "pointer-events-none opacity-50" : "",
              "sm:flex hidden" // Hide text on small screens
            )}
          />
          {/* Mobile version - icon only */}
          <PaginationLink
            onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
            className={cn(
              currentPage === 1 ? "pointer-events-none opacity-50" : "",
              "sm:hidden flex h-9 w-9 p-0 items-center justify-center"
            )}
            aria-label="Vorherige Seite"
          >
            <ChevronLeft className="h-4 w-4" />
          </PaginationLink>
        </PaginationItem>

        {/* Generate smart pagination with ellipsis */}
        {(() => {
          // Logic to determine which page numbers to show
          const visiblePageCount = window.innerWidth < 640 ? 3 : 5; // Show fewer pages on mobile
          let startPage = Math.max(1, currentPage - Math.floor(visiblePageCount / 2));
          let endPage = Math.min(totalPages, startPage + visiblePageCount - 1);

          // Adjust if we're at the end
          if (endPage - startPage + 1 < visiblePageCount) {
            startPage = Math.max(1, endPage - visiblePageCount + 1);
          }

          const pages = [];

          // Add first page if not included in the visible range
          if (startPage > 1) {
            pages.push(
              <PaginationItem key="first">
                <PaginationLink onClick={() => onPageChange(1)} className="h-9 w-9 p-0">
                  1
                </PaginationLink>
              </PaginationItem>
            );

            // Add ellipsis if there's a gap
            if (startPage > 2) {
              pages.push(
                <PaginationItem key="ellipsis-start" className="hidden sm:flex">
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }
          }

          // Add visible page numbers
          for (let i = startPage; i <= endPage; i++) {
            pages.push(
              <PaginationItem key={i}>
                <PaginationLink
                  isActive={currentPage === i}
                  onClick={() => onPageChange(i)}
                  className="h-9 w-9 p-0"
                >
                  {i}
                </PaginationLink>
              </PaginationItem>
            );
          }

          // Add last page if not included in the visible range
          if (endPage < totalPages) {
            // Add ellipsis if there's a gap
            if (endPage < totalPages - 1) {
              pages.push(
                <PaginationItem key="ellipsis-end" className="hidden sm:flex">
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            pages.push(
              <PaginationItem key="last">
                <PaginationLink onClick={() => onPageChange(totalPages)} className="h-9 w-9 p-0">
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            );
          }

          return pages;
        })()}

        <PaginationItem>
          <PaginationNext
            onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
            className={cn(
              currentPage === totalPages ? "pointer-events-none opacity-50" : "",
              "sm:flex hidden" // Hide text on small screens
            )}
          />
          {/* Mobile version - icon only */}
          <PaginationLink
            onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
            className={cn(
              currentPage === totalPages ? "pointer-events-none opacity-50" : "",
              "sm:hidden flex h-9 w-9 p-0 items-center justify-center"
            )}
            aria-label="Nächste Seite"
          >
            <ChevronRight className="h-4 w-4" />
          </PaginationLink>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};
