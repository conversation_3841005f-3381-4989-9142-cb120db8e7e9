import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import TeamDisplay from "../TeamDisplay";
import { useGameSessions } from "@/hooks/useGameSessions";
import { useTeams } from "@/hooks/useTeams";

// Mock the hooks
vi.mock("@/hooks/useGameSessions");
vi.mock("@/hooks/useTeams");
vi.mock("@/components/player-profile/PlayerName", () => ({
  PlayerName: ({ playerName, className }: { playerName: string; className: string }) => (
    <span className={className}>{playerName}</span>
  ),
}));

// Mock the LoadingSpinner component
vi.mock("@/components/LoadingSpinner", () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

describe("TeamDisplay", () => {
  const mockTeams = [
    {
      id: "1",
      name: "Team A",
      averageRating: 7.2,
      players: [
        { id: "p1", name: "Player 1", jerseyNumber: 10, rating: 7.5, status: "in" },
        { id: "p2", name: "Player 2", jerseyNumber: 11, rating: 6.9, status: "in" },
      ],
    },
    {
      id: "2",
      name: "Team B",
      averageRating: 7.4,
      players: [{ id: "p3", name: "Player 3", jerseyNumber: 12, rating: 7.4, status: "in" }],
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useGameSessions as any).mockReturnValue({
      currentSession: {
        id: "session1",
        isTeamGenerated: true,
      },
      loading: false,
    });
    (useTeams as any).mockReturnValue({ teams: [], loading: true });

    // Clear localStorage
    localStorage.clear();
  });

  it("should render loading state", () => {
    render(<TeamDisplay />);
    expect(screen.getByText("Teams")).toBeInTheDocument();
    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
  });

  it("should render teams with correct data for non-admin users", () => {
    (useTeams as any).mockReturnValue({ teams: mockTeams, loading: false });

    render(<TeamDisplay />);

    // Check team names
    expect(screen.getByText("Team A")).toBeInTheDocument();
    expect(screen.getByText("Team B")).toBeInTheDocument();

    // Check that average ratings are NOT displayed for non-admin users
    expect(screen.queryByText("Ø 7.2")).not.toBeInTheDocument();
    expect(screen.queryByText("Ø 7.4")).not.toBeInTheDocument();

    // Check player names and numbers
    expect(screen.getByText("Player 1")).toBeInTheDocument();
    expect(screen.getByText("#10")).toBeInTheDocument();
    expect(screen.getByText("Player 2")).toBeInTheDocument();
    expect(screen.getByText("#11")).toBeInTheDocument();
    expect(screen.getByText("Player 3")).toBeInTheDocument();
    expect(screen.getByText("#12")).toBeInTheDocument();
  });

  it("should render teams with average ratings for admin users", () => {
    (useTeams as any).mockReturnValue({ teams: mockTeams, loading: false });

    render(<TeamDisplay isAdminView={true} />);

    // Check team names
    expect(screen.getByText("Team A")).toBeInTheDocument();
    expect(screen.getByText("Team B")).toBeInTheDocument();

    // Check that average ratings ARE displayed for admin users
    expect(screen.getByText("Ø 7.2")).toBeInTheDocument();
    expect(screen.getByText("Ø 7.4")).toBeInTheDocument();

    // Check player names and numbers
    expect(screen.getByText("Player 1")).toBeInTheDocument();
    expect(screen.getByText("#10")).toBeInTheDocument();
    expect(screen.getByText("Player 2")).toBeInTheDocument();
    expect(screen.getByText("#11")).toBeInTheDocument();
    expect(screen.getByText("Player 3")).toBeInTheDocument();
    expect(screen.getByText("#12")).toBeInTheDocument();
  });

  it("should not render when no teams are available", () => {
    (useTeams as any).mockReturnValue({ teams: [], loading: false });
    (useGameSessions as any).mockReturnValue({
      currentSession: {
        id: "session1",
        isTeamGenerated: false,
      },
      loading: false,
    });

    const { container } = render(<TeamDisplay />);
    expect(container.firstChild).toBeNull();
  });

  it("should apply correct team gradient styles", () => {
    (useTeams as any).mockReturnValue({ teams: mockTeams, loading: false });

    render(<TeamDisplay />);

    // Look for team divs that should have gradient classes
    const teamDivs = screen
      .getAllByText(/^Team [A-Z]$/)
      .map((el) => el.closest('div[class*="bg-gradient-to-br"]'));

    expect(teamDivs[0]).toHaveClass("bg-gradient-to-br");
    expect(teamDivs[1]).toHaveClass("bg-gradient-to-br");
  });

  it("should still render average rating badge for teams with undefined ratings (admin view)", () => {
    // Create a team with undefined averageRating
    const teamsWithoutRatings = [
      {
        id: "1",
        name: "Team A",
        averageRating: undefined,
        players: [{ id: "p1", name: "Player 1", jerseyNumber: 10, status: "in" }],
      },
    ];

    (useTeams as any).mockReturnValue({ teams: teamsWithoutRatings, loading: false });

    render(<TeamDisplay isAdminView={true} />);

    expect(screen.getByText("Team A")).toBeInTheDocument();

    // Find the badge that contains the Ø symbol
    const badge = screen.getByText("Ø").closest("div");
    expect(badge).toBeInTheDocument();

    // The badge content should be rendered even with undefined value
    expect(badge).toHaveTextContent("Ø");
  });

  it("should not render average rating badge for non-admin users", () => {
    // Create a team with averageRating
    const teamsWithRatings = [
      {
        id: "1",
        name: "Team A",
        averageRating: 7.5,
        players: [{ id: "p1", name: "Player 1", jerseyNumber: 10, status: "in" }],
      },
    ];

    (useTeams as any).mockReturnValue({ teams: teamsWithRatings, loading: false });

    render(<TeamDisplay />);

    expect(screen.getByText("Team A")).toBeInTheDocument();

    // The badge should not be rendered for non-admin users
    expect(screen.queryByText("Ø 7.5")).not.toBeInTheDocument();
    expect(screen.queryByText(/Ø/)).not.toBeInTheDocument();
  });

  it("should handle players without jersey numbers", () => {
    const teamsWithPlayersWithoutNumbers = [
      {
        id: "1",
        name: "Team A",
        averageRating: 7.2,
        players: [{ id: "p1", name: "Player 1", status: "in" }],
      },
    ];

    (useTeams as any).mockReturnValue({ teams: teamsWithPlayersWithoutNumbers, loading: false });

    render(<TeamDisplay />);

    expect(screen.getByText("Player 1")).toBeInTheDocument();
    // Since we're not adding jerseyNumber to the player, the # indicator shouldn't be present
    expect(screen.queryByText(/#/)).not.toBeInTheDocument();
  });

  it("should render correctly on mobile and desktop", () => {
    (useTeams as any).mockReturnValue({ teams: mockTeams, loading: false });

    const { container } = render(<TeamDisplay />);

    // Find the grid container that should have responsive classes
    // Use a more direct approach to find the grid element
    const gridContainer = container.querySelector(".grid.grid-cols-1.md\\:grid-cols-2");
    expect(gridContainer).not.toBeNull();
    expect(gridContainer).toHaveClass("grid-cols-1");
    expect(gridContainer).toHaveClass("md:grid-cols-2");
  });

  it("should highlight user team when user is in a team", () => {
    localStorage.setItem("currentUserId", "p1");

    (useTeams as any).mockReturnValue({ teams: mockTeams, loading: false });

    render(<TeamDisplay />);

    // Check for the "Dein Team" text that indicates this is the user's team
    expect(screen.getByText("Dein Team")).toBeInTheDocument();
  });
});
