export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  public: {
    Tables: {
      admin_users: {
        Row: {
          created_at: string;
          id: string;
          password_hash: string;
          username: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          password_hash: string;
          username: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          password_hash?: string;
          username?: string;
        };
        Relationships: [];
      };
      app_settings: {
        Row: {
          created_at: string;
          id: string;
          key: string;
          updated_at: string;
          value: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          key: string;
          updated_at?: string;
          value: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          key?: string;
          updated_at?: string;
          value?: string;
        };
        Relationships: [];
      };
      feedback: {
        Row: {
          content: string;
          created_at: string;
          game_session_id: string | null;
          id: string;
          improvements: string | null;
          is_game_feedback: boolean;
          notes: string | null;
          positives: string | null;
          rating: number | null;
          submitted_by: string | null;
        };
        Insert: {
          content: string;
          created_at?: string;
          game_session_id?: string | null;
          id?: string;
          improvements?: string | null;
          is_game_feedback?: boolean;
          notes?: string | null;
          positives?: string | null;
          rating?: number | null;
          submitted_by?: string | null;
        };
        Update: {
          content?: string;
          created_at?: string;
          game_session_id?: string | null;
          id?: string;
          improvements?: string | null;
          is_game_feedback?: boolean;
          notes?: string | null;
          positives?: string | null;
          rating?: number | null;
          submitted_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "feedback_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
        ];
      };
      game_sessions: {
        Row: {
          created_at: string;
          date: string;
          duration_minutes: number;
          id: string;
          is_signup_open: boolean | null;
          is_team_generated: boolean | null;
          signup_opens_at: string;
          status: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          date: string;
          duration_minutes?: number;
          id?: string;
          is_signup_open?: boolean | null;
          is_team_generated?: boolean | null;
          signup_opens_at: string;
          status?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          date?: string;
          duration_minutes?: number;
          id?: string;
          is_signup_open?: boolean | null;
          is_team_generated?: boolean | null;
          signup_opens_at?: string;
          status?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      match_results: {
        Row: {
          created_at: string;
          field: string | null;
          game_session_id: string;
          goals_a: number;
          goals_b: number;
          id: string;
          match_order: number | null;
          team_a_id: string;
          team_b_id: string;
          time_slot: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          field?: string | null;
          game_session_id: string;
          goals_a: number;
          goals_b: number;
          id?: string;
          match_order?: number | null;
          team_a_id: string;
          team_b_id: string;
          time_slot?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          field?: string | null;
          game_session_id?: string;
          goals_a?: number;
          goals_b?: number;
          id?: string;
          match_order?: number | null;
          team_a_id?: string;
          team_b_id?: string;
          time_slot?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "match_results_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "match_results_team_a_id_fkey";
            columns: ["team_a_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "match_results_team_b_id_fkey";
            columns: ["team_b_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
        ];
      };
      mvp_votes: {
        Row: {
          created_at: string;
          id: string;
          player_id: string;
          voter_device_id: string;
          voting_period_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          player_id: string;
          voter_device_id: string;
          voting_period_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          player_id?: string;
          voter_device_id?: string;
          voting_period_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "mvp_votes_player_id_fkey";
            columns: ["player_id"];
            isOneToOne: false;
            referencedRelation: "players";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "mvp_votes_voting_period_id_fkey";
            columns: ["voting_period_id"];
            isOneToOne: false;
            referencedRelation: "mvp_voting_periods";
            referencedColumns: ["id"];
          },
        ];
      };
      mvp_voting_periods: {
        Row: {
          created_at: string;
          ends_at: string;
          game_session_id: string;
          id: string;
          is_open: boolean;
        };
        Insert: {
          created_at?: string;
          ends_at?: string;
          game_session_id: string;
          id?: string;
          is_open?: boolean;
        };
        Update: {
          created_at?: string;
          ends_at?: string;
          game_session_id?: string;
          id?: string;
          is_open?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "mvp_voting_periods_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: true;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
        ];
      };
      mvp_winners: {
        Row: {
          created_at: string;
          game_session_id: string;
          id: string;
          player_id: string;
          vote_count: number;
          voting_period_id: string;
        };
        Insert: {
          created_at?: string;
          game_session_id: string;
          id?: string;
          player_id: string;
          vote_count: number;
          voting_period_id: string;
        };
        Update: {
          created_at?: string;
          game_session_id?: string;
          id?: string;
          player_id?: string;
          vote_count?: number;
          voting_period_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "mvp_winners_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "mvp_winners_player_id_fkey";
            columns: ["player_id"];
            isOneToOne: false;
            referencedRelation: "players";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "mvp_winners_voting_period_id_fkey";
            columns: ["voting_period_id"];
            isOneToOne: false;
            referencedRelation: "mvp_voting_periods";
            referencedColumns: ["id"];
          },
        ];
      };
      player_signups: {
        Row: {
          game_session_id: string;
          id: string;
          player_id: string;
          signup_time: string;
          status: string;
        };
        Insert: {
          game_session_id: string;
          id?: string;
          player_id: string;
          signup_time?: string;
          status: string;
        };
        Update: {
          game_session_id?: string;
          id?: string;
          player_id?: string;
          signup_time?: string;
          status?: string;
        };
        Relationships: [
          {
            foreignKeyName: "player_signups_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "player_signups_player_id_fkey";
            columns: ["player_id"];
            isOneToOne: false;
            referencedRelation: "players";
            referencedColumns: ["id"];
          },
        ];
      };
      players: {
        Row: {
          created_at: string;
          id: string;
          is_active: boolean;
          jersey_number: number | null;
          name: string;
          rating: number | null;
          role: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          is_active?: boolean;
          jersey_number?: number | null;
          name: string;
          rating?: number | null;
          role?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          is_active?: boolean;
          jersey_number?: number | null;
          name?: string;
          rating?: number | null;
          role?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      team_players: {
        Row: {
          game_session_id: string;
          id: string;
          player_id: string;
          team_id: string;
        };
        Insert: {
          game_session_id: string;
          id?: string;
          player_id: string;
          team_id: string;
        };
        Update: {
          game_session_id?: string;
          id?: string;
          player_id?: string;
          team_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "team_players_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "team_players_player_id_fkey";
            columns: ["player_id"];
            isOneToOne: false;
            referencedRelation: "players";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "team_players_team_id_fkey";
            columns: ["team_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
        ];
      };
      teams: {
        Row: {
          average_rating: number | null;
          created_at: string;
          game_session_id: string;
          id: string;
          name: string;
        };
        Insert: {
          average_rating?: number | null;
          created_at?: string;
          game_session_id: string;
          id?: string;
          name: string;
        };
        Update: {
          average_rating?: number | null;
          created_at?: string;
          game_session_id?: string;
          id?: string;
          name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "teams_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
        ];
      };
      tournament_matches: {
        Row: {
          created_at: string;
          end_time: string | null;
          field: string | null;
          goals_a: number | null;
          goals_b: number | null;
          id: string;
          is_completed: boolean;
          match_number: number;
          start_time: string | null;
          team_a_id: string;
          team_b_id: string;
          tournament_id: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          end_time?: string | null;
          field?: string | null;
          goals_a?: number | null;
          goals_b?: number | null;
          id?: string;
          is_completed?: boolean;
          match_number: number;
          start_time?: string | null;
          team_a_id: string;
          team_b_id: string;
          tournament_id: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          end_time?: string | null;
          field?: string | null;
          goals_a?: number | null;
          goals_b?: number | null;
          id?: string;
          is_completed?: boolean;
          match_number?: number;
          start_time?: string | null;
          team_a_id?: string;
          team_b_id?: string;
          tournament_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tournament_matches_team_a_id_fkey";
            columns: ["team_a_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tournament_matches_team_b_id_fkey";
            columns: ["team_b_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tournament_matches_tournament_id_fkey";
            columns: ["tournament_id"];
            isOneToOne: false;
            referencedRelation: "tournaments";
            referencedColumns: ["id"];
          },
        ];
      };
      tournament_standings: {
        Row: {
          created_at: string;
          drawn: number;
          goal_difference: number;
          goals_against: number;
          goals_for: number;
          id: string;
          lost: number;
          played: number;
          points: number;
          team_id: string;
          tournament_id: string;
          updated_at: string;
          won: number;
        };
        Insert: {
          created_at?: string;
          drawn?: number;
          goal_difference?: number;
          goals_against?: number;
          goals_for?: number;
          id?: string;
          lost?: number;
          played?: number;
          points?: number;
          team_id: string;
          tournament_id: string;
          updated_at?: string;
          won?: number;
        };
        Update: {
          created_at?: string;
          drawn?: number;
          goal_difference?: number;
          goals_against?: number;
          goals_for?: number;
          id?: string;
          lost?: number;
          played?: number;
          points?: number;
          team_id?: string;
          tournament_id?: string;
          updated_at?: string;
          won?: number;
        };
        Relationships: [
          {
            foreignKeyName: "tournament_standings_team_id_fkey";
            columns: ["team_id"];
            isOneToOne: false;
            referencedRelation: "teams";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tournament_standings_tournament_id_fkey";
            columns: ["tournament_id"];
            isOneToOne: false;
            referencedRelation: "tournaments";
            referencedColumns: ["id"];
          },
        ];
      };
      tournaments: {
        Row: {
          break_between_matches_minutes: number;
          created_at: string;
          end_time: string | null;
          game_session_id: string;
          id: string;
          is_active: boolean;
          match_duration_minutes: number;
          number_of_fields: number;
          number_of_teams: number;
          start_time: string | null;
          total_duration_minutes: number;
          updated_at: string;
        };
        Insert: {
          break_between_matches_minutes: number;
          created_at?: string;
          end_time?: string | null;
          game_session_id: string;
          id?: string;
          is_active?: boolean;
          match_duration_minutes: number;
          number_of_fields: number;
          number_of_teams: number;
          start_time?: string | null;
          total_duration_minutes: number;
          updated_at?: string;
        };
        Update: {
          break_between_matches_minutes?: number;
          created_at?: string;
          end_time?: string | null;
          game_session_id?: string;
          id?: string;
          is_active?: boolean;
          match_duration_minutes?: number;
          number_of_fields?: number;
          number_of_teams?: number;
          start_time?: string | null;
          total_duration_minutes?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "tournaments_game_session_id_fkey";
            columns: ["game_session_id"];
            isOneToOne: false;
            referencedRelation: "game_sessions";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      mvp_vote_results: {
        Row: {
          player_id: string | null;
          player_name: string | null;
          vote_count: number | null;
          vote_percentage: number | null;
          voting_period_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "mvp_votes_player_id_fkey";
            columns: ["player_id"];
            isOneToOne: false;
            referencedRelation: "players";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "mvp_votes_voting_period_id_fkey";
            columns: ["voting_period_id"];
            isOneToOne: false;
            referencedRelation: "mvp_voting_periods";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Functions: {
      delete_push_subscription: {
        Args: { endpoint_param: string };
        Returns: undefined;
      };
      insert_push_subscription: {
        Args: {
          endpoint_param: string;
          keys_param: string;
          created_at_param: string;
        };
        Returns: undefined;
      };
      set_claim: {
        Args: { name: string; value: string };
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] & DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"] | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
