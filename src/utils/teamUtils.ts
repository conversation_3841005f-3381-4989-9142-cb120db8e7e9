import { Player, Team, PlayerRole } from "@/types";

/**
 * Distributes players into balanced teams based on player ratings and roles
 * Uses a simplified approach focusing on equal team sizes and key positions
 *
 * @param players Array of players to distribute
 * @param teamCount Number of teams to create
 * @returns Array of teams with assigned players
 */
export function generateTeams(players: Player[], teamCount: number): Team[] {
  console.log(`Starting team generation with ${players.length} players and ${teamCount} teams`);

  // Basic validation
  if (players.length === 0 || teamCount === 0) {
    console.log("No players or teams provided, returning empty array");
    return [];
  }

  try {
    // Determine optimal team count and player count based on the available players
    let optimalTeamCount = teamCount;
    let playerCount = players.length;
    let playersToUse = [...players];

    // Always use only multiples of 5 players: 10, 15, or 20
    if (playerCount > 10 && playerCount < 15) {
      console.log(
        `Adjusting player count from ${playerCount} to 10 (reserves will not be included in teams)`
      );
      // Sort by timestamp to use the first 10 players
      playersToUse = players
        .sort((a, b) => {
          if (a.signupTimestamp && b.signupTimestamp) {
            return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
          }
          return 0;
        })
        .slice(0, 10);
      playerCount = 10;
      optimalTeamCount = 2;
    } else if (playerCount > 15 && playerCount < 20) {
      console.log(
        `Adjusting player count from ${playerCount} to 15 (reserves will not be included in teams)`
      );
      // Sort by timestamp to use the first 15 players
      playersToUse = players
        .sort((a, b) => {
          if (a.signupTimestamp && b.signupTimestamp) {
            return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
          }
          return 0;
        })
        .slice(0, 15);
      playerCount = 15;
      optimalTeamCount = 3;
    } else if (playerCount > 20) {
      console.log(
        `Adjusting player count from ${playerCount} to 20 (reserves will not be included in teams)`
      );
      // Sort by timestamp to use the first 20 players
      playersToUse = players
        .sort((a, b) => {
          if (a.signupTimestamp && b.signupTimestamp) {
            return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
          }
          return 0;
        })
        .slice(0, 20);
      playerCount = 20;
      optimalTeamCount = 4;
    }

    console.log(`Player count: ${playerCount}, Initial team count: ${teamCount}`);

    // Apply the team allocation rules
    if (playerCount < 10) {
      console.log("Not enough players for a game (less than 10)");
      return [];
    } else if (playerCount === 10) {
      optimalTeamCount = 2; // 2 teams of 5
    } else if (playerCount === 15) {
      optimalTeamCount = 3; // 3 teams of 5
    } else if (playerCount === 20) {
      optimalTeamCount = 4; // 4 teams of 5
    }

    console.log(`Optimized to ${optimalTeamCount} teams with exactly ${playerCount} players`);

    // Initialize empty teams
    const teams: Team[] = Array.from({ length: optimalTeamCount }, (_, i) => ({
      id: `temp-${i}`,
      name: `Team ${i + 1}`,
      averageRating: 0,
      players: [],
    }));

    // Calculate target players per team - this should always be exactly 5
    const playersPerTeam = Math.floor(playerCount / optimalTeamCount);
    const extraPlayers = playerCount % optimalTeamCount;

    // Keep track of target sizes for each team
    const targetTeamSizes = teams.map((_, index) =>
      index < extraPlayers ? playersPerTeam + 1 : playersPerTeam
    );

    console.log(`Target team sizes: ${JSON.stringify(targetTeamSizes)}`);
    // Double-check that all target sizes are 5
    const allTeamsSize5 = targetTeamSizes.every((size) => size === 5);
    if (!allTeamsSize5) {
      console.error("Error: Not all teams have a target size of 5 players. Adjusting...");
      // Force all teams to have 5 players
      for (let i = 0; i < targetTeamSizes.length; i++) {
        targetTeamSizes[i] = 5;
      }
    }

    // Track remaining slots for each team
    const remainingSlots = [...targetTeamSizes];

    // Categorize players
    const goalkeepers = playersToUse
      .filter((p) => p.role === "goalkeeper")
      .sort((a, b) => b.rating - a.rating);
    const strikers = playersToUse
      .filter((p) => p.role === "striker")
      .sort((a, b) => b.rating - a.rating);
    const otherPlayers = playersToUse
      .filter((p) => p.role !== "goalkeeper" && p.role !== "striker")
      .sort((a, b) => b.rating - a.rating);

    console.log(
      `Found ${goalkeepers.length} goalkeepers, ${strikers.length} strikers, and ${otherPlayers.length} other players`
    );

    // STEP 1: Distribute goalkeepers (one per team)
    for (let i = 0; i < Math.min(teams.length, goalkeepers.length); i++) {
      teams[i].players.push(goalkeepers[i]);
      remainingSlots[i]--;
    }

    // Add any remaining goalkeepers to our pool of other players
    if (goalkeepers.length > teams.length) {
      otherPlayers.push(...goalkeepers.slice(teams.length));
    }

    // STEP 2: Distribute strikers (one per team where possible)
    let assignedStrikerCount = 0;
    for (let i = 0; i < teams.length && assignedStrikerCount < strikers.length; i++) {
      if (remainingSlots[i] > 0) {
        teams[i].players.push(strikers[assignedStrikerCount]);
        remainingSlots[i]--;
        assignedStrikerCount++;
      }
    }

    // Add any remaining strikers to our pool of other players
    if (assignedStrikerCount < strikers.length) {
      otherPlayers.push(...strikers.slice(assignedStrikerCount));
    }

    // Re-sort other players by rating for final distribution
    otherPlayers.sort((a, b) => b.rating - a.rating);

    // STEP 3: Distribute all remaining players to achieve balanced ratings
    console.log(`Distributing ${otherPlayers.length} remaining players to balance teams`);

    // First distribute players based on team ratings (lower-rated teams get higher-rated players)
    while (otherPlayers.length > 0 && remainingSlots.some((slots) => slots > 0)) {
      // Calculate current team ratings
      const teamRatings = teams.map((team) => {
        if (team.players.length === 0) return 0;
        return team.players.reduce((sum, player) => sum + player.rating, 0) / team.players.length;
      });

      // Find the team with the lowest rating that still has slots
      const teamsByRating = remainingSlots
        .map((slots, index) => ({ index, slots, rating: teamRatings[index] }))
        .filter((item) => item.slots > 0)
        .sort((a, b) => a.rating - b.rating);

      if (teamsByRating.length === 0) break;

      const lowestRatedTeamIndex = teamsByRating[0].index;

      // Add top-rated remaining player to lowest-rated team
      teams[lowestRatedTeamIndex].players.push(otherPlayers[0]);
      remainingSlots[lowestRatedTeamIndex]--;
      otherPlayers.shift();
    }

    // Check if we still have players to distribute (this is for safety)
    if (otherPlayers.length > 0) {
      console.log(
        `WARNING: Still have ${otherPlayers.length} players to distribute, will forcibly distribute to ensure equal team sizes`
      );

      // Force distribution to any teams that still need players
      while (
        otherPlayers.length > 0 &&
        teams.some((team, i) => team.players.length < targetTeamSizes[i])
      ) {
        for (let i = 0; i < teams.length && otherPlayers.length > 0; i++) {
          if (teams[i].players.length < targetTeamSizes[i]) {
            teams[i].players.push(otherPlayers.shift()!);
          }
        }
      }
    }

    // STEP 4: Verify all teams have the correct number of players
    teams.forEach((team, index) => {
      console.log(
        `Team ${index + 1} has ${team.players.length} players, target is ${targetTeamSizes[index]}`
      );

      if (team.players.length !== targetTeamSizes[index]) {
        console.error(
          `Team size mismatch! Team ${index + 1} has ${team.players.length} players but should have ${targetTeamSizes[index]}`
        );
      }
    });

    // Calculate final average ratings for each team
    teams.forEach((team) => {
      const totalRating = team.players.reduce((sum, player) => sum + player.rating, 0);
      team.averageRating = totalRating / team.players.length;
      console.log(`Team ${team.name} final average rating: ${team.averageRating.toFixed(2)}`);
    });

    // Perform final balancing through player swapping
    const balancedTeams = balanceTeamsBySwapping(teams);
    console.log("Team generation completed successfully");
    return balancedTeams;
  } catch (error) {
    console.error("Error in team generation:", error);
    return [];
  }
}

/**
 * Swaps players between teams to optimize balance
 *
 * @param teams Array of teams to balance
 * @param maxSwaps Maximum number of swaps to perform
 * @returns Balanced teams after swaps
 */
export function balanceTeamsBySwapping(teams: Team[], maxSwaps: number = 5): Team[] {
  if (teams.length < 2) return teams;

  try {
    const newTeams = JSON.parse(JSON.stringify(teams)) as Team[];
    let swapsPerformed = 0;

    // Get team average ratings
    const getTeamRatings = () =>
      newTeams.map((team) => {
        const totalRating = team.players.reduce((sum, player) => sum + player.rating, 0);
        return team.players.length > 0 ? totalRating / team.players.length : 0;
      });

    // Get rating variance (lower is better)
    const getRatingVariance = (ratings: number[]) => {
      const mean = ratings.reduce((sum, r) => sum + r, 0) / ratings.length;
      return ratings.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / ratings.length;
    };

    // Store original team sizes to maintain them
    const teamSizes = newTeams.map((team) => team.players.length);

    // Try to improve team balance through player swaps
    while (swapsPerformed < maxSwaps) {
      let bestSwap: {
        team1: number;
        player1: number;
        team2: number;
        player2: number;
        improvement: number;
      } | null = null;

      // Check all possible player swaps
      for (let t1 = 0; t1 < newTeams.length; t1++) {
        for (let t2 = t1 + 1; t2 < newTeams.length; t2++) {
          // Skip if teams have different sizes (we need to maintain size)
          if (
            newTeams[t1].players.length !== teamSizes[t1] ||
            newTeams[t2].players.length !== teamSizes[t2]
          ) {
            continue;
          }

          for (let p1 = 0; p1 < newTeams[t1].players.length; p1++) {
            for (let p2 = 0; p2 < newTeams[t2].players.length; p2++) {
              // Get player references
              const player1 = newTeams[t1].players[p1];
              const player2 = newTeams[t2].players[p2];

              // Don't swap goalkeepers
              if (player1.role === "goalkeeper" || player2.role === "goalkeeper") {
                continue;
              }

              // Prefer not to swap strikers unless necessary
              if (player1.role === "striker" && player2.role !== "striker") {
                continue;
              }
              if (player2.role === "striker" && player1.role !== "striker") {
                continue;
              }

              // Calculate current variance
              const currentRatings = getTeamRatings();
              const currentVariance = getRatingVariance(currentRatings);

              // Temporarily swap players
              newTeams[t1].players[p1] = player2;
              newTeams[t2].players[p2] = player1;

              // Recalculate variance after swap
              const newRatings = getTeamRatings();
              const newVariance = getRatingVariance(newRatings);

              // Undo swap
              newTeams[t1].players[p1] = player1;
              newTeams[t2].players[p2] = player2;

              // Check if this swap improves balance
              const improvement = currentVariance - newVariance;
              if (improvement > 0 && (!bestSwap || improvement > bestSwap.improvement)) {
                bestSwap = { team1: t1, player1: p1, team2: t2, player2: p2, improvement };
              }
            }
          }
        }
      }

      // If we found a good swap, apply it
      if (bestSwap && bestSwap.improvement > 0.1) {
        const { team1, player1, team2, player2 } = bestSwap;
        const temp = newTeams[team1].players[player1];
        newTeams[team1].players[player1] = newTeams[team2].players[player2];
        newTeams[team2].players[player2] = temp;
        swapsPerformed++;
        console.log(
          `Performed swap ${swapsPerformed}: improvement = ${bestSwap.improvement.toFixed(2)}`
        );
      } else {
        // No more beneficial swaps found
        break;
      }
    }

    // Recalculate final average ratings
    newTeams.forEach((team) => {
      const totalRating = team.players.reduce((sum, player) => sum + player.rating, 0);
      team.averageRating = team.players.length > 0 ? totalRating / team.players.length : 0;
      console.log(
        `After swapping: Team ${team.name} has ${team.players.length} players, rating: ${team.averageRating.toFixed(2)}`
      );
    });

    return newTeams;
  } catch (error) {
    console.error("Error in team balancing:", error);
    return teams; // Return original teams on error
  }
}

/**
 * Returns the CSS gradient class for a team based on its color identifier
 *
 * @param colorId Team color identifier (optional)
 * @returns CSS class with gradient background colors
 */
export function getTeamGradientClass(colorId?: string | number): string {
  // Default to a blue gradient if no color is specified
  if (!colorId) return "bg-gradient-to-br from-blue-300 to-blue-400";

  // Convert to string in case a number is passed
  const color = String(colorId);

  // Map of color identifiers to gradient classes - using pastel colors
  const gradientMap: Record<string, string> = {
    blue: "bg-gradient-to-br from-blue-300 to-blue-400",
    red: "bg-gradient-to-br from-rose-300 to-red-300",
    green: "bg-gradient-to-br from-emerald-300 to-green-300",
    yellow: "bg-gradient-to-br from-amber-200 to-amber-300",
    purple: "bg-gradient-to-br from-violet-300 to-purple-300",
    orange: "bg-gradient-to-br from-orange-200 to-orange-300",
    cyan: "bg-gradient-to-br from-cyan-200 to-cyan-300",
    pink: "bg-gradient-to-br from-pink-200 to-pink-300",
    teal: "bg-gradient-to-br from-teal-200 to-teal-300",
    indigo: "bg-gradient-to-br from-indigo-300 to-indigo-400",
    // Add more color options as needed
    "1": "bg-gradient-to-br from-blue-300 to-blue-400",
    "2": "bg-gradient-to-br from-rose-300 to-red-300",
    "3": "bg-gradient-to-br from-emerald-300 to-green-300",
    "4": "bg-gradient-to-br from-amber-200 to-amber-300",
  };

  // Return the gradient class if it exists, otherwise return the default blue gradient
  return gradientMap[color] || "bg-gradient-to-br from-blue-300 to-blue-400";
}
