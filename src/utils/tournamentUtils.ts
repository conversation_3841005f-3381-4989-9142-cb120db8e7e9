import { Team } from "@/types";
import { TournamentMatch, TournamentStanding } from "@/types/tournament";

/**
 * Calculate tournament standings based on match results
 * This is a standalone version of the calculateStandings function from useTournament
 */
export function calculateStandings(
  matches: TournamentMatch[],
  teams: Team[]
): TournamentStanding[] {
  const standings: Record<string, TournamentStanding> = {};

  // Initialize standings for all teams
  teams.forEach((team) => {
    if (!team.id) return;

    standings[team.id] = {
      team,
      played: 0,
      won: 0,
      drawn: 0,
      lost: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      goalDifference: 0,
      points: 0,
    };
  });

  // Update standings based on completed matches
  matches
    .filter((match) => match.isCompleted)
    .forEach((match) => {
      const teamAId = match.teamA.id!;
      const teamBId = match.teamB.id!;

      if (match.goalsA === null || match.goalsB === null) return;

      // Update team A stats
      standings[teamAId].played += 1;
      standings[teamAId].goalsFor += match.goalsA;
      standings[teamAId].goalsAgainst += match.goalsB;

      // Update team B stats
      standings[teamBId].played += 1;
      standings[teamBId].goalsFor += match.goalsB;
      standings[teamBId].goalsAgainst += match.goalsA;

      // Determine match result
      if (match.goalsA > match.goalsB) {
        // Team A won
        standings[teamAId].won += 1;
        standings[teamAId].points += 3;
        standings[teamBId].lost += 1;
      } else if (match.goalsA < match.goalsB) {
        // Team B won
        standings[teamBId].won += 1;
        standings[teamBId].points += 3;
        standings[teamAId].lost += 1;
      } else {
        // Draw
        standings[teamAId].drawn += 1;
        standings[teamAId].points += 1;
        standings[teamBId].drawn += 1;
        standings[teamBId].points += 1;
      }
    });

  // Calculate goal differences
  Object.values(standings).forEach((team) => {
    team.goalDifference = team.goalsFor - team.goalsAgainst;
  });

  // Sort standings by points, then goal difference, then goals scored
  return Object.values(standings).sort((a, b) => {
    if (a.points !== b.points) return b.points - a.points;
    if (a.goalDifference !== b.goalDifference) return b.goalDifference - a.goalDifference;
    return b.goalsFor - a.goalsFor;
  });
}
