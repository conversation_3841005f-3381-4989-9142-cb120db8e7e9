// Team colors for consistent display - using pastel colors with gradients
export const teamColors = [
  {
    bg: "bg-gradient-to-r from-blue-300 to-blue-400",
    bgLight: "bg-blue-50 dark:bg-blue-900/20",
    text: "text-blue-600",
    textDark: "text-blue-500 dark:text-blue-300",
    border: "border-blue-300 dark:border-blue-500/40",
  },
  {
    bg: "bg-gradient-to-r from-rose-300 to-red-300",
    bgLight: "bg-rose-50 dark:bg-rose-900/20",
    text: "text-rose-600",
    textDark: "text-rose-500 dark:text-rose-300",
    border: "border-rose-300 dark:border-rose-500/40",
  },
  {
    bg: "bg-gradient-to-r from-emerald-300 to-green-300",
    bgLight: "bg-emerald-50 dark:bg-emerald-900/20",
    text: "text-emerald-600",
    textDark: "text-emerald-500 dark:text-emerald-300",
    border: "border-emerald-300 dark:border-emerald-500/40",
  },
  {
    bg: "bg-gradient-to-r from-violet-300 to-purple-300",
    bgLight: "bg-violet-50 dark:bg-violet-900/20",
    text: "text-violet-600",
    textDark: "text-violet-500 dark:text-violet-300",
    border: "border-violet-300 dark:border-violet-500/40",
  },
  {
    bg: "bg-gradient-to-r from-amber-200 to-amber-300",
    bgLight: "bg-amber-50 dark:bg-amber-900/20",
    text: "text-amber-600",
    textDark: "text-amber-500 dark:text-amber-300",
    border: "border-amber-300 dark:border-amber-500/40",
  },
  {
    bg: "bg-gradient-to-r from-cyan-200 to-cyan-300",
    bgLight: "bg-cyan-50 dark:bg-cyan-900/20",
    text: "text-cyan-600",
    textDark: "text-cyan-500 dark:text-cyan-300",
    border: "border-cyan-300 dark:border-cyan-500/40",
  },
  {
    bg: "bg-gradient-to-r from-pink-200 to-pink-300",
    bgLight: "bg-pink-50 dark:bg-pink-900/20",
    text: "text-pink-600",
    textDark: "text-pink-500 dark:text-pink-300",
    border: "border-pink-300 dark:border-pink-500/40",
  },
  {
    bg: "bg-gradient-to-r from-indigo-300 to-indigo-400",
    bgLight: "bg-indigo-50 dark:bg-indigo-900/20",
    text: "text-indigo-600",
    textDark: "text-indigo-500 dark:text-indigo-300",
    border: "border-indigo-300 dark:border-indigo-500/40",
  },
  {
    bg: "bg-gradient-to-r from-teal-200 to-teal-300",
    bgLight: "bg-teal-50 dark:bg-teal-900/20",
    text: "text-teal-600",
    textDark: "text-teal-500 dark:text-teal-300",
    border: "border-teal-300 dark:border-teal-500/40",
  },
  {
    bg: "bg-gradient-to-r from-orange-200 to-orange-300",
    bgLight: "bg-orange-50 dark:bg-orange-900/20",
    text: "text-orange-600",
    textDark: "text-orange-500 dark:text-orange-300",
    border: "border-orange-300 dark:border-orange-500/40",
  },
];

// Simple team color mapping to ensure consistent colors
const teamColorMap = new Map<string, number>();
let nextColorIndex = 0;

// Get team color based on team ID or name
export const getTeamColor = (teamIdOrName?: string, index?: number) => {
  // If index is provided, use it directly
  if (index !== undefined) {
    return teamColors[index % teamColors.length];
  }

  // If no team ID/name, return default color
  if (!teamIdOrName) return teamColors[0];

  // Check if we've already assigned a color to this team
  if (!teamColorMap.has(teamIdOrName)) {
    // Assign the next available color
    teamColorMap.set(teamIdOrName, nextColorIndex);
    nextColorIndex = (nextColorIndex + 1) % teamColors.length;
  }

  // Return the assigned color
  return teamColors[teamColorMap.get(teamIdOrName)!];
};
