import { describe, it, expect } from "vitest";
import { calculateStandings } from "../tournamentUtils";
import { Team } from "@/types";
import { TournamentMatch } from "@/types/tournament";
import { createMockTeams, createMockTournamentMatches } from "@/test/testUtils";

describe("tournamentUtils", () => {
  describe("calculateStandings", () => {
    it("should calculate correct standings for completed matches", () => {
      // Create mock teams and adapt them to the Team interface
      const mockTeams: Team[] = createMockTeams(3).map((team) => ({
        id: team.id,
        name: team.name,
        players: [],
        averageRating: team.rating || 0,
      }));

      // Create mock matches with specific results using the utility function
      // Note: createMockTournamentMatches creates matches in this pattern:
      // Match 0: Team A vs Team B
      // Match 1: Team B vs Team C
      // Match 2: Team C vs Team A
      const mockMatches: TournamentMatch[] = createMockTournamentMatches(mockTeams, 3, {
        completedMatches: 3, // All matches are completed
        specificGoals: [
          { index: 0, goalsA: 3, goalsB: 1 }, // Team A beats Team B
          { index: 1, goalsA: 2, goalsB: 2 }, // Team B draws with Team C
          { index: 2, goalsA: 1, goalsB: 0 }, // Team C beats Team A
        ],
      });

      // Calculate standings
      const standings = calculateStandings(mockMatches, mockTeams);

      // Verify standings are sorted correctly (by points, then goal difference, then goals scored)
      expect(standings).toHaveLength(3);

      // Team A: 1 win (3 points), 1 loss (0 points), 3 goals for, 2 goals against, +1 goal difference
      const teamA = standings.find((s) => s.team.id === mockTeams[0].id);
      expect(teamA).toBeDefined();
      expect(teamA?.points).toBe(3);
      expect(teamA?.won).toBe(1);
      expect(teamA?.drawn).toBe(0);
      expect(teamA?.lost).toBe(1);
      expect(teamA?.goalsFor).toBe(3);
      expect(teamA?.goalsAgainst).toBe(2);
      expect(teamA?.goalDifference).toBe(1);

      // Team B: 1 draw (1 point), 1 loss (0 points), 3 goals for, 5 goals against, -2 goal difference
      const teamB = standings.find((s) => s.team.id === mockTeams[1].id);
      expect(teamB).toBeDefined();
      expect(teamB?.points).toBe(1);
      expect(teamB?.won).toBe(0);
      expect(teamB?.drawn).toBe(1);
      expect(teamB?.lost).toBe(1);
      expect(teamB?.goalsFor).toBe(3);
      expect(teamB?.goalsAgainst).toBe(5);
      expect(teamB?.goalDifference).toBe(-2);

      // Team C: 1 win (3 points), 1 draw (1 point), 3 goals for, 2 goals against, +1 goal difference
      const teamC = standings.find((s) => s.team.id === mockTeams[2].id);
      expect(teamC).toBeDefined();
      expect(teamC?.points).toBe(4);
      expect(teamC?.won).toBe(1);
      expect(teamC?.drawn).toBe(1);
      expect(teamC?.lost).toBe(0);
      expect(teamC?.goalsFor).toBe(3);
      expect(teamC?.goalsAgainst).toBe(2);
      expect(teamC?.goalDifference).toBe(1);

      // Verify the order of standings (Team C should be first, then Team A, then Team B)
      expect(standings[0].team.id).toBe(mockTeams[2].id); // Team C (4 points)
      expect(standings[1].team.id).toBe(mockTeams[0].id); // Team A (3 points)
      expect(standings[2].team.id).toBe(mockTeams[1].id); // Team B (1 point)
    });

    it("should handle matches with null goals correctly", () => {
      // Create mock teams and adapt them to the Team interface
      const mockTeams: Team[] = createMockTeams(2).map((team) => ({
        id: team.id,
        name: team.name,
        players: [],
        averageRating: team.rating || 0,
      }));

      // Create a match with null goals (not completed)
      const mockMatches: TournamentMatch[] = [
        {
          id: "match-1",
          teamA: mockTeams[0],
          teamB: mockTeams[1],
          goalsA: null,
          goalsB: null,
          isCompleted: true, // Marked as completed but has null goals
          matchNumber: 1,
        },
      ];

      // Calculate standings
      const standings = calculateStandings(mockMatches, mockTeams);

      // Verify all teams have 0 for all stats
      expect(standings).toHaveLength(2);
      standings.forEach((team) => {
        expect(team.played).toBe(0);
        expect(team.won).toBe(0);
        expect(team.drawn).toBe(0);
        expect(team.lost).toBe(0);
        expect(team.goalsFor).toBe(0);
        expect(team.goalsAgainst).toBe(0);
        expect(team.goalDifference).toBe(0);
        expect(team.points).toBe(0);
      });
    });

    it("should handle empty matches array", () => {
      // Create mock teams and adapt them to the Team interface
      const mockTeams: Team[] = createMockTeams(3).map((team) => ({
        id: team.id,
        name: team.name,
        players: [],
        averageRating: team.rating || 0,
      }));

      // Empty matches array
      const mockMatches: TournamentMatch[] = [];

      // Calculate standings
      const standings = calculateStandings(mockMatches, mockTeams);

      // Verify all teams have 0 for all stats
      expect(standings).toHaveLength(3);
      standings.forEach((team) => {
        expect(team.played).toBe(0);
        expect(team.won).toBe(0);
        expect(team.drawn).toBe(0);
        expect(team.lost).toBe(0);
        expect(team.goalsFor).toBe(0);
        expect(team.goalsAgainst).toBe(0);
        expect(team.goalDifference).toBe(0);
        expect(team.points).toBe(0);
      });
    });

    it("should handle teams with missing IDs", () => {
      // Create teams with one missing ID
      const mockTeams: Team[] = [
        { id: "team-1", name: "Team A", players: [], averageRating: 80 },
        { id: undefined, name: "Team B", players: [], averageRating: 80 }, // Missing ID
        { id: "team-3", name: "Team C", players: [], averageRating: 80 },
      ];

      // Create matches
      const mockMatches: TournamentMatch[] = [
        {
          id: "match-1",
          teamA: mockTeams[0],
          teamB: mockTeams[2],
          goalsA: 1,
          goalsB: 1,
          isCompleted: true,
          matchNumber: 1,
        },
      ];

      // Calculate standings
      const standings = calculateStandings(mockMatches, mockTeams);

      // Verify only teams with IDs are in standings
      expect(standings).toHaveLength(2); // Only 2 teams have IDs

      // Verify the team with missing ID is not in standings
      const teamIds = standings.map((s) => s.team.id);
      expect(teamIds).toContain("team-1");
      expect(teamIds).toContain("team-3");
      expect(teamIds).not.toContain(undefined);
    });

    it("should sort standings correctly by points, goal difference, and goals scored", () => {
      // Create mock teams and adapt them to the Team interface
      const mockTeams: Team[] = createMockTeams(3).map((team) => ({
        id: team.id,
        name: team.name,
        players: [],
        averageRating: team.rating || 0,
      }));

      // Create matches with specific results to test sorting logic
      // Note: createMockTournamentMatches creates matches in this pattern:
      // Match 0: Team A vs Team B
      // Match 1: Team B vs Team C
      // Match 2: Team C vs Team A
      const mockMatches: TournamentMatch[] = createMockTournamentMatches(mockTeams, 3, {
        completedMatches: 3, // All matches are completed
        specificGoals: [
          { index: 0, goalsA: 3, goalsB: 1 }, // Team A beats Team B
          { index: 1, goalsA: 2, goalsB: 1 }, // Team B beats Team C
          { index: 2, goalsA: 1, goalsB: 1 }, // Team C draws with Team A
        ],
      });

      // Calculate standings
      const standings = calculateStandings(mockMatches, mockTeams);

      // Verify standings are sorted correctly
      expect(standings).toHaveLength(3);

      // Expected order: Team A (3 pts, +2 GD), Team B (3 pts, +1 GD), Team C (0 pts)
      expect(standings[0].team.id).toBe(mockTeams[0].id); // Team A (3 pts, +2 GD)
      expect(standings[1].team.id).toBe(mockTeams[1].id); // Team B (3 pts, -1 GD)
      expect(standings[2].team.id).toBe(mockTeams[2].id); // Team C (0 pts, -1 GD)

      // Verify Team A stats
      expect(standings[0].points).toBe(4); // 1 win (3 pts) + 1 draw (1 pt)
      expect(standings[0].goalDifference).toBe(2); // 4 goals for, 2 against

      // Verify Team B stats
      expect(standings[1].points).toBe(3); // 1 win (3 pts)
      expect(standings[1].goalDifference).toBe(-1); // 3 goals for, 4 against

      // Verify Team C stats
      expect(standings[2].points).toBe(1); // 1 draw (1 pt)
      expect(standings[2].goalDifference).toBe(-1); // 2 goals for, 3 against
    });
  });
});
