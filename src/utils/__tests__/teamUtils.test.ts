import { describe, it, expect } from "vitest";
import { generateTeams, balanceTeamsBySwapping } from "../teamUtils";
import { Player, PlayerRole } from "@/types";

describe("Team Generation Utils", () => {
  describe("generateTeams", () => {
    it("should generate 2 teams with exactly 10 players", () => {
      // Setup: 10 players with different roles and ratings
      const players: Player[] = [
        { id: "1", name: "Player 1", rating: 90, status: "in", role: "goalkeeper" },
        { id: "2", name: "Player 2", rating: 85, status: "in", role: "defender" },
        { id: "3", name: "Player 3", rating: 80, status: "in", role: "defender" },
        { id: "4", name: "Player 4", rating: 75, status: "in", role: "midfielder" },
        { id: "5", name: "Player 5", rating: 95, status: "in", role: "midfielder" },
        { id: "6", name: "Player 6", rating: 70, status: "in", role: "midfielder" },
        { id: "7", name: "Player 7", rating: 88, status: "in", role: "striker" },
        { id: "8", name: "Player 8", rating: 82, status: "in", role: "striker" },
        { id: "9", name: "Player 9", rating: 75, status: "in", role: "allrounder" },
        { id: "10", name: "Player 10", rating: 85, status: "in", role: "goalkeeper" },
      ];

      // Generate teams with any team count (should optimize to 2)
      const teams = generateTeams(players, 4);

      // Expectations:
      // 1. It should create 2 teams
      expect(teams).toHaveLength(2);

      // 2. Each team should have 5 players (equal division)
      expect(teams[0].players).toHaveLength(5);
      expect(teams[1].players).toHaveLength(5);

      // 3. Roles should be distributed as evenly as possible
      // Count roles in each team
      const team1Roles = countRoles(teams[0].players);
      const team2Roles = countRoles(teams[1].players);

      // Check goalkeeper distribution (should be 1 in each team)
      expect(team1Roles.goalkeeper + team2Roles.goalkeeper).toBe(2);
      expect(Math.abs(team1Roles.goalkeeper - team2Roles.goalkeeper)).toBeLessThanOrEqual(1);

      // Check other role distributions
      expect(Math.abs(team1Roles.defender - team2Roles.defender)).toBeLessThanOrEqual(1);
      expect(Math.abs(team1Roles.midfielder - team2Roles.midfielder)).toBeLessThanOrEqual(1);
      expect(Math.abs(team1Roles.striker - team2Roles.striker)).toBeLessThanOrEqual(1);
      expect(Math.abs(team1Roles.allrounder - team2Roles.allrounder)).toBeLessThanOrEqual(1);
    });

    it("should generate 3 teams with exactly 15 players", () => {
      // Setup: Create exactly 3 goalkeepers to enable distribution of 1 per team
      const players: Player[] = [];

      // Create exactly 3 goalkeepers
      for (let i = 0; i < 3; i++) {
        players.push({
          id: `gk-${i + 1}`,
          name: `Goalkeeper ${i + 1}`,
          rating: 95 - i * 7, // 95, 88, 81
          status: "in",
          role: "goalkeeper",
        });
      }

      // Add remaining players to reach 15 total
      for (let i = 0; i < 12; i++) {
        const role: PlayerRole =
          i < 3 ? "striker" : i < 6 ? "defender" : i < 9 ? "midfielder" : "allrounder";

        players.push({
          id: `player-${i + 4}`,
          name: `Player ${i + 4}`,
          rating: Math.floor(Math.random() * 20) + 70, // Random rating between 70-90
          status: "in",
          role,
        });
      }

      // Generate teams
      const teams = generateTeams(players, 3);

      // Expectation: 3 teams with 5 players each
      expect(teams).toHaveLength(3);
      teams.forEach((team) => {
        expect(team.players).toHaveLength(5);
      });

      // Check that goalkeepers are evenly distributed (one per team)
      const goalkeeperDistribution = teams.map(
        (team) => team.players.filter((p) => p.role === "goalkeeper").length
      );
      expect(goalkeeperDistribution).toEqual([1, 1, 1]);
    });

    it("should generate 4 teams with exactly 20 players", () => {
      // Setup: Create exactly 4 goalkeepers to enable distribution of 1 per team
      const players: Player[] = [];

      // Create exactly 4 goalkeepers
      for (let i = 0; i < 4; i++) {
        players.push({
          id: `gk-${i + 1}`,
          name: `Goalkeeper ${i + 1}`,
          rating: 99 - i * 7, // 99, 92, 85, 78
          status: "in",
          role: "goalkeeper",
        });
      }

      // Add remaining players to reach 20 total
      for (let i = 0; i < 16; i++) {
        const role: PlayerRole =
          i < 4 ? "striker" : i < 8 ? "defender" : i < 12 ? "midfielder" : "allrounder";

        players.push({
          id: `player-${i + 5}`,
          name: `Player ${i + 5}`,
          rating: Math.floor(Math.random() * 20) + 70, // Random rating between 70-90
          status: "in",
          role,
        });
      }

      // Generate teams
      const teams = generateTeams(players, 4);

      // Expectation: 4 teams with 5 players each
      expect(teams).toHaveLength(4);
      teams.forEach((team) => {
        expect(team.players).toHaveLength(5);
      });

      // Check that goalkeepers are evenly distributed (one per team)
      const goalkeeperDistribution = teams.map(
        (team) => team.players.filter((p) => p.role === "goalkeeper").length
      );
      expect(goalkeeperDistribution).toEqual([1, 1, 1, 1]);
    });

    it("should return an empty array when given 0 players", () => {
      // Setup: Provide an empty array of players
      const players: Player[] = [];

      // Generate teams
      const teams = generateTeams(players, 2);

      // Expectation: The function should return an empty array
      expect(teams).toHaveLength(0);
    });

    it("should distribute roles evenly first, while balancing ratings", () => {
      // Setup: 10 players with rating differences
      const players: Player[] = [
        // 5 players with high ratings
        { id: "1", name: "Strong 1", rating: 95, status: "in", role: "goalkeeper" },
        { id: "2", name: "Strong 2", rating: 93, status: "in", role: "defender" },
        { id: "3", name: "Strong 3", rating: 91, status: "in", role: "midfielder" },
        { id: "4", name: "Strong 4", rating: 89, status: "in", role: "striker" },
        { id: "5", name: "Strong 5", rating: 87, status: "in", role: "allrounder" },

        // 5 players with low ratings
        { id: "6", name: "Weak 1", rating: 75, status: "in", role: "goalkeeper" },
        { id: "7", name: "Weak 2", rating: 73, status: "in", role: "defender" },
        { id: "8", name: "Weak 3", rating: 72, status: "in", role: "midfielder" },
        { id: "9", name: "Weak 4", rating: 71, status: "in", role: "striker" },
        { id: "10", name: "Weak 5", rating: 70, status: "in", role: "allrounder" },
      ];

      // Generate teams
      const teams = generateTeams(players, 2);

      // Expectation: Both teams should have been created
      expect(teams).toHaveLength(2);

      // Count the distribution of players
      const team1StrongCount = teams[0].players.filter((p) => p.rating >= 87).length;
      const team1WeakCount = teams[0].players.filter((p) => p.rating <= 75).length;
      const team2StrongCount = teams[1].players.filter((p) => p.rating >= 87).length;
      const team2WeakCount = teams[1].players.filter((p) => p.rating <= 75).length;

      // The improved algorithm should generate more balanced teams from the start
      // Each team should have 5 players total
      expect(teams[0].players.length).toBe(5);
      expect(teams[1].players.length).toBe(5);

      // Total players should be preserved
      expect(team1StrongCount + team2StrongCount).toBe(5); // All strong players assigned
      expect(team1WeakCount + team2WeakCount).toBe(5); // All weak players assigned

      // Both teams should have a mix of strong and weak players
      expect(team1StrongCount).toBeGreaterThan(0);
      expect(team1WeakCount).toBeGreaterThan(0);
      expect(team2StrongCount).toBeGreaterThan(0);
      expect(team2WeakCount).toBeGreaterThan(0);

      // Check that the rating difference is reasonable - shouldn't be extreme
      const ratingDifference = Math.abs(teams[0].averageRating - teams[1].averageRating);
      expect(ratingDifference).toBeLessThan(30); // Not a huge difference
    });

    it("should prioritize roles in the correct order: goalkeeper, striker, allrounder, defender, midfielder", () => {
      // Setup: Create players with different roles in a specific order
      const players: Player[] = [
        // Create multiple players of each role type with mixed ratings
        { id: "gk1", name: "Goalkeeper 1", rating: 85, status: "in", role: "goalkeeper" },
        { id: "gk2", name: "Goalkeeper 2", rating: 80, status: "in", role: "goalkeeper" },

        { id: "st1", name: "Striker 1", rating: 90, status: "in", role: "striker" },
        { id: "st2", name: "Striker 2", rating: 85, status: "in", role: "striker" },

        { id: "ar1", name: "Allrounder 1", rating: 88, status: "in", role: "allrounder" },
        { id: "ar2", name: "Allrounder 2", rating: 82, status: "in", role: "allrounder" },

        { id: "df1", name: "Defender 1", rating: 86, status: "in", role: "defender" },
        { id: "df2", name: "Defender 2", rating: 84, status: "in", role: "defender" },

        { id: "mf1", name: "Midfielder 1", rating: 83, status: "in", role: "midfielder" },
        { id: "mf2", name: "Midfielder 2", rating: 81, status: "in", role: "midfielder" },
      ];

      // Generate 2 teams with 5 players each
      const teams = generateTeams(players, 2);

      // Check that we have 2 teams with 5 players each
      expect(teams).toHaveLength(2);
      expect(teams[0].players).toHaveLength(5);
      expect(teams[1].players).toHaveLength(5);

      // Verify priority order by checking which roles went to which team

      // Goalkeepers should be distributed first (one per team when possible)
      expect(teams[0].players.some((p) => p.role === "goalkeeper")).toBe(true);
      expect(teams[1].players.some((p) => p.role === "goalkeeper")).toBe(true);

      // Strikers should be distributed next (one per team when possible)
      expect(teams[0].players.some((p) => p.role === "striker")).toBe(true);
      expect(teams[1].players.some((p) => p.role === "striker")).toBe(true);

      // Allrounders should be distributed next
      expect(teams[0].players.some((p) => p.role === "allrounder")).toBe(true);
      expect(teams[1].players.some((p) => p.role === "allrounder")).toBe(true);

      // Remaining roles (defender, midfielder) should be distributed after
      const team0DefenderOrMidfielders = teams[0].players.filter(
        (p) => p.role === "defender" || p.role === "midfielder"
      ).length;

      const team1DefenderOrMidfielders = teams[1].players.filter(
        (p) => p.role === "defender" || p.role === "midfielder"
      ).length;

      // Should have at least one defender or midfielder in each team
      expect(team0DefenderOrMidfielders).toBeGreaterThan(0);
      expect(team1DefenderOrMidfielders).toBeGreaterThan(0);
    });

    it("should generate balanced teams using real player data", () => {
      // Setup: Real world player data from the app
      const players: Player[] = [
        {
          id: "1",
          name: "Viktor Reich",
          jerseyNumber: 8,
          rating: 82,
          status: "in",
          role: "midfielder",
        },
        {
          id: "2",
          name: "Aykut Oktay",
          jerseyNumber: 54,
          rating: 82,
          status: "in",
          role: "defender",
        },
        {
          id: "3",
          name: "Esref Kapucu",
          jerseyNumber: 99,
          rating: 84,
          status: "in",
          role: "midfielder",
        },
        {
          id: "4",
          name: "Markus Frank",
          jerseyNumber: 6,
          rating: 91,
          status: "in",
          role: "striker",
        },
        {
          id: "5",
          name: "Ismail Bas",
          jerseyNumber: 64,
          rating: 83,
          status: "in",
          role: "goalkeeper",
        },
        {
          id: "6",
          name: "Burak Ceyhan",
          jerseyNumber: 50,
          rating: 75,
          status: "in",
          role: "allrounder",
        },
        {
          id: "7",
          name: "Berat Kaya",
          jerseyNumber: 10,
          rating: 85,
          status: "in",
          role: "midfielder",
        },
        {
          id: "8",
          name: "Youssef Ahdour",
          jerseyNumber: 99,
          rating: 78,
          status: "in",
          role: "allrounder",
        },
        {
          id: "9",
          name: "Brahim El-Bakali",
          jerseyNumber: 99,
          rating: 90,
          status: "in",
          role: "striker",
        },
        {
          id: "10",
          name: "Ahmed Abara",
          jerseyNumber: 9,
          rating: 88,
          status: "in",
          role: "allrounder",
        },
        {
          id: "11",
          name: "Bent Exner",
          jerseyNumber: 3,
          rating: 87,
          status: "in",
          role: "midfielder",
        },
        {
          id: "12",
          name: "Ersin Basol",
          jerseyNumber: 10,
          rating: 89,
          status: "in",
          role: "allrounder",
        },
        {
          id: "13",
          name: "Tevfik Dincer",
          jerseyNumber: 64,
          rating: 89,
          status: "in",
          role: "striker",
        },
        {
          id: "14",
          name: "Samet Kockar",
          jerseyNumber: 10,
          rating: 88,
          status: "in",
          role: "allrounder",
        },
        {
          id: "15",
          name: "Ermond Ademaj",
          jerseyNumber: 99,
          rating: 90,
          status: "in",
          role: "striker",
        },
        {
          id: "16",
          name: "Muammer Cakir",
          jerseyNumber: 89,
          rating: 80,
          status: "in",
          role: "striker",
        },
        {
          id: "17",
          name: "Jobin Karapurackal",
          jerseyNumber: 23,
          rating: 87,
          status: "in",
          role: "allrounder",
        },
        {
          id: "18",
          name: "Akin Zengin",
          jerseyNumber: 50,
          rating: 88,
          status: "in",
          role: "defender",
        },
        {
          id: "19",
          name: "Samir Bouajaj",
          jerseyNumber: 11,
          rating: 89,
          status: "in",
          role: "allrounder",
        },
        {
          id: "20",
          name: "Emre Basol",
          jerseyNumber: 99,
          rating: 83,
          status: "in",
          role: "midfielder",
        },
      ];

      // Generate 4 teams
      const teams = generateTeams(players, 4);

      // Should create 4 teams with 5 players each
      expect(teams).toHaveLength(4);
      teams.forEach((team) => {
        expect(team.players).toHaveLength(5);
      });

      // Verify role distribution based on priority
      // The goalkeeper should be in the first team
      expect(teams[0].players.some((p) => p.role === "goalkeeper")).toBe(true);

      // Strikers should be distributed to first teams
      const strikerDistribution = teams.map(
        (team) => team.players.filter((p) => p.role === "striker").length
      );

      // First 4 teams should each get 1 striker if possible (we have 5 strikers total)
      expect(strikerDistribution.reduce((sum, count) => sum + count, 0)).toBe(5);

      // The rating variance between teams should be reasonable
      const teamRatings = teams.map((team) => team.averageRating);
      const maxRatingDiff = Math.max(...teamRatings) - Math.min(...teamRatings);
      expect(maxRatingDiff).toBeLessThan(15); // Not a huge difference in average ratings
    });
  });

  describe("balanceTeamsBySwapping", () => {
    it("should improve team balance through player swapping", () => {
      // Setup: Create intentionally unfair teams
      const team1: Player[] = [
        { id: "1", name: "High 1", rating: 95, status: "in", role: "goalkeeper" },
        { id: "2", name: "High 2", rating: 93, status: "in", role: "defender" },
        { id: "3", name: "High 3", rating: 91, status: "in", role: "midfielder" },
        { id: "4", name: "High 4", rating: 89, status: "in", role: "striker" },
        { id: "5", name: "High 5", rating: 87, status: "in", role: "allrounder" },
      ];

      const team2: Player[] = [
        { id: "6", name: "Low 1", rating: 75, status: "in", role: "goalkeeper" },
        { id: "7", name: "Low 2", rating: 73, status: "in", role: "defender" },
        { id: "8", name: "Low 3", rating: 72, status: "in", role: "midfielder" },
        { id: "9", name: "Low 4", rating: 71, status: "in", role: "striker" },
        { id: "10", name: "Low 5", rating: 70, status: "in", role: "allrounder" },
      ];

      const unfairTeams = [
        { id: "team1", name: "Team 1", averageRating: 94, players: team1 },
        { id: "team2", name: "Team 2", averageRating: 14, players: team2 },
      ];

      // Calculate original variance
      const originalRatings = unfairTeams.map((team) => team.averageRating);
      const originalMean = originalRatings.reduce((sum, r) => sum + r, 0) / originalRatings.length;
      const originalVariance =
        originalRatings.reduce((sum, r) => sum + Math.pow(r - originalMean, 2), 0) /
        originalRatings.length;

      // Apply balancing
      const balancedTeams = balanceTeamsBySwapping(unfairTeams);

      // Calculate new variance
      const newRatings = balancedTeams.map((team) => team.averageRating);
      const newMean = newRatings.reduce((sum, r) => sum + r, 0) / newRatings.length;
      const newVariance =
        newRatings.reduce((sum, r) => sum + Math.pow(r - newMean, 2), 0) / newRatings.length;

      // Expectations: variance should be reduced after balancing
      expect(newVariance).toBeLessThan(originalVariance);

      // The average ratings should be closer together
      const originalDifference = Math.abs(
        unfairTeams[0].averageRating - unfairTeams[1].averageRating
      );
      const newDifference = Math.abs(
        balancedTeams[0].averageRating - balancedTeams[1].averageRating
      );
      expect(newDifference).toBeLessThan(originalDifference);

      // Players should have been swapped while respecting roles
      const team1GoalkeepersBefore = team1.filter((p) => p.role === "goalkeeper").length;
      const team2GoalkeepersBefore = team2.filter((p) => p.role === "goalkeeper").length;

      const team1GoalkeepersAfter = balancedTeams[0].players.filter(
        (p) => p.role === "goalkeeper"
      ).length;
      const team2GoalkeepersAfter = balancedTeams[1].players.filter(
        (p) => p.role === "goalkeeper"
      ).length;

      // Role counts should be maintained (for roles like goalkeeper)
      expect(team1GoalkeepersBefore + team2GoalkeepersBefore).toBe(
        team1GoalkeepersAfter + team2GoalkeepersAfter
      );

      // Each team should still have 5 players each (team size preserved)
      expect(balancedTeams[0].players.length).toBe(5);
      expect(balancedTeams[1].players.length).toBe(5);
    });
  });
});

/**
 * Helper function to count the number of players in each role
 */
function countRoles(players: Player[]): Record<string, number> {
  const roleCount: Record<string, number> = {
    goalkeeper: 0,
    defender: 0,
    midfielder: 0,
    striker: 0,
    allrounder: 0,
    undefined: 0,
  };

  players.forEach((player) => {
    if (player.role) {
      roleCount[player.role]++;
    } else {
      roleCount["undefined"]++;
    }
  });

  return roleCount;
}
