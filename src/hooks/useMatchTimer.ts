import { useState, useEffect, useRef, useCallback } from "react";

interface UseMatchTimerProps {
  initialDurationSeconds: number;
  onTimeUp?: () => void;
  startTime?: Date;
}

interface UseMatchTimerReturn {
  timeRemaining: number;
  formattedTime: string;
  isRunning: boolean;
  isPaused: boolean;
  progress: number;
  startTimer: () => void;
  pauseTimer: () => void;
  resetTimer: (newDuration?: number) => void;
  addTime: (seconds: number) => void;
}

export function useMatchTimer({
  initialDurationSeconds,
  onTimeUp,
  startTime,
}: UseMatchTimerProps): UseMatchTimerReturn {
  // Calculate remaining time if match was already started
  const calculateInitialTimeRemaining = () => {
    if (!startTime) return initialDurationSeconds;

    const now = new Date();
    const elapsedSeconds = Math.floor((now.getTime() - startTime.getTime()) / 1000);

    // If match should be already finished
    if (elapsedSeconds >= initialDurationSeconds) {
      return 0;
    }

    return initialDurationSeconds - elapsedSeconds;
  };

  const initialTimeRemaining = calculateInitialTimeRemaining();
  const [timeRemaining, setTimeRemaining] = useState(initialTimeRemaining);
  const [isRunning, setIsRunning] = useState(startTime !== undefined && initialTimeRemaining > 0);
  const [isPaused, setIsPaused] = useState(false);
  const [totalDuration, setTotalDuration] = useState(initialDurationSeconds);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Format time as MM:SS
  const formattedTime = useCallback(() => {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }, [timeRemaining]);

  // Calculate progress percentage
  const progress = useCallback(() => {
    return (timeRemaining / totalDuration) * 100;
  }, [timeRemaining, totalDuration]);

  // Start the timer
  const startTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // If timer is already at 0, don't start
    if (timeRemaining <= 0) {
      if (onTimeUp) {
        onTimeUp();
      }
      return;
    }

    setIsRunning(true);
    setIsPaused(false);

    timerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          setIsRunning(false);
          if (onTimeUp) {
            onTimeUp();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [onTimeUp, timeRemaining]);

  // Pause the timer
  const pauseTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsRunning(false);
    setIsPaused(true);
  }, []);

  // Reset the timer
  const resetTimer = useCallback(
    (newDuration?: number) => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      const duration = newDuration !== undefined ? newDuration : initialDurationSeconds;
      setTotalDuration(duration);
      setTimeRemaining(duration);
      setIsRunning(false);
      setIsPaused(false);
    },
    [initialDurationSeconds]
  );

  // Add time to the timer
  const addTime = useCallback((seconds: number) => {
    setTimeRemaining((prev) => prev + seconds);
    setTotalDuration((prev) => prev + seconds);
  }, []);

  // Start timer automatically if startTime is provided
  useEffect(() => {
    if (startTime && initialTimeRemaining > 0 && !isPaused) {
      startTimer();
    }
  }, [startTime, initialTimeRemaining, isPaused, startTimer]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return {
    timeRemaining,
    formattedTime: formattedTime(),
    isRunning,
    isPaused,
    progress: progress(),
    startTimer,
    pauseTimer,
    resetTimer,
    addTime,
  };
}
