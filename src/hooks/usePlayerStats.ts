import { useState, useEffect } from "react";
import { useGameSessions } from "@/hooks/useGameSessions";
import { Player, GameSession } from "@/types";
import { supabase } from "@/integrations/supabase/client";

interface CoPlayer {
  id?: string;
  name: string;
  gamesCount: number;
}

// Interface für die MVP-Winner-Daten aus der MatchHistory
interface MVPWinner {
  playerName: string;
  jerseyNumber?: number | null;
  matchId: string;
}

export interface PlayerStats {
  totalGames: number;
  participationPercentage: number;
  mvpCount: number;
  winRate: number;
  averageRating: number;
  lastGameDate?: Date;
  lastGameId?: string;
  coPlayers: CoPlayer[];
  positions: {
    [key: string]: number; // position name -> games played
  };
}

export const usePlayerStats = (player: Player | null) => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<PlayerStats>({
    totalGames: 0,
    participationPercentage: 0,
    mvpCount: 0,
    winRate: 0,
    averageRating: 0,
    lastGameId: undefined,
    coPlayers: [],
    positions: {},
  });

  // Always fetch past sessions when player stats are needed
  const { pastSessions, loading: sessionsLoading } = useGameSessions({
    fetchCurrent: false,
    fetchPast: true,
  });

  useEffect(() => {
    if (!player || sessionsLoading) {
      return;
    }

    setLoading(true);

    // Function to count games a player participated in
    const calculateGamesParticipated = (sessions: GameSession[], playerId: string | undefined) => {
      if (!playerId) return [];

      return sessions.filter((session) => {
        if (!session.teams) return false;

        return session.teams.some((team) => team.players.some((p) => p.id === playerId));
      });
    };

    // Fetch MVP count directly from database using player's ID
    const fetchMVPCount = async (playerId: string | undefined, playerName: string) => {
      if (!playerId) return 0;

      try {
        // First get all voting periods where this player is the MVP winner
        const { data: winners, error } = await (supabase as any)
          .from("mvp_winners")
          .select(
            `
            id,
            player_id,
            game_session_id
          `
          )
          .eq("player_id", playerId);

        if (error) {
          console.error("Error fetching MVP winners:", error);
          return 0;
        }

        // Count how many times this player was an MVP
        return winners?.length || 0;
      } catch (error) {
        console.error("Error calculating MVP count:", error);
        return 0;
      }
    };

    const calculateWinRate = (sessions: GameSession[], playerId: string | undefined) => {
      if (!playerId) return 0;

      // In football there are typically no "winning" teams recorded
      // This is a placeholder for future functionality
      return 0;
    };

    const calculatePositionStats = (sessions: GameSession[], playerId: string | undefined) => {
      if (!playerId) return {};

      const positions: { [key: string]: number } = {};

      // Count role occurrences (we use the current role as there's no role history)
      if (player.role) {
        const roleMapping: { [key: string]: string } = {
          goalkeeper: "Torwart",
          defender: "Verteidiger",
          midfielder: "Mittelfeld",
          striker: "Stürmer",
          allrounder: "Allrounder",
        };

        const roleName = roleMapping[player.role] || player.role;
        positions[roleName] = sessions.length;
      }

      return positions;
    };

    const calculateLastGameDate = (sessions: GameSession[]) => {
      if (sessions.length === 0) return { date: undefined, gameId: undefined };

      // Sort sessions by date (descending) and take the most recent
      const sortedSessions = [...sessions].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      const lastSession = sortedSessions[0];

      return {
        date: new Date(lastSession.date),
        gameId: lastSession.id,
      };
    };

    const calculateCoPlayers = (sessions: GameSession[], playerId: string | undefined) => {
      if (!playerId || sessions.length === 0) return [];

      // Maps player IDs to number of shared games
      const coPlayerCounts: Record<string, { id?: string; name: string; count: number }> = {};

      // Go through each session where the player participated
      sessions.forEach((session) => {
        if (!session.teams) return;

        // Find which team the current player is on
        const playerTeam = session.teams.find((team) =>
          team.players.some((p) => p.id === playerId)
        );

        if (!playerTeam) return; // Skip if player's team can't be found

        // Only count players from the same team
        playerTeam.players.forEach((coPlayer) => {
          // Skip if this is the current player
          if (coPlayer.id === playerId) return;

          // Add to co-player counts
          const key = coPlayer.id || coPlayer.name;
          if (!coPlayerCounts[key]) {
            coPlayerCounts[key] = {
              id: coPlayer.id,
              name: coPlayer.name,
              count: 1,
            };
          } else {
            coPlayerCounts[key].count++;
          }
        });
      });

      // Convert to array and sort by count (descending)
      const sortedCoPlayers = Object.values(coPlayerCounts)
        .sort((a, b) => b.count - a.count)
        .map((cp) => ({
          id: cp.id,
          name: cp.name,
          gamesCount: cp.count,
        }));

      // Return top 3 co-players
      return sortedCoPlayers.slice(0, 3);
    };

    const calculateStats = async () => {
      try {
        const participatedSessions = calculateGamesParticipated(pastSessions, player.id);
        const mvpCount = await fetchMVPCount(player.id, player.name);
        const winRate = calculateWinRate(pastSessions, player.id);
        const positions = calculatePositionStats(participatedSessions, player.id);
        const { date, gameId } = calculateLastGameDate(participatedSessions);
        const coPlayers = calculateCoPlayers(participatedSessions, player.id);

        setStats({
          totalGames: participatedSessions.length,
          participationPercentage:
            pastSessions.length > 0
              ? Math.round((participatedSessions.length / pastSessions.length) * 100)
              : 0,
          mvpCount,
          winRate,
          averageRating: player.rating || 0,
          lastGameDate: date,
          lastGameId: gameId,
          coPlayers,
          positions,
        });

        setLoading(false);
      } catch (error) {
        console.error("Error calculating player stats:", error);
        setLoading(false);
      }
    };

    calculateStats();
  }, [player, pastSessions, sessionsLoading]);

  return { stats, loading };
};
