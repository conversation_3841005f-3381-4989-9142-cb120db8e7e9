import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { closeVotingPeriodAndCalculateMVP } from "@/services/mvpService";

interface MVPWinner {
  player_id: string;
  player_name: string;
  vote_count: number;
  jersey_number?: number | null;
}

interface MVPVotingPeriod {
  id: string;
  game_session_id: string;
  is_open: boolean;
  ends_at: string;
  game_sessions: {
    date: string;
  };
  mvp_winners?: MVPWinner[];
}

interface VoteResult {
  voting_period_id: string;
  player_id: string;
  player_name: string;
  vote_count: number;
  vote_percentage: number;
}

export function useMVPVotingAdmin() {
  const [votingPeriods, setVotingPeriods] = useState<MVPVotingPeriod[]>([]);
  const [voteResults, setVoteResults] = useState<Record<string, VoteResult[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [closingPeriodId, setClosingPeriodId] = useState<string | null>(null);

  const fetchVotingPeriods = async () => {
    try {
      const { data: periods, error } = await (supabase as any)
        .from("mvp_voting_periods")
        .select(
          `
          id,
          game_session_id,
          is_open,
          ends_at,
          game_sessions (
            date
          )
        `
        )
        .order("ends_at", { ascending: false });

      if (error) throw error;

      console.log("Fetched voting periods:", periods);

      // Fetch MVP winners data separately to avoid the relationship error
      const { data: winners, error: winnersError } = await (supabase as any).from("mvp_winners")
        .select(`
          voting_period_id,
          player_id,
          vote_count,
          players (
            name,
            jersey_number
          )
        `);

      if (winnersError) throw winnersError;

      console.log("Fetched MVP winners:", winners);

      // Create a map of winners by voting period ID (now supporting multiple winners per period)
      const winnersMap: Record<string, MVPWinner[]> = {};

      winners?.forEach((winner: any) => {
        const periodId = winner.voting_period_id;
        const winnerData: MVPWinner = {
          player_id: winner.player_id,
          player_name: winner.players.name,
          vote_count: winner.vote_count,
          jersey_number: winner.players.jersey_number,
        };

        if (!winnersMap[periodId]) {
          winnersMap[periodId] = [];
        }
        winnersMap[periodId].push(winnerData);
      });

      console.log("Winners map:", winnersMap);

      // Merge winners data with voting periods
      const transformedPeriods = periods?.map((period: any) => ({
        ...period,
        mvp_winners: winnersMap[period.id] || [],
      }));

      console.log("Transformed periods with winners:", transformedPeriods);

      setVotingPeriods(transformedPeriods || []);

      const resultsPromises =
        periods?.map(async (period: MVPVotingPeriod) => {
          const { data: results, error: resultsError } = await (supabase as any)
            .from("mvp_vote_results")
            .select("*")
            .eq("voting_period_id", period.id)
            .order("vote_count", { ascending: false });

          if (resultsError) throw resultsError;
          return { [period.id]: results };
        }) || [];

      const allResults = await Promise.all(resultsPromises);
      const combinedResults = allResults.reduce((acc, curr) => ({ ...acc, ...curr }), {});
      setVoteResults(combinedResults);

      console.log("Vote results:", combinedResults);
    } catch (error) {
      console.error("Error fetching MVP voting periods:", error);
      toast({
        title: "Fehler",
        description: "MVP-Voting-Daten konnten nicht geladen werden",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const closeVotingPeriod = async (periodId: string) => {
    setClosingPeriodId(periodId);
    try {
      // Use the new function to close voting period and calculate MVP winners
      const success = await closeVotingPeriodAndCalculateMVP(periodId);

      if (!success) {
        throw new Error("Failed to close voting period and calculate MVP winners");
      }

      toast({
        title: "Erfolgreich",
        description: "MVP-Abstimmung wurde geschlossen und Gewinner berechnet",
      });

      await fetchVotingPeriods();
    } catch (error) {
      console.error("Error closing MVP voting period:", error);
      toast({
        title: "Fehler",
        description: "MVP-Abstimmung konnte nicht geschlossen werden",
        variant: "destructive",
      });
    } finally {
      setClosingPeriodId(null);
    }
  };

  useEffect(() => {
    fetchVotingPeriods();
  }, []);

  return { votingPeriods, voteResults, isLoading, closeVotingPeriod, closingPeriodId };
}
