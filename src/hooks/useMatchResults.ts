import { useState, useEffect } from "react";
import { getMatchResultsForSession } from "@/services/matchResultsService";
import { MatchResult } from "@/types/match-results";

export interface MatchResultsMap {
  [gameSessionId: string]: MatchResult[];
}

export function useMatchResults(gameSessionIds: string[]) {
  const [results, setResults] = useState<MatchResultsMap>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Nur laden, wenn aktive Session-IDs vorhanden sind
    if (gameSessionIds.length === 0) return;

    async function fetchResults() {
      setLoading(true);
      setError(null);

      try {
        // Alle Ergebnisse parallel laden
        const resultPromises = gameSessionIds.map((id) =>
          getMatchResultsForSession(id)
            .then((data) => ({ id, data }))
            .catch((err) => {
              console.error(`Error fetching results for session ${id}:`, err);
              return { id, data: [] }; // Leeres Array bei <PERSON> zurückgeben
            })
        );

        const allResults = await Promise.all(resultPromises);

        // Ergebnisse in eine Map umwandeln
        const resultsMap: MatchResultsMap = {};
        allResults.forEach(({ id, data }) => {
          resultsMap[id] = data;
        });

        setResults(resultsMap);
      } catch (err) {
        console.error("Error fetching match results:", err);
        setError(err instanceof Error ? err : new Error("Unknown error"));
      } finally {
        setLoading(false);
      }
    }

    fetchResults();
  }, [gameSessionIds]);

  return { results, loading, error };
}

// Helper-Funktion zum Bestimmen des Gewinnerteams
export function getWinningTeam(
  result: MatchResult,
  teamMap: Record<string, string> = {}
): {
  id: string;
  name: string;
  score: number;
  isWinner: boolean;
}[] {
  const teamAId = result.team_a_id || "";
  const teamBId = result.team_b_id || "";

  const teamA = {
    id: teamAId,
    name: teamMap[teamAId] || (teamAId ? `Team ${teamAId.substring(0, 4)}` : "Unbekannt"),
    score: result.goals_a,
    isWinner: result.goals_a > result.goals_b,
  };

  const teamB = {
    id: teamBId,
    name: teamMap[teamBId] || (teamBId ? `Team ${teamBId.substring(0, 4)}` : "Unbekannt"),
    score: result.goals_b,
    isWinner: result.goals_b > result.goals_a,
  };

  return [teamA, teamB];
}

// Helper-Funktion zum Bestimmen des Gesamtsiegers einer Spielsession
export function getOverallWinner(
  sessionResults: MatchResult[],
  teamMap: Record<string, string> = {}
): {
  teamId: string;
  teamName: string;
  wins: number;
  totalGoals: number;
  goalDifference: number;
  isTie?: boolean;
} | null {
  if (!sessionResults || sessionResults.length === 0) return null;

  // Teamstatistiken sammeln
  const teamStats: Record<
    string,
    {
      wins: number;
      totalGoals: number;
      goalsAgainst: number;
      name: string;
    }
  > = {};

  // Alle Spiele durchgehen
  sessionResults.forEach((result) => {
    const teams = getWinningTeam(result, teamMap);

    // TeamA Statistiken aktualisieren
    const teamAId = result.team_a_id || "";
    if (!teamStats[teamAId]) {
      teamStats[teamAId] = {
        wins: 0,
        totalGoals: 0,
        goalsAgainst: 0,
        name: teamMap[teamAId] || (teamAId ? `Team ${teamAId.substring(0, 4)}` : "Unbekannt"),
      };
    }
    teamStats[teamAId].totalGoals += result.goals_a;
    teamStats[teamAId].goalsAgainst += result.goals_b;
    if (result.goals_a > result.goals_b) {
      teamStats[teamAId].wins += 1;
    }

    // TeamB Statistiken aktualisieren
    const teamBId = result.team_b_id || "";
    if (!teamStats[teamBId]) {
      teamStats[teamBId] = {
        wins: 0,
        totalGoals: 0,
        goalsAgainst: 0,
        name: teamMap[teamBId] || (teamBId ? `Team ${teamBId.substring(0, 4)}` : "Unbekannt"),
      };
    }
    teamStats[teamBId].totalGoals += result.goals_b;
    teamStats[teamBId].goalsAgainst += result.goals_a;
    if (result.goals_b > result.goals_a) {
      teamStats[teamBId].wins += 1;
    }
  });

  // Team mit den meisten Siegen und bester Tordifferenz finden
  const teamIds = Object.keys(teamStats);
  if (teamIds.length === 0) return null;

  // Sortiere Teams nach Siegen (absteigend) und dann nach Tordifferenz
  const sortedTeams = teamIds
    .map((id) => ({
      id,
      ...teamStats[id],
      goalDifference: teamStats[id].totalGoals - teamStats[id].goalsAgainst,
    }))
    .sort((a, b) => {
      // Zuerst nach Siegen sortieren
      if (b.wins !== a.wins) {
        return b.wins - a.wins;
      }
      // Bei gleicher Anzahl Siege nach Tordifferenz sortieren
      return b.goalDifference - a.goalDifference;
    });

  // Prüfe, ob die beiden Teams mit den meisten Siegen gleich viele haben
  // UND die gleiche Tordifferenz
  const topTeams = sortedTeams.filter(
    (team) =>
      team.wins === sortedTeams[0].wins && team.goalDifference === sortedTeams[0].goalDifference
  );

  // Wenn mehr als ein Team die meisten Siege und gleiche Tordifferenz hat, ist es ein Unentschieden
  if (topTeams.length > 1) {
    return {
      teamId: "",
      teamName: "Unentschieden",
      wins: topTeams[0].wins,
      totalGoals: 0,
      goalDifference: topTeams[0].goalDifference,
      isTie: true,
    };
  }

  // Ansonsten ist das erste Team der Gewinner
  const winner = sortedTeams[0];
  return {
    teamId: winner.id,
    teamName: winner.name,
    wins: winner.wins,
    totalGoals: winner.totalGoals,
    goalDifference: winner.goalDifference,
  };
}
