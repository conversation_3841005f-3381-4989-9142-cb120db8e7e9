import { useState, useEffect, useCallback, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Team } from "@/types";
import { generateTeams, balanceTeamsBySwapping } from "@/utils/teamUtils";
import { toast } from "@/components/ui/use-toast";

// Add a cache to persist generated teams between component renders
const teamsCache = new Map<string, { teams: Team[]; pendingApproval: boolean }>();

// Helper to debounce state updates
const useDebouncedState = <T>(initialValue: T, delay = 100): [T, (value: T) => void] => {
  const [state, setState] = useState<T>(initialValue);
  const timeoutRef = useRef<number | null>(null);

  const setDebouncedState = useCallback(
    (value: T) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = window.setTimeout(() => {
        setState(value);
        timeoutRef.current = null;
      }, delay);
    },
    [delay]
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, setDebouncedState];
};

export function useTeamEditor(gameSessionId: string | undefined, confirmedPlayers: any[]) {
  // Use debounced state for values that trigger UI updates
  const [generatedTeams, setGeneratedTeams] = useDebouncedState<Team[]>([], 50);
  const [isPendingApproval, setIsPendingApproval] = useState(false);
  const [isGeneratingTeams, setIsGeneratingTeams] = useState(false);
  const [hasPersistedTeams, setHasPersistedTeams] = useState(false);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);

  // Add refs to prevent excessive re-renders
  const hasInitialized = useRef(false);
  const hasRestoredFromCache = useRef(false);
  const previousSessionId = useRef<string | undefined>(undefined);
  const isApprovingTeams = useRef(false);

  // Initialize state from cache if available - only once per session ID
  useEffect(() => {
    if (
      !gameSessionId ||
      hasRestoredFromCache.current ||
      gameSessionId === previousSessionId.current
    ) {
      return;
    }

    if (teamsCache.has(gameSessionId)) {
      const cachedData = teamsCache.get(gameSessionId);
      if (cachedData) {
        console.log(
          `Restoring ${cachedData.teams.length} teams from cache for session ${gameSessionId}`
        );
        setGeneratedTeams(cachedData.teams);
        setIsPendingApproval(cachedData.pendingApproval);
        hasRestoredFromCache.current = true;
        previousSessionId.current = gameSessionId;
      }
    }
  }, [gameSessionId, setGeneratedTeams]);

  // Reset hasRestoredFromCache when session ID changes
  useEffect(() => {
    if (gameSessionId !== previousSessionId.current) {
      hasRestoredFromCache.current = false;
      previousSessionId.current = gameSessionId;
    }
  }, [gameSessionId]);

  // Fetch existing teams to check if they're already persisted
  const fetchExistingTeams = useCallback(async () => {
    if (!gameSessionId || isApprovingTeams.current) return;

    try {
      setIsLoadingTeams(true);

      const { data: teamsData, error: teamsError } = await supabase
        .from("teams")
        .select(
          `
          id,
          name,
          average_rating,
          game_session_id,
          team_players(
            player_id,
            player:players(id, name, jersey_number, rating)
          )
        `
        )
        .eq("game_session_id", gameSessionId);

      if (teamsError) throw teamsError;

      if (teamsData && teamsData.length > 0) {
        setHasPersistedTeams(true);

        // Map the fetched teams to our Team structure
        const mappedTeams: Team[] = teamsData.map((team) => ({
          id: team.id,
          name: team.name || `Team ${team.id}`,
          averageRating: team.average_rating || 0,
          players: team.team_players
            .map((tp: any) =>
              tp.player
                ? {
                    id: tp.player.id,
                    name: tp.player.name,
                    jerseyNumber: tp.player.jersey_number,
                    rating: tp.player.rating || 0,
                    status: "in" as const,
                  }
                : null
            )
            .filter(Boolean),
        }));

        // Only set generated teams if we don't already have cached teams and there are no existing teams in state
        const hasCachedTeams = teamsCache.has(gameSessionId);
        if (generatedTeams.length === 0 && !hasCachedTeams) {
          setGeneratedTeams(mappedTeams);
          setIsPendingApproval(false); // Teams are already saved
        }
      } else {
        setHasPersistedTeams(false);
      }
    } catch (error) {
      console.error("Error fetching existing teams:", error);
    } finally {
      setIsLoadingTeams(false);
    }
  }, [gameSessionId, generatedTeams.length, setGeneratedTeams]);

  // Generate teams based on player data
  const generateTeamsFromPlayers = useCallback(async () => {
    if (!confirmedPlayers || confirmedPlayers.length < 10 || !gameSessionId) {
      return [];
    }

    setIsGeneratingTeams(true);
    try {
      let teamCount = 2;
      const nPlayers = confirmedPlayers.length;

      if (nPlayers >= 20) {
        teamCount = 4;
      } else if (nPlayers >= 15) {
        teamCount = 3;
      }

      // Generate teams using the utility function
      const teams = generateTeams(confirmedPlayers, teamCount);
      // Apply additional balancing
      const balancedTeams = balanceTeamsBySwapping(teams);

      // Store in cache for persistence
      teamsCache.set(gameSessionId, {
        teams: balancedTeams,
        pendingApproval: true,
      });

      console.log(`Cached ${balancedTeams.length} teams for session ${gameSessionId}`);

      // Update state after caching to reduce re-renders
      setGeneratedTeams(balancedTeams);
      setIsPendingApproval(true);

      return balancedTeams;
    } catch (error) {
      console.error("Error generating teams:", error);
      toast({
        title: "Fehler",
        description: "Es gab ein Problem bei der Generierung der Teams",
        variant: "destructive",
      });
      return [];
    } finally {
      setIsGeneratingTeams(false);
    }
  }, [confirmedPlayers, gameSessionId, setGeneratedTeams]);

  // Approve and persist teams to the database
  const approveTeams = useCallback(
    async (teamsToApprove: Team[]) => {
      if (!gameSessionId || !teamsToApprove.length) {
        console.error("Cannot approve teams: gameSessionId is missing or teams array is empty", {
          hasGameSessionId: !!gameSessionId,
          teamCount: teamsToApprove.length,
        });
        return;
      }

      console.log(
        `Starting team approval process for ${teamsToApprove.length} teams in session ${gameSessionId}`
      );

      // Set loading state at the beginning to show the spinner
      setIsLoadingTeams(true);

      // Set flag to prevent concurrent operations
      isApprovingTeams.current = true;

      try {
        // First delete any existing teams for this session
        console.log("Deleting existing team players...");
        const { error: deletePlayersError } = await supabase
          .from("team_players")
          .delete()
          .eq("game_session_id", gameSessionId);

        if (deletePlayersError) {
          console.error("Error deleting team players:", deletePlayersError);
          throw deletePlayersError;
        }

        console.log("Deleting existing teams...");
        const { error: deleteTeamsError } = await supabase
          .from("teams")
          .delete()
          .eq("game_session_id", gameSessionId);

        if (deleteTeamsError) {
          console.error("Error deleting teams:", deleteTeamsError);
          throw deleteTeamsError;
        }

        console.log(`Inserting ${teamsToApprove.length} new teams...`);
        // Then insert new teams
        for (let i = 0; i < teamsToApprove.length; i++) {
          const team = teamsToApprove[i];
          console.log(`Processing team ${i + 1}: ${team.name} with ${team.players.length} players`);

          // Insert the team
          const { data: teamData, error: teamError } = await supabase
            .from("teams")
            .insert({
              name: team.name,
              game_session_id: gameSessionId,
              average_rating: team.averageRating,
            })
            .select();

          if (teamError) {
            console.error(`Error inserting team ${i + 1}:`, teamError);
            throw teamError;
          }

          if (teamData && teamData.length > 0) {
            const teamId = teamData[0].id;
            console.log(
              `Team ${i + 1} inserted with ID ${teamId}, now inserting ${team.players.length} players`
            );

            // Insert the team_players
            const teamPlayersInserts = team.players.map((player) => ({
              team_id: teamId,
              player_id: player.id,
              game_session_id: gameSessionId,
            }));

            const { error: playersError } = await supabase
              .from("team_players")
              .insert(teamPlayersInserts);

            if (playersError) {
              console.error(`Error inserting players for team ${i + 1}:`, playersError);
              throw playersError;
            }
            console.log(`Successfully inserted ${team.players.length} players for team ${i + 1}`);
          } else {
            console.error(`Team ${i + 1} was inserted but no data returned`);
          }
        }

        console.log("Updating game session to mark teams as generated...");
        // Update game session to mark teams as generated
        const { error: updateSessionError } = await supabase
          .from("game_sessions")
          .update({ is_team_generated: true })
          .eq("id", gameSessionId);

        if (updateSessionError) {
          console.error("Error updating game session:", updateSessionError);
          throw updateSessionError;
        }

        console.log("Teams successfully approved and persisted!");

        // First clear cache to prevent flash of cached teams
        teamsCache.delete(gameSessionId);

        // Batch state updates to avoid multiple renders
        const batchUpdate = () => {
          setIsPendingApproval(false);
          setHasPersistedTeams(true);
          hasRestoredFromCache.current = false;
        };
        batchUpdate();

        toast({
          title: "Teams gespeichert",
          description: `${teamsToApprove.length} Teams wurden erfolgreich gespeichert.`,
          variant: "default",
        });

        return true;
      } catch (error) {
        console.error("Error approving teams:", error);
        toast({
          title: "Fehler beim Speichern",
          description: "Die Teams konnten nicht gespeichert werden. Bitte versuche es erneut.",
          variant: "destructive",
        });
        throw error;
      } finally {
        // Use setTimeout to ensure this happens after the React state updates
        setTimeout(() => {
          setIsLoadingTeams(false);
          isApprovingTeams.current = false;
        }, 500);
      }
    },
    [gameSessionId]
  );

  // Only check for existing teams if we have a gameSessionId and haven't initialized yet
  useEffect(() => {
    if (gameSessionId && !hasInitialized.current) {
      hasInitialized.current = true;
      fetchExistingTeams();
    }

    // Reset state if gameSessionId changes to null/undefined, but don't clear cache
    if (!gameSessionId) {
      setGeneratedTeams([]);
      setIsPendingApproval(false);
      setHasPersistedTeams(false);
      hasInitialized.current = false;
      hasRestoredFromCache.current = false;
    }

    // Set up real-time subscription only if we have a gameSessionId
    if (gameSessionId) {
      const teamsChannel = supabase
        .channel(`teams_${gameSessionId}`)
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "teams",
            filter: `game_session_id=eq.${gameSessionId}`,
          },
          () => {
            // Only fetch if we're not in the middle of an approval
            if (!isApprovingTeams.current) {
              fetchExistingTeams();
            }
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(teamsChannel);
      };
    }
  }, [gameSessionId, fetchExistingTeams, setGeneratedTeams]);

  return {
    generatedTeams,
    isPendingApproval,
    isGeneratingTeams,
    hasPersistedTeams,
    isLoadingTeams,
    generateTeamsFromPlayers,
    approveTeams,
    setGeneratedTeams,
    setIsPendingApproval,
  };
}
