import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Team } from "@/types";
import {
  Tournament,
  TournamentConfig,
  TournamentMatch,
  TournamentStanding,
} from "@/types/tournament";
import { toast } from "@/components/ui/use-toast";
import {
  saveTournament,
  getTournamentByGameSessionId,
  updateTournamentMatchResult,
} from "@/services/tournamentDbService";
import { syncTournamentResultsToMatchResults } from "@/services/tournamentResultsService";

interface UseTournamentProps {
  gameSessionId?: string;
}

export function useTournament({ gameSessionId }: UseTournamentProps) {
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [loading, setLoading] = useState(true);
  const [teams, setTeams] = useState<Team[]>([]);
  const [teamsLoading, setTeamsLoading] = useState(true);

  // Fetch teams for the current game session
  const fetchTeams = useCallback(async () => {
    if (!gameSessionId) return;

    setTeamsLoading(true);
    try {
      const { data, error } = await supabase
        .from("teams")
        .select(
          `
          id,
          name,
          average_rating,
          team_players (
            player_id,
            player:players (
              id,
              name,
              jersey_number,
              rating,
              role
            )
          )
        `
        )
        .eq("game_session_id", gameSessionId);

      if (error) {
        console.error("Error fetching teams:", error);
        toast({
          title: "Error",
          description: "Failed to load teams",
          variant: "destructive",
        });
        return;
      }

      if (data) {
        const formattedTeams: Team[] = data.map((team) => ({
          id: team.id,
          name: team.name,
          averageRating: team.average_rating || 0,
          players:
            team.team_players && Array.isArray(team.team_players)
              ? team.team_players
                  .map((tp: any) =>
                    tp && tp.player
                      ? {
                          id: tp.player.id,
                          name: tp.player.name,
                          jerseyNumber: tp.player.jersey_number,
                          rating: tp.player.rating || 0,
                          status: "in" as const,
                          role: tp.player.role,
                          is_active: true,
                        }
                      : null
                  )
                  .filter(Boolean)
              : [],
        }));

        setTeams(formattedTeams);
      } else {
        // If no data is returned, set an empty array
        setTeams([]);
      }
    } catch (error) {
      console.error("Error in fetchTeams:", error);
      // Set empty teams array on error to avoid null issues
      setTeams([]);
    } finally {
      setTeamsLoading(false);
    }
  }, [gameSessionId, toast]);

  // Generate tournament schedule based on teams
  const generateTournamentSchedule = useCallback(
    (teams: Team[], config: TournamentConfig): TournamentMatch[] => {
      if (teams.length < 3) return [];

      // Calculate maximum possible matches based on time constraints
      const matchCycleDuration = config.matchDurationMinutes + config.breakBetweenMatchesMinutes;
      // Total time available for matches (including breaks between matches)
      // We add one break duration because the last match doesn't need a break after it
      const possibleMatchCycles = Math.floor(
        (config.totalDurationMinutes + config.breakBetweenMatchesMinutes) / matchCycleDuration
      );
      // Multiply by the number of fields to get the total possible matches
      const maxPossibleMatches = possibleMatchCycles * config.numberOfFields;

      console.log("Match calculation:", {
        totalDuration: config.totalDurationMinutes,
        matchDuration: config.matchDurationMinutes,
        breakDuration: config.breakBetweenMatchesMinutes,
        matchCycleDuration,
        possibleMatchCycles,
        numberOfFields: config.numberOfFields,
        maxPossibleMatches,
      });

      // Generate all possible match combinations
      const allPossibleMatches: { teamA: Team; teamB: Team }[] = [];
      for (let i = 0; i < teams.length; i++) {
        for (let j = i + 1; j < teams.length; j++) {
          allPossibleMatches.push({
            teamA: teams[i],
            teamB: teams[j],
          });
        }
      }

      // Create a map to track how many matches each team has
      const teamMatchCounts = new Map<string, number>();
      teams.forEach((team) => teamMatchCounts.set(team.id!, 0));

      // Organize matches into time slots to ensure no team plays in multiple matches at the same time
      const timeSlots: { teamA: Team; teamB: Team; field: number }[][] = [];

      // Initialize time slots based on the number of possible match cycles
      for (let i = 0; i < possibleMatchCycles; i++) {
        timeSlots.push([]);
      }

      // Sort all possible matches by the sum of match counts for both teams (ascending)
      allPossibleMatches.sort((a, b) => {
        const aCount =
          (teamMatchCounts.get(a.teamA.id!) || 0) + (teamMatchCounts.get(a.teamB.id!) || 0);
        const bCount =
          (teamMatchCounts.get(b.teamA.id!) || 0) + (teamMatchCounts.get(b.teamB.id!) || 0);
        return aCount - bCount;
      });

      // Distribute matches across time slots and fields
      let matchesScheduled = 0;
      let timeSlotIndex = 0;

      while (matchesScheduled < maxPossibleMatches && allPossibleMatches.length > 0) {
        const currentTimeSlot = timeSlots[timeSlotIndex];

        // Find teams already playing in this time slot
        const teamsInTimeSlot = new Set<string>();
        currentTimeSlot.forEach((match) => {
          teamsInTimeSlot.add(match.teamA.id!);
          teamsInTimeSlot.add(match.teamB.id!);
        });

        // Find the next match where neither team is already playing in this time slot
        let matchIndex = -1;
        for (let i = 0; i < allPossibleMatches.length; i++) {
          const match = allPossibleMatches[i];
          if (!teamsInTimeSlot.has(match.teamA.id!) && !teamsInTimeSlot.has(match.teamB.id!)) {
            matchIndex = i;
            break;
          }
        }

        if (matchIndex !== -1) {
          // We found a match where neither team is already playing
          const match = allPossibleMatches.splice(matchIndex, 1)[0];

          // Assign to the next available field in this time slot
          const fieldNumber = currentTimeSlot.length + 1;
          if (fieldNumber <= config.numberOfFields) {
            currentTimeSlot.push({
              ...match,
              field: fieldNumber,
            });

            // Update match counts for these teams
            teamMatchCounts.set(match.teamA.id!, (teamMatchCounts.get(match.teamA.id!) || 0) + 1);
            teamMatchCounts.set(match.teamB.id!, (teamMatchCounts.get(match.teamB.id!) || 0) + 1);

            matchesScheduled++;
          } else {
            // No more fields available in this time slot, move to the next one
            timeSlotIndex = (timeSlotIndex + 1) % timeSlots.length;
          }
        } else {
          // No suitable match found for this time slot, move to the next one
          timeSlotIndex = (timeSlotIndex + 1) % timeSlots.length;

          // If we've checked all time slots and couldn't find a match, break to avoid infinite loop
          if (timeSlotIndex === 0) {
            // Re-sort remaining matches to try to fit more
            allPossibleMatches.sort((a, b) => {
              const aCount =
                (teamMatchCounts.get(a.teamA.id!) || 0) + (teamMatchCounts.get(a.teamB.id!) || 0);
              const bCount =
                (teamMatchCounts.get(b.teamA.id!) || 0) + (teamMatchCounts.get(b.teamB.id!) || 0);
              return aCount - bCount;
            });

            // If we still can't find a match after re-sorting, we're done
            let foundMatch = false;
            for (let slotIdx = 0; slotIdx < timeSlots.length; slotIdx++) {
              const slot = timeSlots[slotIdx];
              const teamsInSlot = new Set<string>();
              slot.forEach((match) => {
                teamsInSlot.add(match.teamA.id!);
                teamsInSlot.add(match.teamB.id!);
              });

              for (let i = 0; i < allPossibleMatches.length; i++) {
                const match = allPossibleMatches[i];
                if (!teamsInSlot.has(match.teamA.id!) && !teamsInSlot.has(match.teamB.id!)) {
                  foundMatch = true;
                  break;
                }
              }

              if (foundMatch) break;
            }

            if (!foundMatch) break;
          }
        }
      }

      // Convert time slots to TournamentMatch objects
      const matches: TournamentMatch[] = [];
      let matchNumber = 1;

      // Flatten the time slots into a list of matches
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        const slot = timeSlots[slotIndex];

        // Sort by field number to ensure consistent ordering
        slot.sort((a, b) => a.field - b.field);

        for (let i = 0; i < slot.length; i++) {
          const match = slot[i];
          matches.push({
            id: `match-${matchNumber}`,
            teamA: match.teamA,
            teamB: match.teamB,
            goalsA: null,
            goalsB: null,
            isCompleted: false,
            matchNumber: matchNumber,
            field: `Feld ${match.field}`,
            timeSlot: slotIndex + 1, // Store the time slot for grouping matches
          });
          matchNumber++;
        }
      }

      return matches;
    },
    []
  );

  // Calculate tournament standings
  const calculateStandings = useCallback(
    (matches: TournamentMatch[], teams: Team[]): TournamentStanding[] => {
      const standings: Record<string, TournamentStanding> = {};

      // Initialize standings for all teams
      teams.forEach((team) => {
        if (team && team.id) {
          standings[team.id] = {
            team,
            played: 0,
            won: 0,
            drawn: 0,
            lost: 0,
            goalsFor: 0,
            goalsAgainst: 0,
            goalDifference: 0,
            points: 0,
          };
        }
      });

      // Update standings based on completed matches
      matches
        .filter((match) => match.isCompleted)
        .forEach((match) => {
          // Ensure both teams exist and have IDs
          if (!match.teamA?.id || !match.teamB?.id) return;

          const teamAId = match.teamA.id;
          const teamBId = match.teamB.id;

          // Ensure both teams exist in standings
          if (!standings[teamAId] || !standings[teamBId]) return;

          if (match.goalsA === null || match.goalsB === null) return;

          // Update team A stats
          standings[teamAId].played += 1;
          standings[teamAId].goalsFor += match.goalsA;
          standings[teamAId].goalsAgainst += match.goalsB;

          // Update team B stats
          standings[teamBId].played += 1;
          standings[teamBId].goalsFor += match.goalsB;
          standings[teamBId].goalsAgainst += match.goalsA;

          // Determine match result
          if (match.goalsA > match.goalsB) {
            // Team A won
            standings[teamAId].won += 1;
            standings[teamAId].points += 3;
            standings[teamBId].lost += 1;
          } else if (match.goalsA < match.goalsB) {
            // Team B won
            standings[teamBId].won += 1;
            standings[teamBId].points += 3;
            standings[teamAId].lost += 1;
          } else {
            // Draw
            standings[teamAId].drawn += 1;
            standings[teamAId].points += 1;
            standings[teamBId].drawn += 1;
            standings[teamBId].points += 1;
          }
        });

      // Calculate goal differences
      Object.values(standings).forEach((team) => {
        team.goalDifference = team.goalsFor - team.goalsAgainst;
      });

      // Sort standings by points, then goal difference, then goals scored
      return Object.values(standings).sort((a, b) => {
        if (a.points !== b.points) return b.points - a.points;
        if (a.goalDifference !== b.goalDifference) return b.goalDifference - a.goalDifference;
        return b.goalsFor - a.goalsFor;
      });
    },
    []
  );

  // Create a new tournament
  const createTournament = useCallback(
    async (config: TournamentConfig) => {
      if (!gameSessionId || teams.length < 3) {
        toast({
          title: "Error",
          description: "Cannot create tournament: Need at least 3 teams",
          variant: "destructive",
        });
        return null;
      }

      try {
        const matches = generateTournamentSchedule(teams, config);
        const standings = calculateStandings(matches, teams);

        const newTournament: Tournament = {
          gameSessionId,
          config,
          matches,
          standings,
          currentMatchIndex: 0,
          isActive: true,
          startTime: new Date(),
        };

        // Save the tournament to the database
        const savedTournament = await saveTournament(newTournament);

        if (savedTournament) {
          // Use the saved tournament with database IDs
          setTournament(savedTournament);

          // Return the saved tournament with database IDs
          return savedTournament;
        } else {
          // If saving failed, still use the in-memory tournament
          setTournament(newTournament);

          // Return the in-memory tournament
          return newTournament;
        }
      } catch (error) {
        console.error("Error creating tournament:", error);
        toast({
          title: "Error",
          description: "Failed to create tournament",
          variant: "destructive",
        });
        return null;
      }
    },
    [gameSessionId, teams, generateTournamentSchedule, calculateStandings, toast]
  );

  // Update match result
  const updateMatchResult = useCallback(
    async (matchId: string, goalsA: number, goalsB: number) => {
      if (!tournament) return;

      // Find the match to update
      const matchToUpdate = tournament.matches.find((m) => m.id === matchId);
      if (!matchToUpdate) {
        console.error("Match not found:", matchId);
        return;
      }

      // Update the tournament in memory first for immediate UI feedback
      setTournament((prev) => {
        if (!prev) return null;

        const updatedMatches = prev.matches.map((match) => {
          if (match.id === matchId) {
            return {
              ...match,
              goalsA,
              goalsB,
              isCompleted: true,
              endTime: new Date(),
            };
          }
          return match;
        });

        // Make sure we have valid teams before calculating standings
        const validTeams = teams.filter((team) => team && team.id);
        const updatedStandings = calculateStandings(updatedMatches, validTeams);

        return {
          ...prev,
          matches: updatedMatches,
          standings: updatedStandings,
        };
      });

      // If the tournament has a database ID, update the match result in the database
      if (tournament.id) {
        try {
          // Update the match result in the database
          await updateTournamentMatchResult(matchId, goalsA, goalsB);

          // No automatic syncing - this will only happen when the user clicks "Aus Turnier importieren"
        } catch (error) {
          console.error("Error updating match result in database:", error);
          // We don't show an error toast here because the UI is already updated
          // and we don't want to confuse the user
        }
      }
    },
    [tournament, calculateStandings, teams]
  );

  // Start the tournament
  const startTournament = useCallback(async () => {
    if (!tournament) return;

    // Update in memory first for immediate UI feedback
    setTournament((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        isActive: true,
        startTime: new Date(),
      };
    });

    // If the tournament has a database ID, update it in the database
    if (tournament.id) {
      try {
        const { error } = await supabase
          .from("tournaments")
          .update({
            is_active: true,
            start_time: new Date().toISOString(),
          })
          .eq("id", tournament.id);

        if (error) {
          console.error("Error updating tournament status in database:", error);
        }
      } catch (error) {
        console.error("Error starting tournament in database:", error);
      }
    }
  }, [tournament]);

  // End the tournament
  const endTournament = useCallback(async () => {
    if (!tournament) return;

    // Update in memory first for immediate UI feedback
    setTournament((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        isActive: false,
        endTime: new Date(),
      };
    });

    // If the tournament has a database ID, update it in the database
    if (tournament.id) {
      try {
        const { error } = await supabase
          .from("tournaments")
          .update({
            is_active: false,
            end_time: new Date().toISOString(),
          })
          .eq("id", tournament.id);

        if (error) {
          console.error("Error updating tournament status in database:", error);
        }
      } catch (error) {
        console.error("Error ending tournament in database:", error);
      }
    }
  }, [tournament]);

  // Start a specific match
  const startMatch = useCallback(
    async (matchId: string) => {
      if (!tournament) return;

      const startTime = new Date();

      // Update in memory first for immediate UI feedback
      setTournament((prev) => {
        if (!prev) return null;

        const matchIndex = prev.matches.findIndex((m) => m.id === matchId);
        if (matchIndex === -1) return prev;

        const updatedMatches = prev.matches.map((match, index) => {
          if (index === matchIndex) {
            return {
              ...match,
              startTime,
            };
          }
          return match;
        });

        return {
          ...prev,
          matches: updatedMatches,
          currentMatchIndex: matchIndex,
        };
      });

      // If the tournament has a database ID, update the match in the database
      if (tournament.id && !matchId.startsWith("match-")) {
        try {
          const { error } = await supabase
            .from("tournament_matches")
            .update({
              start_time: startTime.toISOString(),
            })
            .eq("id", matchId);

          if (error) {
            console.error("Error updating match start time in database:", error);
          }
        } catch (error) {
          console.error("Error starting match in database:", error);
        }
      }
    },
    [tournament]
  );

  // Start multiple matches simultaneously
  const startMultipleMatches = useCallback(
    async (matchIds: string[]) => {
      if (!tournament || matchIds.length === 0) return;

      const startTime = new Date();

      // Update in memory first for immediate UI feedback
      setTournament((prev) => {
        if (!prev) return null;

        const updatedMatches = prev.matches.map((match) => {
          if (matchIds.includes(match.id)) {
            return {
              ...match,
              startTime,
            };
          }
          return match;
        });

        // Set the current match index to the first match in the list
        const firstMatchIndex = prev.matches.findIndex((m) => m.id === matchIds[0]);

        return {
          ...prev,
          matches: updatedMatches,
          currentMatchIndex: firstMatchIndex >= 0 ? firstMatchIndex : prev.currentMatchIndex,
        };
      });

      // If the tournament has a database ID, update the matches in the database
      if (tournament.id) {
        try {
          // Filter out temporary match IDs
          const dbMatchIds = matchIds.filter((id) => !id.startsWith("match-"));

          if (dbMatchIds.length > 0) {
            const { error } = await supabase
              .from("tournament_matches")
              .update({
                start_time: startTime.toISOString(),
              })
              .in("id", dbMatchIds);

            if (error) {
              console.error("Error updating match start times in database:", error);
            }
          }
        } catch (error) {
          console.error("Error starting matches in database:", error);
        }
      }

      return matchIds[0]; // Return the ID of the first match for navigation purposes
    },
    [tournament]
  );

  // Fetch tournament from database
  const fetchTournament = useCallback(async () => {
    if (!gameSessionId) return;

    setLoading(true);
    try {
      const tournamentData = await getTournamentByGameSessionId(gameSessionId);
      if (tournamentData) {
        console.log("Loaded tournament from database:", tournamentData);
        setTournament(tournamentData);
      }
    } catch (error) {
      console.error("Error fetching tournament:", error);
    } finally {
      setLoading(false);
    }
  }, [gameSessionId]);

  // Load teams and tournament when gameSessionId changes
  useEffect(() => {
    if (gameSessionId) {
      fetchTeams();
      fetchTournament();
    } else {
      // If there's no gameSessionId, we're not loading anymore
      setLoading(false);
      setTeamsLoading(false);
    }
  }, [gameSessionId, fetchTeams, fetchTournament]);

  // Add next match
  const addNextMatch = useCallback(async () => {
    if (!tournament || !teams || teams.length < 2) return null;

    // Calculate maximum possible matches based on time constraints
    const matchCycleDuration =
      tournament.config.matchDurationMinutes + tournament.config.breakBetweenMatchesMinutes;
    const possibleMatchCycles = Math.floor(
      (tournament.config.totalDurationMinutes + tournament.config.breakBetweenMatchesMinutes) /
        matchCycleDuration
    );
    const maxPossibleMatches = possibleMatchCycles * tournament.config.numberOfFields;

    // Check if we've reached the maximum number of matches based on time constraints
    if (tournament.matches.length >= maxPossibleMatches) {
      toast({
        title: "Keine weiteren Spiele möglich",
        description: `Die maximale Anzahl von ${maxPossibleMatches} Spielen für die gegebene Zeitdauer wurde erreicht.`,
        variant: "destructive",
      });
      return null;
    }

    // Get all possible team combinations (including those that have already played)
    const allTeamCombinations: { teamA: Team; teamB: Team }[] = [];
    for (let i = 0; i < teams.length; i++) {
      for (let j = i + 1; j < teams.length; j++) {
        if (teams[i] && teams[j] && teams[i].id && teams[j].id) {
          allTeamCombinations.push({
            teamA: teams[i],
            teamB: teams[j],
          });
        }
      }
    }

    // If no valid team combinations are available, return
    if (allTeamCombinations.length === 0) {
      toast({
        title: "Keine weiteren Spiele möglich",
        description: "Es konnten keine gültigen Team-Kombinationen gefunden werden.",
        variant: "destructive",
      });
      return null;
    }

    // Create a map to track how many times each team pair has played
    const pairMatchCounts = new Map<string, number>();

    // Create a map to track how many matches each team has played
    const teamMatchCounts = new Map<string, number>();
    teams.forEach((team) => {
      if (team && team.id) {
        const matchCount = tournament.matches.filter(
          (m) => m.teamA?.id === team.id || m.teamB?.id === team.id
        ).length;
        teamMatchCounts.set(team.id, matchCount);
      }
    });

    // Count how many times each pair has played
    tournament.matches.forEach((match) => {
      if (match.teamA?.id && match.teamB?.id) {
        const pairKey1 = `${match.teamA.id}-${match.teamB.id}`;
        const pairKey2 = `${match.teamB.id}-${match.teamA.id}`;

        pairMatchCounts.set(pairKey1, (pairMatchCounts.get(pairKey1) || 0) + 1);
        pairMatchCounts.set(pairKey2, (pairMatchCounts.get(pairKey2) || 0) + 1);
      }
    });

    // Sort team combinations by:
    // 1. How many times they've played against each other (fewer is better)
    // 2. Total number of matches both teams have played (fewer is better)
    allTeamCombinations.sort((a, b) => {
      if (a.teamA?.id && a.teamB?.id && b.teamA?.id && b.teamB?.id) {
        const pairKeyA = `${a.teamA.id}-${a.teamB.id}`;
        const pairKeyB = `${b.teamA.id}-${b.teamB.id}`;

        const pairCountA = pairMatchCounts.get(pairKeyA) || 0;
        const pairCountB = pairMatchCounts.get(pairKeyB) || 0;

        // First sort by how many times these teams have played each other
        if (pairCountA !== pairCountB) {
          return pairCountA - pairCountB;
        }

        // Then sort by total number of matches both teams have played
        const aCount =
          (teamMatchCounts.get(a.teamA.id) || 0) + (teamMatchCounts.get(a.teamB.id) || 0);
        const bCount =
          (teamMatchCounts.get(b.teamA.id) || 0) + (teamMatchCounts.get(b.teamB.id) || 0);
        return aCount - bCount;
      }
      return 0;
    });

    // Take the first match (teams with fewest matches against each other)
    const nextMatch = allTeamCombinations[0];
    if (!nextMatch) {
      toast({
        title: "Keine weiteren Spiele möglich",
        description: "Es konnten keine gültigen Team-Kombinationen gefunden werden.",
        variant: "destructive",
      });
      return null;
    }

    // Determine the time slot (round) for the new match
    // Find the highest time slot currently in use
    const highestTimeSlot = tournament.matches.reduce(
      (max, match) => (match.timeSlot && match.timeSlot > max ? match.timeSlot : max),
      0
    );

    // Count matches in the highest time slot
    const matchesInHighestTimeSlot = tournament.matches.filter(
      (match) => match.timeSlot === highestTimeSlot
    ).length;

    // If we've reached the number of fields, create a new time slot
    const timeSlot =
      matchesInHighestTimeSlot >= tournament.config.numberOfFields
        ? highestTimeSlot + 1
        : highestTimeSlot || 1;

    // Create the new match
    const newMatch: TournamentMatch = {
      id: `match-${tournament.matches.length + 1}`,
      teamA: nextMatch.teamA,
      teamB: nextMatch.teamB,
      goalsA: null,
      goalsB: null,
      isCompleted: false,
      matchNumber: tournament.matches.length + 1,
      field: `Feld ${(matchesInHighestTimeSlot % tournament.config.numberOfFields) + 1}`,
      timeSlot: timeSlot,
    };

    // Add the match to the tournament in memory
    setTournament((prev) => {
      if (!prev) return null;

      const updatedMatches = [...prev.matches, newMatch];

      return {
        ...prev,
        matches: updatedMatches,
      };
    });

    // If the tournament has a database ID, add the match to the database
    if (tournament.id) {
      try {
        // Store timeSlot in the field metadata as "Feld X (Spielrunde Y)"
        const fieldWithTimeSlot = newMatch.timeSlot
          ? `${newMatch.field} (Spielrunde ${newMatch.timeSlot})`
          : newMatch.field;

        const { data, error } = await supabase
          .from("tournament_matches")
          .insert({
            tournament_id: tournament.id,
            team_a_id: newMatch.teamA.id,
            team_b_id: newMatch.teamB.id,
            goals_a: newMatch.goalsA,
            goals_b: newMatch.goalsB,
            is_completed: newMatch.isCompleted,
            match_number: newMatch.matchNumber,
            field: fieldWithTimeSlot,
            start_time: newMatch.startTime,
            end_time: newMatch.endTime,
          })
          .select()
          .single();

        if (error) {
          console.error("Error adding match to database:", error);
        } else if (data) {
          // Update the match ID with the database ID
          newMatch.id = data.id;
        }
      } catch (error) {
        console.error("Error adding match to database:", error);
      }
    }

    toast({
      title: "Neues Spiel hinzugefügt",
      description: `Spiel ${newMatch.teamA.name} vs ${newMatch.teamB.name} wurde hinzugefügt.`,
    });

    return newMatch;
  }, [tournament, teams, toast]);

  // Delete a match
  const deleteMatch = useCallback(
    async (matchId: string) => {
      if (!tournament) return;

      // Check if the match is already completed
      const matchToDelete = tournament.matches.find((m) => m.id === matchId);
      if (!matchToDelete) {
        toast({
          title: "Fehler",
          description: "Spiel konnte nicht gefunden werden.",
          variant: "destructive",
        });
        return;
      }

      if (matchToDelete.isCompleted) {
        toast({
          title: "Fehler",
          description: "Bereits gespielte Partien können nicht gelöscht werden.",
          variant: "destructive",
        });
        return;
      }

      // If the match has a database ID (not a temporary ID like "match-1"), delete it from the database
      if (tournament.id && !matchId.startsWith("match-")) {
        try {
          const { error } = await supabase.from("tournament_matches").delete().eq("id", matchId);

          if (error) {
            console.error("Error deleting match from database:", error);
            toast({
              title: "Fehler",
              description: "Die Partie konnte nicht aus der Datenbank gelöscht werden.",
              variant: "destructive",
            });
            return;
          }
        } catch (error) {
          console.error("Error deleting match from database:", error);
          toast({
            title: "Fehler",
            description: "Die Partie konnte nicht aus der Datenbank gelöscht werden.",
            variant: "destructive",
          });
          return;
        }
      }

      // Remove the match from the tournament in memory
      setTournament((prev) => {
        if (!prev) return null;

        // Make a copy of the matches array without the deleted match
        const updatedMatches = prev.matches.filter((m) => m.id !== matchId);

        // Renumber the matches but preserve time slots
        const renumberedMatches = updatedMatches.map((match, index) => {
          // Keep the original time slot
          const timeSlot = match.timeSlot;

          // Count how many matches are in this time slot before this one
          const matchesInSameTimeSlotBefore = updatedMatches.filter(
            (m) => m.timeSlot === timeSlot && updatedMatches.indexOf(m) < index
          ).length;

          return {
            ...match,
            matchNumber: index + 1,
            field: `Feld ${(matchesInSameTimeSlotBefore % prev.config.numberOfFields) + 1}`,
          };
        });

        // Calculate new standings based on the updated matches
        const updatedStandings = calculateStandings(renumberedMatches, teams);

        return {
          ...prev,
          matches: renumberedMatches,
          standings: updatedStandings,
          // If the current match is deleted, reset the current match index
          currentMatchIndex:
            prev.currentMatchIndex >= renumberedMatches.length ? -1 : prev.currentMatchIndex,
        };
      });

      // Update match numbers in the database if the tournament has a database ID
      if (tournament.id) {
        try {
          // Get all matches for this tournament
          const { data: matches, error: fetchError } = await supabase
            .from("tournament_matches")
            .select("id, match_number")
            .eq("tournament_id", tournament.id)
            .order("match_number", { ascending: true });

          if (fetchError) {
            console.error("Error fetching matches for renumbering:", fetchError);
            return;
          }

          // We'll handle the database updates differently
          // Instead of trying to update match numbers, we'll fetch the full tournament data
          // and update it with our new in-memory state
          await fetchTournament();
        } catch (error) {
          console.error("Error renumbering matches in database:", error);
        }
      }

      toast({
        title: "Partie gelöscht",
        description: `Die Partie wurde erfolgreich aus dem Spielplan entfernt.`,
      });
    },
    [tournament, toast, calculateStandings, teams, fetchTournament]
  );

  return {
    tournament,
    teams,
    loading,
    teamsLoading,
    createTournament,
    updateMatchResult,
    startTournament,
    endTournament,
    startMatch,
    startMultipleMatches,
    calculateStandings,
    addNextMatch,
    deleteMatch,
  };
}
