import { useState, useEffect } from "react";

interface AuthState {
  isLoggedIn: boolean;
  isAdmin: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isLoggedIn: false,
    isAdmin: false,
  });

  const checkAuth = () => {
    try {
      const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
      const isAdmin = localStorage.getItem("isAdminLoggedIn") === "true";

      setAuthState({
        isLoggedIn,
        isAdmin,
      });
    } catch (error) {
      console.error("Error accessing localStorage:", error);
      setAuthState({
        isLoggedIn: false,
        isAdmin: false,
      });
    }
  };

  useEffect(() => {
    // Check auth status on initial mount
    checkAuth();

    // Also update auth state when localStorage changes in other tabs/windows
    window.addEventListener("storage", checkAuth);

    // Cleanup event listener on unmount
    return () => {
      window.removeEventListener("storage", checkAuth);
    };
  }, []);

  return authState;
};
