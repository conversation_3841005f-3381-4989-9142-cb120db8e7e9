import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Team } from "@/types";
import { toast } from "sonner";
import { useLoadingState } from "./useLoadingState";

type TeamPlayerResponse = {
  team_id: string;
  player_id: string;
  players: {
    id: string;
    name: string;
    jersey_number: number;
    rating: number;
  };
};

export function useTeams(gameSessionId?: string) {
  const [teams, setTeams] = useState<Team[]>([]);
  const { loading, startLoading, stopLoading } = useLoadingState(true);

  const mapTeamPlayers = useCallback((teamPlayersData: TeamPlayerResponse[], teamId: string) => {
    return teamPlayersData
      .filter((tp) => tp.team_id === teamId)
      .map((tp) => ({
        id: tp.players.id,
        name: tp.players.name,
        jerseyNumber: tp.players.jersey_number,
        rating: tp.players.rating,
        status: "in" as const,
      }));
  }, []);

  const fetchTeams = useCallback(async () => {
    if (!gameSessionId) {
      setTeams([]);
      stopLoading();
      return;
    }

    startLoading();
    try {
      const { data: teamsData, error: teamsError } = await supabase
        .from("teams")
        .select("*")
        .eq("game_session_id", gameSessionId);

      if (teamsError) throw new Error(`Failed to fetch teams: ${teamsError.message}`);

      if (!teamsData?.length) {
        setTeams([]);
        stopLoading();
        return;
      }

      const teamIds = teamsData.map((t) => t.id);

      const { data: teamPlayersData, error: playersError } = await supabase
        .from("team_players")
        .select(
          `
          team_id,
          player_id,
          players:player_id (
            id, name, jersey_number, rating
          )
        `
        )
        .in("team_id", teamIds);

      if (playersError) throw new Error(`Failed to fetch team players: ${playersError.message}`);

      const fullTeams: Team[] = teamsData.map((team) => ({
        id: team.id,
        name: team.name,
        averageRating: team.average_rating,
        players: mapTeamPlayers(teamPlayersData || [], team.id),
      }));

      setTeams(fullTeams);
    } catch (error) {
      toast.error(
        `Error fetching teams: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      setTeams([]);
    } finally {
      stopLoading();
    }
  }, [gameSessionId, startLoading, stopLoading]);

  useEffect(() => {
    fetchTeams();

    const channel = supabase
      .channel("teams-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "teams",
        },
        () => {
          fetchTeams();
        }
      )
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "team_players",
        },
        () => {
          fetchTeams();
        }
      )
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "game_sessions",
        },
        () => {
          fetchTeams();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [fetchTeams]);

  return {
    teams,
    loading,
    fetchTeams,
  };
}
