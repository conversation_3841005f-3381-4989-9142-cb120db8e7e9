import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { usePlayerLists } from "../usePlayerLists";
import { usePlayers } from "../usePlayers";
import { useGameSessions } from "../useGameSessions";
import { useTeamSettings } from "../useTeamSettings";
import { Player, GameSession } from "@/types";

// Mock the dependencies
vi.mock("../usePlayers", () => ({
  usePlayers: vi.fn(),
}));

vi.mock("../useGameSessions", () => ({
  useGameSessions: vi.fn(),
}));

vi.mock("../useTeamSettings", () => ({
  useTeamSettings: vi.fn(),
}));

describe("usePlayerLists", () => {
  const mockPlayers: Player[] = [
    {
      id: "1",
      name: "Player 1",
      status: "in",
      signupTimestamp: new Date("2024-01-01"),
      rating: 1,
      is_active: true,
    },
    {
      id: "2",
      name: "Player 2",
      status: "in",
      signupTimestamp: new Date("2024-01-02"),
      rating: 1,
      is_active: true,
    },
    { id: "3", name: "Player 3", status: "out", rating: 1, is_active: true },
    {
      id: "4",
      name: "Player 4",
      status: "pending",
      rating: 1,
      is_active: true,
    },
  ];

  const mockUpdatePlayer = vi.fn().mockResolvedValue(true);
  const mockUpdateGameDuration = vi.fn().mockResolvedValue({ duration_minutes: 90 });

  const mockCurrentSession: GameSession = {
    id: "test-session-id",
    status: "scheduled",
    isSignupOpen: true,
    date: new Date(),
    signupOpensAt: new Date(),
    isTeamGenerated: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(usePlayers).mockReturnValue({
      players: mockPlayers,
      updatePlayer: mockUpdatePlayer,
      loading: false,
      createPlayer: vi.fn(),
      deletePlayer: vi.fn(),
    });

    vi.mocked(useGameSessions).mockReturnValue({
      currentSession: mockCurrentSession,
      gameSessions: [mockCurrentSession],
      pastSessions: [],
      loading: false,
      createGameSession: vi.fn(),
      updateGameSession: vi.fn(),
      updateGameDuration: mockUpdateGameDuration,
      refetchCurrent: vi.fn(),
      refetchPast: vi.fn(),
      refetchAll: vi.fn(),
    });

    vi.mocked(useTeamSettings).mockReturnValue({
      allow3Teams: true,
      duration2Teams: 90,
      duration3Teams: 120,
      duration4Teams: 180,
      loading: false,
      saveSettings: vi.fn(),
      costPerPlayer2Teams: 5,
      costPerPlayer3Teams: 7,
      costPerPlayer4Teams: 9,
      payPalMeLink: "https://paypal.me/test",
    });
  });

  it("should initialize with empty lists when no players", () => {
    vi.mocked(usePlayers).mockReturnValue({
      players: [],
      updatePlayer: mockUpdatePlayer,
      loading: false,
      createPlayer: vi.fn(),
      deletePlayer: vi.fn(),
    });

    const { result } = renderHook(() => usePlayerLists(true));

    expect(result.current.confirmedPlayers).toEqual([]);
    expect(result.current.reservePlayers).toEqual([]);
  });

  it("should properly categorize players based on status", async () => {
    const { result } = renderHook(() => usePlayerLists(true));

    // Should only include 'in' players in confirmed/reserve lists
    expect(result.current.confirmedPlayers.length).toBe(2);
    expect(result.current.reservePlayers.length).toBe(0);
    expect(result.current.confirmedPlayers.every((p) => p.status === "in")).toBe(true);
  });

  it("should handle 3-team setting correctly", async () => {
    // Create enough players to test 3-team scenario
    const manyPlayers: Player[] = Array.from({ length: 16 }, (_, i) => ({
      id: String(i + 1),
      name: `Player ${i + 1}`,
      status: "in",
      signupTimestamp: new Date(`2024-01-${String(i + 1).padStart(2, "0")}`),
      rating: 1,
      is_active: true,
    }));

    vi.mocked(usePlayers).mockReturnValue({
      players: manyPlayers,
      updatePlayer: mockUpdatePlayer,
      loading: false,
      createPlayer: vi.fn(),
      deletePlayer: vi.fn(),
    });

    // Test with 3 teams enabled
    const { result: resultWith3Teams } = renderHook(() => usePlayerLists(true));

    expect(resultWith3Teams.current.confirmedPlayers.length).toBe(15);
    expect(resultWith3Teams.current.reservePlayers.length).toBe(1);

    // Test with 3 teams disabled
    const { result: resultWithout3Teams } = renderHook(() => usePlayerLists(false));

    expect(resultWithout3Teams.current.confirmedPlayers.length).toBe(10);
    expect(resultWithout3Teams.current.reservePlayers.length).toBe(6);
  });

  it("should update player status and maintain local state", async () => {
    const { result } = renderHook(() => usePlayerLists(true));

    // Initial state
    expect(result.current.players.find((p) => p.id === "1")?.status).toBe("in");

    // Update status
    await act(async () => {
      await result.current.updatePlayer("1", { status: "out" });
    });

    // Check local state is updated
    expect(result.current.players.find((p) => p.id === "1")?.status).toBe("out");
    expect(mockUpdatePlayer).toHaveBeenCalledWith("1", { status: "out" });
  });

  it("should sort players by signup timestamp", () => {
    const { result } = renderHook(() => usePlayerLists(true));

    // Players should be sorted by signupTimestamp
    const timestamps = result.current.confirmedPlayers.map((p) => p.signupTimestamp);
    expect(timestamps).toEqual(
      [...timestamps].sort((a, b) => new Date(a!).getTime() - new Date(b!).getTime())
    );
  });

  it("should handle different player counts correctly", async () => {
    const testCases = [
      { count: 9, expectedConfirmed: 9, expectedReserve: 0 },
      { count: 10, expectedConfirmed: 10, expectedReserve: 0 },
      { count: 14, expectedConfirmed: 10, expectedReserve: 4 },
      { count: 19, expectedConfirmed: 15, expectedReserve: 4 }, // With 3 teams
      { count: 20, expectedConfirmed: 20, expectedReserve: 0 },
    ];

    for (const { count, expectedConfirmed, expectedReserve } of testCases) {
      const testPlayers: Player[] = Array.from({ length: count }, (_, i) => ({
        id: String(i + 1),
        name: `Player ${i + 1}`,
        status: "in",
        signupTimestamp: new Date(`2024-01-${String(i + 1).padStart(2, "0")}`),
        rating: 1,
        is_active: true,
      }));

      vi.mocked(usePlayers).mockReturnValue({
        players: testPlayers,
        updatePlayer: mockUpdatePlayer,
        loading: false,
        createPlayer: vi.fn(),
        deletePlayer: vi.fn(),
      });

      const { result } = renderHook(() => usePlayerLists(true));
      expect(result.current.confirmedPlayers.length).toBe(expectedConfirmed);
      expect(result.current.reservePlayers.length).toBe(expectedReserve);
    }
  });

  it("should update game duration when player count changes", async () => {
    const { result } = renderHook(() => usePlayerLists(true));

    expect(mockUpdateGameDuration).toHaveBeenCalledWith(
      mockCurrentSession.id,
      2, // 2 players with status 'in' and is_active true
      {
        allow3Teams: true,
        duration2Teams: 90,
        duration3Teams: 120,
        duration4Teams: 180,
      }
    );

    // Update player status which should trigger duration update
    await act(async () => {
      await result.current.updatePlayer("3", { status: "in" });
    });

    // Verify the second call to updateGameDuration with updated player count
    expect(mockUpdateGameDuration).toHaveBeenCalledWith(
      mockCurrentSession.id,
      3, // Now 3 players with status 'in'
      {
        allow3Teams: true,
        duration2Teams: 90,
        duration3Teams: 120,
        duration4Teams: 180,
      }
    );
  });
});
