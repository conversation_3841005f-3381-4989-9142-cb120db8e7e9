import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { useAuth } from "../useAuth";

describe("useAuth", () => {
  // Save original localStorage methods
  const originalGetItem = Storage.prototype.getItem;
  const originalSetItem = Storage.prototype.setItem;

  beforeEach(() => {
    // Mock localStorage
    Storage.prototype.getItem = vi.fn();
    Storage.prototype.setItem = vi.fn();
  });

  afterEach(() => {
    // Restore original localStorage methods
    Storage.prototype.getItem = originalGetItem;
    Storage.prototype.setItem = originalSetItem;

    // Clear all mocks
    vi.clearAllMocks();
  });

  it("should initialize with not logged in state by default", () => {
    // Mock localStorage to return null (not logged in)
    vi.mocked(localStorage.getItem).mockImplementation(() => null);

    const { result } = renderHook(() => useAuth());

    expect(result.current.isLoggedIn).toBe(false);
    expect(result.current.isAdmin).toBe(false);
  });

  it("should detect logged in user state from localStorage", () => {
    // Mock localStorage to return logged in user
    vi.mocked(localStorage.getItem).mockImplementation((key) => {
      if (key === "isLoggedIn") return "true";
      if (key === "isAdminLoggedIn") return "false";
      return null;
    });

    const { result } = renderHook(() => useAuth());

    expect(result.current.isLoggedIn).toBe(true);
    expect(result.current.isAdmin).toBe(false);
  });

  it("should detect admin user state from localStorage", () => {
    // Mock localStorage to return admin user
    vi.mocked(localStorage.getItem).mockImplementation((key) => {
      if (key === "isLoggedIn") return "true";
      if (key === "isAdminLoggedIn") return "true";
      return null;
    });

    const { result } = renderHook(() => useAuth());

    expect(result.current.isLoggedIn).toBe(true);
    expect(result.current.isAdmin).toBe(true);
  });

  it("should handle errors when accessing localStorage", () => {
    // Mock localStorage.getItem to throw an error
    vi.mocked(localStorage.getItem).mockImplementation(() => {
      throw new Error("localStorage access denied");
    });

    // Spy on console.error
    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const { result } = renderHook(() => useAuth());

    // Should fall back to not logged in
    expect(result.current.isLoggedIn).toBe(false);
    expect(result.current.isAdmin).toBe(false);

    // Should log the error
    expect(consoleSpy).toHaveBeenCalledWith("Error accessing localStorage:", expect.any(Error));

    // Clean up
    consoleSpy.mockRestore();
  });

  it('should consider any non-"true" value as false', () => {
    // Mock localStorage to return various non-true values
    vi.mocked(localStorage.getItem).mockImplementation((key) => {
      if (key === "isLoggedIn") return "yes"; // not exactly "true"
      if (key === "isAdminLoggedIn") return "1"; // not exactly "true"
      return null;
    });

    const { result } = renderHook(() => useAuth());

    // Both should be false since they're not exactly "true"
    expect(result.current.isLoggedIn).toBe(false);
    expect(result.current.isAdmin).toBe(false);
  });

  it("should respond to storage events from other tabs", () => {
    // Mock localStorage to return not logged in initially
    vi.mocked(localStorage.getItem).mockImplementation(() => null);

    const { result } = renderHook(() => useAuth());

    // Initially not logged in
    expect(result.current.isLoggedIn).toBe(false);
    expect(result.current.isAdmin).toBe(false);

    // Update mock to return logged in
    vi.mocked(localStorage.getItem).mockImplementation((key) => {
      if (key === "isLoggedIn") return "true";
      if (key === "isAdminLoggedIn") return "true";
      return null;
    });

    // Fire storage event to simulate another tab changing localStorage
    act(() => {
      window.dispatchEvent(
        new StorageEvent("storage", {
          key: "isLoggedIn",
          newValue: "true",
          oldValue: null,
          storageArea: localStorage,
        })
      );
    });

    // Now the hook should have updated
    expect(result.current.isLoggedIn).toBe(true);
    expect(result.current.isAdmin).toBe(true);
  });

  it("should clean up event listeners on unmount", () => {
    // Spy on window.addEventListener and removeEventListener
    const addEventSpy = vi.spyOn(window, "addEventListener");
    const removeEventSpy = vi.spyOn(window, "removeEventListener");

    // Render and unmount the hook
    const { unmount } = renderHook(() => useAuth());

    // Verify that event listener was added
    expect(addEventSpy).toHaveBeenCalledWith("storage", expect.any(Function));

    // Unmount to trigger cleanup
    unmount();

    // Verify that event listener was removed
    expect(removeEventSpy).toHaveBeenCalledWith("storage", expect.any(Function));

    // Clean up spies
    addEventSpy.mockRestore();
    removeEventSpy.mockRestore();
  });
});
