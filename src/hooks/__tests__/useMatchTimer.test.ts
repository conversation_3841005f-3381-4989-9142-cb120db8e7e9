import { renderHook, act } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { useMatchTimer } from "../useMatchTimer";

describe("useMatchTimer", () => {
  // Mock timer functions
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should initialize with correct values", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    expect(result.current.timeRemaining).toBe(300);
    expect(result.current.formattedTime).toBe("05:00");
    expect(result.current.isRunning).toBe(false);
    expect(result.current.isPaused).toBe(false);
    expect(result.current.progress).toBe(100);
  });

  it("should start and update timer correctly", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    // Start the timer
    act(() => {
      result.current.startTimer();
    });

    expect(result.current.isRunning).toBe(true);
    expect(result.current.isPaused).toBe(false);

    // Advance timer by 10 seconds
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(result.current.timeRemaining).toBe(290);
    expect(result.current.formattedTime).toBe("04:50");
    expect(result.current.progress).toBe((290 / 300) * 100);
  });

  it("should pause and resume timer correctly", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    // Start the timer
    act(() => {
      result.current.startTimer();
    });

    // Advance timer by 10 seconds
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    // Pause the timer
    act(() => {
      result.current.pauseTimer();
    });

    expect(result.current.isRunning).toBe(false);
    expect(result.current.isPaused).toBe(true);
    expect(result.current.timeRemaining).toBe(290);

    // Advance timer by 10 more seconds while paused
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    // Time should not change while paused
    expect(result.current.timeRemaining).toBe(290);

    // Resume the timer
    act(() => {
      result.current.startTimer();
    });

    expect(result.current.isRunning).toBe(true);
    expect(result.current.isPaused).toBe(false);

    // Advance timer by 10 more seconds
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(result.current.timeRemaining).toBe(280);
  });

  it("should reset timer correctly", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    // Start the timer
    act(() => {
      result.current.startTimer();
    });

    // Advance timer by 10 seconds
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(result.current.timeRemaining).toBe(290);

    // Reset the timer
    act(() => {
      result.current.resetTimer();
    });

    expect(result.current.timeRemaining).toBe(300);
    expect(result.current.isRunning).toBe(false);
    expect(result.current.isPaused).toBe(false);
    expect(result.current.progress).toBe(100);
  });

  it("should reset timer with new duration", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    // Reset with new duration (10 minutes)
    act(() => {
      result.current.resetTimer(600);
    });

    expect(result.current.timeRemaining).toBe(600);
    expect(result.current.formattedTime).toBe("10:00");
    expect(result.current.progress).toBe(100);
  });

  it("should add time correctly", () => {
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
      })
    );

    // Start the timer
    act(() => {
      result.current.startTimer();
    });

    // Advance timer by 10 seconds
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    expect(result.current.timeRemaining).toBe(290);

    // Add 60 seconds (1 minute)
    act(() => {
      result.current.addTime(60);
    });

    expect(result.current.timeRemaining).toBe(350);
    expect(result.current.formattedTime).toBe("05:50");
    // Progress should be based on new total duration (360 seconds)
    expect(result.current.progress).toBeCloseTo((350 / 360) * 100);
  });

  it("should call onTimeUp when timer reaches zero", () => {
    const onTimeUp = vi.fn();
    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 5,
        onTimeUp,
      })
    );

    // Start the timer
    act(() => {
      result.current.startTimer();
    });

    // Advance timer to completion
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    expect(result.current.timeRemaining).toBe(0);
    expect(result.current.isRunning).toBe(false);
    expect(onTimeUp).toHaveBeenCalledTimes(1);
  });

  it("should initialize with startTime if provided", () => {
    // Create a start time 30 seconds in the past
    const startTime = new Date();
    startTime.setSeconds(startTime.getSeconds() - 30);

    const { result } = renderHook(() =>
      useMatchTimer({
        initialDurationSeconds: 300, // 5 minutes
        startTime,
      })
    );

    // Timer should have ~270 seconds remaining (5 minutes - 30 seconds)
    expect(result.current.timeRemaining).toBeCloseTo(270, -1); // Allow some tolerance
    expect(result.current.isRunning).toBe(true);
  });
});
