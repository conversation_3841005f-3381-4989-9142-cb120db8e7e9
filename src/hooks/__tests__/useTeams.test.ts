import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useTeams } from "../useTeams";
import { supabase } from "@/integrations/supabase/client";

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(),
    channel: vi.fn(),
    removeChannel: vi.fn(),
  },
}));

describe("useTeams", () => {
  const mockTeamsData = [
    {
      id: "1",
      name: "Team A",
      game_session_id: "session1",
      average_rating: 7.2,
    },
    {
      id: "2",
      name: "Team B",
      game_session_id: "session1",
      average_rating: 7.4,
    },
  ];

  const mockTeamPlayersData = [
    {
      team_id: "1",
      player_id: "p1",
      players: {
        id: "p1",
        name: "Player 1",
        jersey_number: 10,
        rating: 7.5,
      },
    },
    {
      team_id: "1",
      player_id: "p2",
      players: {
        id: "p2",
        name: "Player 2",
        jersey_number: 11,
        rating: 7.0,
      },
    },
    {
      team_id: "2",
      player_id: "p3",
      players: {
        id: "p3",
        name: "Player 3",
        jersey_number: 12,
        rating: 7.6,
      },
    },
  ];

  let mockChannel: any;

  beforeEach(() => {
    vi.clearAllMocks();

    mockChannel = {
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    };

    // Mock Supabase queries
    (supabase.from as any).mockImplementation((table: string) => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      then: vi.fn().mockImplementation((callback: any) => {
        if (table === "teams") {
          callback({ data: mockTeamsData, error: null });
        } else if (table === "team_players") {
          callback({ data: mockTeamPlayersData, error: null });
        }
        return Promise.resolve();
      }),
    }));

    // Mock Supabase channel
    (supabase.channel as any).mockReturnValue(mockChannel);
  });

  it("should fetch teams and players for a given session", async () => {
    const { result } = renderHook(() => useTeams("session1"));

    // Initial state
    expect(result.current.loading).toBe(true);
    expect(result.current.teams).toEqual([]);

    // Wait for data to be loaded
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Check final state
    expect(result.current.loading).toBe(false);
    expect(result.current.teams).toHaveLength(2);
    expect(result.current.teams[0].name).toBe("Team A");
    expect(result.current.teams[0].players).toHaveLength(2);
    expect(result.current.teams[1].name).toBe("Team B");
    expect(result.current.teams[1].players).toHaveLength(1);
  });

  it("should handle empty session ID", async () => {
    const { result } = renderHook(() => useTeams());

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.teams).toEqual([]);
  });

  it("should handle Supabase errors", async () => {
    // Mock error response
    (supabase.from as any).mockImplementation(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      then: vi.fn().mockImplementation((callback: any) => {
        callback({ data: null, error: new Error("Test error") });
        return Promise.resolve();
      }),
    }));

    const { result } = renderHook(() => useTeams("session1"));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.teams).toEqual([]);
  });

  it("should maintain real-time subscription", async () => {
    const { result } = renderHook(() => useTeams("session1"));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    // Verify channel subscription
    expect(supabase.channel).toHaveBeenCalledWith("teams-changes");
    expect(mockChannel.on).toHaveBeenCalledTimes(3);
    expect(mockChannel.subscribe).toHaveBeenCalled();
  });

  it("should correctly map player data to teams", async () => {
    const { result } = renderHook(() => useTeams("session1"));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    const teamA = result.current.teams[0];
    expect(teamA.players[0].name).toBe("Player 1");
    expect(teamA.players[0].jerseyNumber).toBe(10);
    expect(teamA.players[0].rating).toBe(7.5);
    expect(teamA.players[0].status).toBe("in");
  });

  it("should handle empty teams data", async () => {
    (supabase.from as any).mockImplementation((table: string) => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      then: vi.fn().mockImplementation((callback: any) => {
        if (table === "teams") {
          callback({ data: [], error: null });
        } else if (table === "team_players") {
          callback({ data: mockTeamPlayersData, error: null });
        }
        return Promise.resolve();
      }),
    }));

    const { result } = renderHook(() => useTeams("session1"));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.teams).toEqual([]);
  });

  it("should handle empty players data", async () => {
    // Mock teams response with empty players
    (supabase.from as any).mockImplementation((table: string) => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      then: vi.fn().mockImplementation((callback: any) => {
        if (table === "teams") {
          callback({ data: mockTeamsData, error: null });
        } else if (table === "team_players") {
          callback({ data: [], error: null });
        }
        return Promise.resolve();
      }),
    }));

    const { result } = renderHook(() => useTeams("session1"));

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.teams).toHaveLength(2);
    expect(result.current.teams[0].players).toEqual([]);
    expect(result.current.teams[1].players).toEqual([]);
  });
});
