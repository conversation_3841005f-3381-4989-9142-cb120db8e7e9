import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Player, PlayerRole } from "@/types";
import { toast } from "@/components/ui/use-toast";
import { useLoadingState } from "./useLoadingState";
import { useTeamSettings } from "./useTeamSettings";

export const usePlayers = (sessionId?: string) => {
  const [players, setPlayers] = useState<Player[]>([]);
  const { loading, startLoading, stopLoading } = useLoadingState(true);
  const { allow3Teams, duration2Teams, duration3Teams, duration4Teams } = useTeamSettings();

  useEffect(() => {
    const fetchPlayers = async () => {
      startLoading();

      try {
        // First, get all players
        const { data: playersData, error: playersError } = await supabase
          .from("players")
          .select("*")
          .order("name");

        if (playersError) throw playersError;

        let currentSessionId = sessionId;

        // If no sessionId was provided, fetch the current one
        if (!currentSessionId) {
          const { data: sessionData, error: sessionError } = await supabase
            .from("game_sessions")
            .select("*")
            .eq("status", "scheduled")
            .order("date", { ascending: false })
            .limit(1);

          if (sessionError) throw sessionError;
          currentSessionId = sessionData && sessionData.length > 0 ? sessionData[0].id : null;
        }

        // If we have a session ID (provided or fetched), get all player signups for this session
        let signupsData: any[] = [];
        if (currentSessionId) {
          const { data: signups, error: signupsError } = await supabase
            .from("player_signups")
            .select("*")
            .eq("game_session_id", currentSessionId);

          if (signupsError) throw signupsError;

          signupsData = signups || [];
        }

        // Map players and merge with signup status
        if (playersData) {
          const mappedPlayers = playersData.map((player) => {
            // Find signup status for this player
            const signup = signupsData.find((s) => s.player_id === player.id);

            return {
              id: player.id,
              name: player.name,
              jerseyNumber: player.jersey_number,
              rating: player.rating || 50,
              role: player.role as PlayerRole | undefined,
              status: signup ? signup.status : ("pending" as const),
              signupTimestamp: signup ? new Date(signup.signup_time) : undefined,
              is_active: player.is_active,
            };
          });

          setPlayers(mappedPlayers);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: `Failed to fetch players: ${error instanceof Error ? error.message : "Unknown error"}`,
          variant: "destructive",
        });
        setPlayers([]);
      } finally {
        stopLoading();
      }
    };

    fetchPlayers();

    // Only set up subscriptions if we need real-time updates
    if (sessionId) {
      // Set up more targeted real-time subscriptions using the provided sessionId
      const playersChannel = supabase
        .channel("players_changes")
        .on("postgres_changes", { event: "*", schema: "public", table: "players" }, () =>
          fetchPlayers()
        )
        .subscribe();

      const signupsChannel = supabase
        .channel("signups_changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "player_signups",
            filter: `game_session_id=eq.${sessionId}`,
          },
          () => fetchPlayers()
        )
        .subscribe();

      return () => {
        supabase.removeChannel(playersChannel);
        supabase.removeChannel(signupsChannel);
      };
    } else {
      // Set up broader real-time subscription for all relevant tables
      const playersChannel = supabase
        .channel("players_changes")
        .on("postgres_changes", { event: "*", schema: "public", table: "players" }, () =>
          fetchPlayers()
        )
        .subscribe();

      const signupsChannel = supabase
        .channel("signups_changes")
        .on("postgres_changes", { event: "*", schema: "public", table: "player_signups" }, () =>
          fetchPlayers()
        )
        .subscribe();

      const sessionsChannel = supabase
        .channel("sessions_changes")
        .on("postgres_changes", { event: "*", schema: "public", table: "game_sessions" }, () =>
          fetchPlayers()
        )
        .subscribe();

      return () => {
        supabase.removeChannel(playersChannel);
        supabase.removeChannel(signupsChannel);
        supabase.removeChannel(sessionsChannel);
      };
    }
  }, [startLoading, stopLoading, sessionId]);

  const createPlayer = async (player: Omit<Player, "status" | "signupTimestamp">) => {
    try {
      const { data, error } = await supabase
        .from("players")
        .insert({
          name: player.name,
          jersey_number: player.jerseyNumber,
          rating: player.rating || 50,
          role: player.role,
          is_active: player.is_active,
        })
        .select();

      if (error) throw error;

      if (data) {
        const newPlayer: Player = {
          id: data[0].id,
          name: data[0].name,
          jerseyNumber: data[0].jersey_number,
          rating: data[0].rating,
          role: data[0].role as PlayerRole | undefined,
          status: "pending",
          is_active: data[0].is_active,
        };
        setPlayers((prev) => [...prev, newPlayer]);
        return newPlayer;
      }
    } catch (error) {
      console.error("Error creating player:", error);
      toast({
        title: "Fehler",
        description: "Spieler konnte nicht erstellt werden",
        variant: "destructive",
      });
    }
    return null;
  };

  const updatePlayer = async (id: string, updates: Partial<Player>) => {
    try {
      // Get current game session
      const { data: sessionData, error: sessionError } = await supabase
        .from("game_sessions")
        .select("*")
        .eq("status", "scheduled")
        .order("date", { ascending: false })
        .limit(1);

      if (sessionError) throw sessionError;

      if (!sessionData || sessionData.length === 0) {
        console.log("No active session found");
        return false;
      }

      const currentSessionId = sessionData[0].id;

      // If we're updating the player status, we need to update or create a player_signup record
      if (updates.status) {
        // Check if there's already a signup for this player and session
        const { data: existingSignups, error: checkError } = await supabase
          .from("player_signups")
          .select("*")
          .eq("player_id", id)
          .eq("game_session_id", currentSessionId);

        if (checkError) {
          console.error("Error checking existing signups:", checkError);
          throw checkError;
        }

        console.log("Existing signups:", existingSignups);

        // Convert Date object to ISO string for Supabase
        const signupTimeISOString = updates.signupTimestamp
          ? updates.signupTimestamp instanceof Date
            ? updates.signupTimestamp.toISOString()
            : updates.signupTimestamp
          : new Date().toISOString();

        if (existingSignups && existingSignups.length > 0) {
          // Update existing signup
          console.log("Updating existing signup");
          const { data, error: updateError } = await supabase
            .from("player_signups")
            .update({
              status: updates.status,
              signup_time: signupTimeISOString,
            })
            .eq("player_id", id)
            .eq("game_session_id", currentSessionId)
            .select();

          if (updateError) {
            console.error("Error updating signup:", updateError);
            throw updateError;
          }

          console.log("Update result:", data);
        } else {
          // Create new signup
          console.log("Creating new signup");
          const { data, error: insertError } = await supabase
            .from("player_signups")
            .insert({
              player_id: id,
              game_session_id: currentSessionId,
              status: updates.status,
              signup_time: signupTimeISOString,
            })
            .select();

          if (insertError) {
            console.error("Error inserting signup:", insertError);
            throw insertError;
          }

          console.log("Insert result:", data);
        }

        // After updating signup status, update game duration if needed
        const { data: signupsData, error: signupsError } = await supabase
          .from("player_signups")
          .select("*")
          .eq("game_session_id", currentSessionId)
          .eq("status", "in");

        if (signupsError) throw signupsError;

        const inPlayerCount = signupsData?.length || 0;
        console.log(`Current in players count: ${inPlayerCount}`);

        // Calculate new duration based on player count and settings
        let newDuration = duration2Teams;
        if (inPlayerCount >= 20) {
          newDuration = duration4Teams;
        } else if (inPlayerCount >= 15 && inPlayerCount <= 19 && allow3Teams) {
          newDuration = duration3Teams;
        } else {
          newDuration = duration2Teams;
        }

        console.log("Updating game duration to:", newDuration);

        // Update game session duration
        const { error: updateError } = await supabase
          .from("game_sessions")
          .update({ duration_minutes: newDuration })
          .eq("id", currentSessionId);

        if (updateError) throw updateError;

        console.log("Game duration updated successfully");
      }

      // If we're updating player details (name, jersey, rating, role, is_active)
      const dbUpdates: any = {};
      if (updates.name) dbUpdates.name = updates.name;
      if (updates.jerseyNumber !== undefined) dbUpdates.jersey_number = updates.jerseyNumber;
      if (updates.rating !== undefined) dbUpdates.rating = updates.rating;
      if (updates.role !== undefined) dbUpdates.role = updates.role;
      if (updates.is_active !== undefined) dbUpdates.is_active = updates.is_active;

      // Only update player info if we have changes to make
      if (Object.keys(dbUpdates).length > 0) {
        const { error } = await supabase.from("players").update(dbUpdates).eq("id", id);

        if (error) throw error;
      }

      // Update local state immediately to provide faster feedback
      setPlayers((prev) =>
        prev.map((player) =>
          player.id === id
            ? {
                ...player,
                ...updates,
              }
            : player
        )
      );

      console.log("Player update completed successfully");
      return true;
    } catch (error) {
      console.error("Error updating player:", error);
      toast({
        title: "Fehler",
        description: "Spieler konnte nicht aktualisiert werden",
        variant: "destructive",
      });
      return false;
    }
  };

  const deletePlayer = async (id: string) => {
    try {
      // First delete all signup records for this player
      const { error: signupDeleteError } = await supabase
        .from("player_signups")
        .delete()
        .eq("player_id", id);

      if (signupDeleteError) throw signupDeleteError;

      // Then delete the player
      const { error } = await supabase.from("players").delete().eq("id", id);

      if (error) throw error;

      setPlayers((prev) => prev.filter((player) => player.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting player:", error);
      toast({
        title: "Fehler",
        description: "Spieler konnte nicht gelöscht werden",
        variant: "destructive",
      });
      return false;
    }
  };

  return { players, loading, createPlayer, updatePlayer, deletePlayer };
};
