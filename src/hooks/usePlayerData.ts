import { useState, useEffect, useRef } from "react";
import { usePlayers } from "@/hooks/usePlayers";
import { supabase } from "@/integrations/supabase/client";
import { Player, PlayerRole } from "@/types";

// Define type for player signup from Supabase
interface PlayerSignup {
  player_id: string;
  game_session_id: string;
  status: "in" | "out" | "pending";
  signup_time: string;
}

// Define type for Supabase realtime payload
interface RealtimePayload {
  eventType: "INSERT" | "UPDATE" | "DELETE";
  new: PlayerSignup;
  old: PlayerSignup;
}

/**
 * Custom hook for managing player data with realtime updates
 * Handles initial data fetching and subscriptions to player signup changes
 */
export function usePlayerData(sessionId: string | undefined) {
  // Initial data fetch for players
  const { players: initialPlayers, loading } = usePlayers(sessionId);

  // Keep our own state of players to update it incrementally
  const [players, setPlayers] = useState<Player[]>(initialPlayers);
  const subscriptionRef = useRef<ReturnType<typeof supabase.channel> | null>(null);

  // Update the local players state when the initialPlayers change (initial load)
  useEffect(() => {
    if (initialPlayers.length > 0) {
      setPlayers(initialPlayers);
    }
  }, [initialPlayers]);

  // Set up Supabase realtime subscription for player signups
  useEffect(() => {
    if (!sessionId) return;

    // Clean up previous subscription if it exists
    if (subscriptionRef.current) {
      supabase.removeChannel(subscriptionRef.current);
    }

    // Create a new subscription
    const channel = supabase
      .channel(`player-signups-${sessionId}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "player_signups",
          filter: `game_session_id=eq.${sessionId}`,
        },
        async (payload: any) => {
          // Cast payload to our defined type
          const typedPayload = payload as unknown as RealtimePayload;

          if (payload.eventType === "INSERT" || payload.eventType === "UPDATE") {
            const newSignup = typedPayload.new;

            // Fetch just the updated player to get complete data
            const { data: playerData } = await supabase
              .from("players")
              .select("*")
              .eq("id", newSignup.player_id)
              .single();

            if (playerData) {
              // Update player in local state
              setPlayers((currentPlayers) => {
                // Map to find and update the specific player
                const updatedPlayers = [...currentPlayers];
                const playerIndex = updatedPlayers.findIndex((p) => p.id === newSignup.player_id);

                if (playerIndex >= 0) {
                  // Update existing player
                  updatedPlayers[playerIndex] = {
                    ...updatedPlayers[playerIndex],
                    status: newSignup.status,
                    signupTimestamp: new Date(newSignup.signup_time),
                  };
                } else {
                  // Add new player
                  updatedPlayers.push({
                    id: playerData.id,
                    name: playerData.name,
                    jerseyNumber: playerData.jersey_number,
                    rating: playerData.rating || 50,
                    role: playerData.role as PlayerRole,
                    status: newSignup.status,
                    signupTimestamp: new Date(newSignup.signup_time),
                  });
                }

                return updatedPlayers;
              });
            }
          } else if (payload.eventType === "DELETE") {
            const oldSignup = typedPayload.old;

            // Handle deletion by updating player status back to pending
            setPlayers((currentPlayers) => {
              return currentPlayers.map((player) => {
                if (player.id === oldSignup.player_id) {
                  return { ...player, status: "pending" as const };
                }
                return player;
              });
            });
          }
        }
      )
      .subscribe();

    subscriptionRef.current = channel;

    return () => {
      supabase.removeChannel(channel);
    };
  }, [sessionId]);

  return { players, loading };
}
