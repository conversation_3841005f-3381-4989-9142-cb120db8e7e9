import { useState, useEffect, useRef } from "react";
import { formatTimeRemaining } from "@/utils/dateUtils";

/**
 * Custom hook for managing game countdown timer
 * Updates the time remaining to the game every minute
 */
export function useGameCountdown(date: Date | null) {
  const [timeToGame, setTimeToGame] = useState("");
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!date) return;

    const updateTimeRemaining = () => {
      const time = formatTimeRemaining(date);
      setTimeToGame(time);
    };

    updateTimeRemaining();

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(updateTimeRemaining, 60000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [date]);

  return timeToGame;
}
