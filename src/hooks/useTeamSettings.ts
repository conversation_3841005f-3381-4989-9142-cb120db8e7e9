import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

const DEFAULT_PAYPAL_LINK = "https://www.paypal.me/Kockar/";

export function useTeamSettings() {
  const [allow3Teams, setAllow3Teams] = useState(true);
  const [loading, setLoading] = useState(false);
  const [duration2Teams, setDuration2Teams] = useState(90);
  const [duration3Teams, setDuration3Teams] = useState(120);
  const [duration4Teams, setDuration4Teams] = useState(120);
  const [costPerPlayer2Teams, setCostPerPlayer2Teams] = useState(8.1);
  const [costPerPlayer3Teams, setCostPerPlayer3Teams] = useState(7.2);
  const [costPerPlayer4Teams, setCostPerPlayer4Teams] = useState(8.1);
  const [payPalMeLink, setPayPalMeLink] = useState(DEFAULT_PAYPAL_LINK);
  const [whatsAppNotificationsEnabled, setWhatsAppNotificationsEnabled] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      const { data, error } = await supabase.from("app_settings").select("key,value");
      if (!error && data) {
        for (const row of data) {
          if (row.key === "allow_3_teams") setAllow3Teams(row.value === "true");
          if (row.key === "match_duration_2_teams") setDuration2Teams(Number(row.value) || 90);
          if (row.key === "match_duration_3_teams") setDuration3Teams(Number(row.value) || 120);
          if (row.key === "match_duration_4_teams") setDuration4Teams(Number(row.value) || 120);
          if (row.key === "cost_per_player_2_teams")
            setCostPerPlayer2Teams(Number(row.value) || 8.1);
          if (row.key === "cost_per_player_3_teams")
            setCostPerPlayer3Teams(Number(row.value) || 7.2);
          if (row.key === "cost_per_player_4_teams")
            setCostPerPlayer4Teams(Number(row.value) || 8.1);
          if (row.key === "paypal_me_link") setPayPalMeLink(row.value || DEFAULT_PAYPAL_LINK);
          if (row.key === "whatsapp_notifications_enabled")
            setWhatsAppNotificationsEnabled(row.value === "true");
        }
      }
      setLoading(false);
    };
    fetchSettings();
  }, []);

  // Save functions can be called from UI if needed
  const saveSettings = async ({
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    payPalMeLink,
    whatsAppNotificationsEnabled,
  }: {
    allow3Teams?: boolean;
    duration2Teams?: number;
    duration3Teams?: number;
    duration4Teams?: number;
    costPerPlayer2Teams?: number;
    costPerPlayer3Teams?: number;
    costPerPlayer4Teams?: number;
    payPalMeLink?: string;
    whatsAppNotificationsEnabled?: boolean;
  }) => {
    const updates = [];
    if (allow3Teams !== undefined) {
      updates.push({ key: "allow_3_teams", value: allow3Teams ? "true" : "false" });
    }
    if (duration2Teams !== undefined) {
      updates.push({ key: "match_duration_2_teams", value: String(duration2Teams) });
    }
    if (duration3Teams !== undefined) {
      updates.push({ key: "match_duration_3_teams", value: String(duration3Teams) });
    }
    if (duration4Teams !== undefined) {
      updates.push({ key: "match_duration_4_teams", value: String(duration4Teams) });
    }
    if (costPerPlayer2Teams !== undefined) {
      updates.push({ key: "cost_per_player_2_teams", value: String(costPerPlayer2Teams) });
    }
    if (costPerPlayer3Teams !== undefined) {
      updates.push({ key: "cost_per_player_3_teams", value: String(costPerPlayer3Teams) });
    }
    if (costPerPlayer4Teams !== undefined) {
      updates.push({ key: "cost_per_player_4_teams", value: String(costPerPlayer4Teams) });
    }
    if (payPalMeLink !== undefined) {
      updates.push({ key: "paypal_me_link", value: payPalMeLink });
    }
    if (whatsAppNotificationsEnabled !== undefined) {
      updates.push({
        key: "whatsapp_notifications_enabled",
        value: whatsAppNotificationsEnabled ? "true" : "false",
      });
    }

    if (updates.length) {
      await supabase.from("app_settings").upsert(updates, { onConflict: "key" });
      // Keine manuelle Invalidierung mehr erforderlich, da wir Realtime Subscriptions verwenden
    }

    // After saving settings, update the current game session duration if needed
    try {
      // Get current game session
      const { data: sessionData, error: sessionError } = await supabase
        .from("game_sessions")
        .select("*")
        .eq("status", "scheduled")
        .order("date", { ascending: false })
        .limit(1);

      if (sessionError) throw sessionError;

      if (sessionData && sessionData.length > 0) {
        const currentSession = sessionData[0];

        // Get current player count
        const { data: signupsData, error: signupsError } = await supabase
          .from("player_signups")
          .select("*")
          .eq("game_session_id", currentSession.id)
          .eq("status", "in");

        if (signupsError) throw signupsError;

        const inPlayerCount = signupsData?.length || 0;
        console.log(`Current in players count: ${inPlayerCount}`);

        // Calculate new duration based on updated settings
        let newDuration = duration2Teams;
        if (inPlayerCount >= 20) {
          newDuration = duration4Teams;
        } else if (inPlayerCount >= 15 && inPlayerCount <= 19 && allow3Teams) {
          newDuration = duration3Teams;
        } else {
          newDuration = duration2Teams;
        }

        console.log("Updating game duration to:", newDuration);

        // Update game session duration
        const { error: updateError } = await supabase
          .from("game_sessions")
          .update({ duration_minutes: newDuration })
          .eq("id", currentSession.id);

        if (updateError) throw updateError;

        console.log("Game duration updated successfully");
      }
    } catch (error) {
      console.error("Error updating game duration after settings change:", error);
    }
  };

  return {
    allow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    costPerPlayer2Teams,
    costPerPlayer3Teams,
    costPerPlayer4Teams,
    payPalMeLink,
    whatsAppNotificationsEnabled,
    loading,
    saveSettings,
  };
}
