import { useState, useEffect } from "react";
import { Player } from "@/types";
import { usePlayers } from "./usePlayers";
import { useGameSessions } from "./useGameSessions";
import { useTeamSettings } from "@/hooks/useTeamSettings";

export function usePlayerLists(allow3Teams: boolean) {
  const { players, updatePlayer } = usePlayers();
  const { currentSession, updateGameDuration } = useGameSessions();
  const [confirmedPlayers, setConfirmedPlayers] = useState<Player[]>([]);
  const [reservePlayers, setReservePlayers] = useState<Player[]>([]);
  const [localPlayers, setLocalPlayers] = useState<Player[]>([]);
  const {
    allow3Teams: globalAllow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
  } = useTeamSettings();

  useEffect(() => {
    if (players && players.length > 0) {
      setLocalPlayers(players);
    }
  }, [players]);

  const updatePlayerLists = (currentPlayers: Player[]) => {
    const inPlayers = currentPlayers
      .filter((p) => p.status === "in" && p.is_active)
      .sort((a, b) => {
        if (a.signupTimestamp && b.signupTimestamp) {
          return new Date(a.signupTimestamp).getTime() - new Date(b.signupTimestamp).getTime();
        }
        return 0;
      });

    const totalIn = inPlayers.length;
    let confirmed: Player[] = [];
    let reserves: Player[] = [];

    if (totalIn <= 9) {
      confirmed = [...inPlayers];
    } else if (totalIn === 10) {
      confirmed = [...inPlayers];
    } else if (totalIn >= 11 && totalIn <= 14) {
      confirmed = inPlayers.slice(0, 10);
      reserves = inPlayers.slice(10);
    } else if (totalIn >= 15 && totalIn <= 19) {
      if (allow3Teams) {
        confirmed = inPlayers.slice(0, 15);
        reserves = inPlayers.slice(15);
      } else {
        confirmed = inPlayers.slice(0, 10);
        reserves = inPlayers.slice(10);
      }
    } else if (totalIn >= 20) {
      confirmed = inPlayers.slice(0, 20);
      reserves = inPlayers.slice(20);
    }

    setConfirmedPlayers(confirmed);
    setReservePlayers(reserves);

    updateGameDurationIfNeeded(totalIn);
  };

  const updateGameDurationIfNeeded = (playerCount: number) => {
    // Only proceed if we have an active session
    if (!currentSession?.id) {
      console.log("No active session found. Cannot update duration.");
      return;
    }

    console.log(`Checking if duration update needed for ${playerCount} players`);

    // Determine required duration based on player count
    let newDuration: number;

    if (playerCount >= 20) {
      newDuration = duration4Teams;
      console.log(`4 teams format: ${newDuration} minutes`);
    } else if (playerCount >= 15 && playerCount <= 19 && globalAllow3Teams) {
      newDuration = duration3Teams;
      console.log(`3 teams format: ${newDuration} minutes`);
    } else {
      newDuration = duration2Teams;
      console.log(`2 teams format: ${newDuration} minutes`);
    }

    const currentDuration = currentSession.duration_minutes;
    console.log(`Current DB duration: ${currentDuration}, Calculated duration: ${newDuration}`);

    // Always update the database to ensure consistency
    updateGameDuration(currentSession.id, playerCount, {
      allow3Teams: globalAllow3Teams,
      duration2Teams,
      duration3Teams,
      duration4Teams,
    })
      .then((result) => {
        if (result) {
          console.log(
            `✅ Duration successfully updated in database to ${result.duration_minutes} minutes`
          );
        } else {
          console.error("❌ Failed to update duration in database");
        }
      })
      .catch((error) => {
        console.error("❌ Error updating duration:", error);
      });
  };

  useEffect(() => {
    if (localPlayers && localPlayers.length > 0 && currentSession) {
      updatePlayerLists(localPlayers);
    } else {
      setConfirmedPlayers([]);
      setReservePlayers([]);
    }
  }, [
    localPlayers,
    currentSession,
    allow3Teams,
    globalAllow3Teams,
    duration2Teams,
    duration3Teams,
    duration4Teams,
    updateGameDuration,
  ]);

  const handleUpdatePlayer = async (playerId: string, updates: Partial<Player>) => {
    const result = await updatePlayer(playerId, updates);
    if (result) {
      // Update local state first for immediate UI feedback
      const updatedPlayers = localPlayers.map((player) =>
        player.id === playerId ? { ...player, ...updates } : player
      );

      setLocalPlayers(updatedPlayers);

      // Immediately recalculate player lists to update duration if needed
      updatePlayerLists(updatedPlayers);
    }
    return result;
  };

  return {
    confirmedPlayers,
    reservePlayers,
    updatePlayer: handleUpdatePlayer,
    players: localPlayers,
  };
}
