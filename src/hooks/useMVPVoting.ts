import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { Player } from "@/types";

export function useMVPVoting() {
  const [selectedPlayer, setSelectedPlayer] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitVote = async (votingPeriodId: string, playerId: string): Promise<boolean> => {
    if (!playerId || !votingPeriodId) return false;

    setIsSubmitting(true);
    try {
      // Generate a device ID if not exists
      let deviceId = localStorage.getItem("deviceId");
      if (!deviceId) {
        deviceId = crypto.randomUUID();
        localStorage.setItem("deviceId", deviceId);
      }

      const { error } = await supabase.from("mvp_votes" as any).insert({
        voting_period_id: votingPeriodId,
        player_id: playerId,
        voter_device_id: deviceId,
      });

      if (error) {
        if (error.code === "23505") {
          // Unique violation
          toast({
            title: "Bereits abgestimmt",
            description: "Du hast für dieses Spiel bereits eine Stimme abgegeben",
            variant: "destructive",
          });
        } else {
          throw error;
        }
        return false;
      } else {
        toast({
          title: "Stimme abgegeben",
          description: "Vielen Dank für deine MVP-Abstimmung!",
        });
        setSelectedPlayer(null);
        return true;
      }
    } catch (error) {
      console.error("Error submitting vote:", error);
      toast({
        title: "Fehler",
        description: "Deine Stimme konnte nicht übermittelt werden. Bitte versuche es erneut.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    selectedPlayer,
    setSelectedPlayer,
    submitVote,
    isSubmitting,
  };
}
