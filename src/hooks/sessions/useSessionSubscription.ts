import { useEffect, useRef, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";

export function useSessionSubscription(onSessionChange: () => void) {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const isSubscribed = useRef(false);
  // Keep a stable reference to the callback
  const onChangeRef = useRef(onSessionChange);

  // Update the callback ref when it changes
  useEffect(() => {
    onChangeRef.current = onSessionChange;
  }, [onSessionChange]);

  // Debounced handler to avoid multiple fetches from rapid changes
  const debouncedChangeHandler = useCallback(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    // Debounce the fetch
    timeoutRef.current = setTimeout(() => {
      onChangeRef.current();
    }, 1000);
  }, []);

  useEffect(() => {
    if (isSubscribed.current) {
      console.log("⏳ Skipping subscription - already subscribed");
      return;
    }

    console.log("🔌 Setting up game sessions subscription");
    isSubscribed.current = true;

    const channel = supabase
      .channel("game_sessions_changes")
      .on("postgres_changes", { event: "*", schema: "public", table: "game_sessions" }, () => {
        console.log("📡 Game session change detected");
        debouncedChangeHandler();
      })
      .on("postgres_changes", { event: "*", schema: "public", table: "player_signups" }, () => {
        console.log("📡 Player signup change detected");
        debouncedChangeHandler();
      })
      .subscribe();

    return () => {
      console.log("🧹 Cleaning up game sessions subscription");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      supabase.removeChannel(channel);
      isSubscribed.current = false;
    };
  }, [debouncedChangeHandler]);
}
