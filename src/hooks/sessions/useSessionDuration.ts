import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { GameSession } from "@/types";

export function useSessionDuration() {
  const updateGameDuration = async (
    gameSessionId: string,
    playerCount: number,
    teamSettings: {
      allow3Teams: boolean;
      duration2Teams: number;
      duration3Teams: number;
      duration4Teams: number;
    }
  ) => {
    if (!gameSessionId) {
      console.error("Cannot update duration: no gameSessionId provided");
      return null;
    }

    const { allow3Teams, duration2Teams, duration3Teams, duration4Teams } = teamSettings;

    let newDuration = duration2Teams;

    if (playerCount >= 20) {
      newDuration = duration4Teams;
    } else if (playerCount >= 15 && playerCount <= 19 && allow3Teams) {
      newDuration = duration3Teams;
    } else {
      newDuration = duration2Teams;
    }

    try {
      console.log(
        `⏱️ Attempting to update game session ${gameSessionId} duration to ${newDuration} minutes for ${playerCount} players`
      );

      const { data, error } = await supabase
        .from("game_sessions")
        .update({ duration_minutes: newDuration })
        .eq("id", gameSessionId)
        .select();

      if (error) {
        console.error(`Failed to update game duration: ${error.message}`);
        toast({
          title: "Error",
          description: "Failed to update game duration",
          variant: "destructive",
        });
        return null;
      }

      if (data && data.length > 0) {
        console.log(
          `✅ Successfully updated game duration in DB to ${data[0].duration_minutes} minutes`
        );
        return {
          duration_minutes: data[0].duration_minutes,
        };
      }

      return null;
    } catch (error) {
      console.error("Error updating game duration:", error);
      toast({
        title: "Error",
        description: "Failed to update game duration",
        variant: "destructive",
      });
      return null;
    }
  };

  return {
    updateGameDuration,
  };
}
