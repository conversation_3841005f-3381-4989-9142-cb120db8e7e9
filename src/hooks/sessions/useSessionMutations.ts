import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { GameSession } from "@/types";
import { toast } from "@/components/ui/use-toast";
import { createOrReopenVotingPeriod } from "@/services/mvpService";

export function useSessionMutations() {
  const createGameSession = async (gameSession: Partial<GameSession>) => {
    try {
      // 1. First, archive any existing scheduled games
      const { data: existingSessions, error: findError } = await supabase
        .from("game_sessions")
        .select("id")
        .eq("status", "scheduled");

      if (findError) throw findError;

      // Archive existing sessions
      if (existingSessions && existingSessions.length > 0) {
        for (const session of existingSessions) {
          // Mark existing session as archived
          const { error: updateError } = await supabase
            .from("game_sessions")
            .update({ status: "archived", is_signup_open: false })
            .eq("id", session.id);

          if (updateError) throw updateError;

          // Reset all player signups for this session
          const { error: resetError } = await supabase
            .from("player_signups")
            .update({ status: "pending" })
            .eq("game_session_id", session.id);

          if (resetError) throw resetError;
        }
      }

      // 2. Now create the new game session
      const { data, error } = await supabase
        .from("game_sessions")
        .insert({
          date: gameSession.date?.toISOString(),
          signup_opens_at: gameSession.signupOpensAt?.toISOString(),
          is_signup_open: gameSession.isSignupOpen || false,
          is_team_generated: gameSession.isTeamGenerated || false,
          status: gameSession.status || "scheduled",
          duration_minutes: gameSession.duration_minutes || 90,
        } as any)
        .select();

      if (error) {
        console.error("Error creating game session:", error);
        toast({
          title: "Error",
          description: "Failed to create game session",
          variant: "destructive",
        });
        return null;
      }

      if (data) {
        return {
          id: data[0].id,
          date: new Date(data[0].date),
          signupOpensAt: new Date(data[0].signup_opens_at),
          isSignupOpen: data[0].is_signup_open || false,
          isTeamGenerated: data[0].is_team_generated || false,
          status: (data[0].status || "scheduled") as GameSession["status"],
          players: [],
          confirmedPlayers: [],
          reservePlayers: [],
          duration_minutes: data[0].duration_minutes,
        } as GameSession;
      }
    } catch (error) {
      console.error("Error in createGameSession:", error);
      toast({
        title: "Error",
        description: "Failed to create game session",
        variant: "destructive",
      });
    }
    return null;
  };

  const updateGameSession = async (id: string, updates: Partial<GameSession>) => {
    try {
      const dbUpdates: any = {};
      if (updates.date) dbUpdates.date = updates.date.toISOString();
      if (updates.signupOpensAt) dbUpdates.signup_opens_at = updates.signupOpensAt.toISOString();
      if (updates.isSignupOpen !== undefined) dbUpdates.is_signup_open = updates.isSignupOpen;
      if (updates.isTeamGenerated !== undefined)
        dbUpdates.is_team_generated = updates.isTeamGenerated;
      if (updates.status !== undefined) dbUpdates.status = updates.status;
      if (updates.duration_minutes !== undefined)
        dbUpdates.duration_minutes = updates.duration_minutes;

      // Special handling for status changes to played
      if (updates.status === "played") {
        try {
          console.log(
            "Game marked as played, creating/reopening MVP voting period for session:",
            id
          );

          const success = await createOrReopenVotingPeriod(id);

          if (!success) {
            toast({
              title: "MVP Voting Setup Error",
              description:
                "MVP voting period konnte nicht erstellt werden. Die Admin kann es manuell erstellen.",
              variant: "destructive",
            });
          } else {
            toast({
              title: "MVP Voting geöffnet",
              description:
                "Die MVP-Abstimmung für dieses Spiel wurde geöffnet und ist für 24 Stunden verfügbar.",
            });
          }
        } catch (votingErr) {
          console.error("Exception in creating MVP voting period:", votingErr);
          toast({
            title: "MVP Voting Error",
            description:
              "Es gab ein Problem beim Erstellen der MVP-Abstimmung. Bitte versuche es später erneut.",
            variant: "destructive",
          });
          // Continue with the game session update even if MVP voting fails
        }
      }

      // Reset player signups when game is no longer scheduled
      if (
        updates.status === "played" ||
        updates.status === "cancelled" ||
        updates.status === "archived"
      ) {
        const { error: resetError } = await supabase
          .from("player_signups")
          .update({ status: "pending" })
          .eq("game_session_id", id);

        if (resetError) throw resetError;
      }

      console.log("Updating game session with:", dbUpdates);
      const { data, error } = await supabase
        .from("game_sessions")
        .update(dbUpdates)
        .eq("id", id)
        .select();

      if (error) {
        console.error("Error updating game session:", error);
        toast({
          title: "Error",
          description: "Failed to update game session",
          variant: "destructive",
        });
        return null;
      }

      if (data) {
        return {
          id: data[0].id,
          date: new Date(data[0].date),
          signupOpensAt: new Date(data[0].signup_opens_at),
          isSignupOpen: data[0].is_signup_open || false,
          isTeamGenerated: data[0].is_team_generated || false,
          status: (data[0].status || "scheduled") as GameSession["status"],
          players: [],
          confirmedPlayers: [],
          reservePlayers: [],
          duration_minutes: data[0].duration_minutes,
        } as GameSession;
      }
      return null;
    } catch (error) {
      console.error("Error in updateGameSession:", error);
      toast({
        title: "Error",
        description: `Failed to update game session: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
      return null;
    }
  };

  return {
    createGameSession,
    updateGameSession,
  };
}
