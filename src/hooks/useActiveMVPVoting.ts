import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

export function useActiveMVPVoting() {
  const [hasActiveVoting, setHasActiveVoting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkActiveVoting = async () => {
      try {
        // Use type assertion to bypass TypeScript errors
        const { data, error } = await (supabase
          .from("mvp_voting_periods" as any)
          .select("id")
          .eq("is_open", true)
          .gte("ends_at", new Date().toISOString())
          .limit(1)
          .maybeSingle() as any);

        if (error) {
          console.error("Error checking MVP voting periods:", error);
        } else {
          setHasActiveVoting(!!data);
        }
      } catch (error) {
        console.error("Exception in checkActiveVoting:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkActiveVoting();

    // Set up real-time subscription
    const channel = supabase
      .channel("mvp_voting_changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "mvp_voting_periods" as any },
        () => {
          checkActiveVoting();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return { hasActiveVoting, isLoading };
}
