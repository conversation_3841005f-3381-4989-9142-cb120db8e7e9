import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import LoadingSpinner from "@/components/LoadingSpinner";
import Footer from "@/components/Footer";

export default function Login() {
  const [pin, setPin] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is already logged in
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";
    if (isLoggedIn) {
      navigate("/");
    } else {
      setIsLoading(false);
    }
  }, [navigate]);

  const handlePinChange = (value: string) => {
    setPin(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (pin.length < 4) {
      toast({
        title: "Fehler",
        description: "Bitte gib einen 4-stelligen PIN ein",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the app PIN from Supabase
      const { data, error } = await (supabase as any)
        .from("app_settings")
        .select("value")
        .eq("key", "app_pin")
        .single();

      if (error) {
        throw error;
      }

      // Check if PIN is correct
      if (data && data.value === pin) {
        // Store login state in localStorage
        localStorage.setItem("isLoggedIn", "true");

        toast({
          title: "Erfolgreich",
          description: "Du bist eingeloggt",
        });

        // Navigate to main page
        navigate("/");
      } else {
        toast({
          title: "Zugriff verweigert",
          description: "Der eingegebene PIN ist falsch",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      toast({
        title: "Fehler",
        description: "Es gab ein Problem bei der Anmeldung",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md flex flex-col items-center space-y-8">
          <img src="/images/logo.png" alt="INTER NRW Logo" className="h-32 w-32 mb-4" />

          <Card className="w-full border-none shadow-lg">
            <CardContent className="pt-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2 text-center">
                  <h1 className="text-2xl font-bold">Willkommen</h1>
                  <p className="text-sm text-gray-500">Bitte gib den Team-PIN ein</p>
                </div>

                <div className="flex justify-center py-4">
                  <InputOTP
                    maxLength={4}
                    value={pin}
                    onChange={handlePinChange}
                    inputMode="numeric"
                    pattern="[0-9]*"
                  >
                    <InputOTPGroup>
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                    </InputOTPGroup>
                  </InputOTP>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-team-primary text-white hover:bg-team-primary/90"
                  disabled={isSubmitting || pin.length < 4}
                >
                  {isSubmitting ? "Überprüfung..." : "Einloggen"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
