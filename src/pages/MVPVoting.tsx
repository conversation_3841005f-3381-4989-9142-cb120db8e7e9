import { useEffect, useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON><PERSON> } from "@/components/ui/button";
import LoadingSpinner from "@/components/LoadingSpinner";
import { useActiveMVPVoting } from "@/hooks/useActiveMVPVoting";
import { useMVPVoting } from "@/hooks/useMVPVoting";
import { supabase } from "@/integrations/supabase/client";
import { Player } from "@/types";
import { Trophy, ArrowLeft, Check, ChevronLeft, Calendar, UserCheck } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { PlayerName } from "@/components/player-profile/PlayerName";

// Define a specific type for the player data we get from the database query
interface TeamPlayerData {
  player_id: string;
  players: {
    id: string;
    name: string;
    jersey_number: number | null;
    rating: number;
    role: string | null;
  } | null;
}

export default function MVPVoting() {
  const navigate = useNavigate();
  const { hasActiveVoting, isLoading: isCheckingVoting } = useActiveMVPVoting();
  const { selectedPlayer, setSelectedPlayer, submitVote, isSubmitting } = useMVPVoting();
  const [players, setPlayers] = useState<Player[]>([]);
  const [votingPeriodId, setVotingPeriodId] = useState<string | null>(null);
  const [isLoadingPlayers, setIsLoadingPlayers] = useState(true);
  const [hasVoted, setHasVoted] = useState(false);
  const [votedPlayer, setVotedPlayer] = useState<Player | null>(null);
  const [gameDate, setGameDate] = useState<string | null>(null);
  const [teamStats, setTeamStats] = useState<{ [key: string]: { name: string; count: number } }>(
    {}
  );

  useEffect(() => {
    const fetchVotingPeriodAndPlayers = async () => {
      try {
        // Get the active voting period
        const { data: votingPeriod, error: votingError } = await (supabase
          .from("mvp_voting_periods" as any)
          .select("id, game_session_id, game_sessions(date)")
          .eq("is_open", true)
          .gte("ends_at", new Date().toISOString())
          .maybeSingle() as any);

        if (votingError) throw votingError;
        if (!votingPeriod) {
          navigate("/");
          return;
        }

        setVotingPeriodId(votingPeriod.id);

        // Format game date if available
        if (votingPeriod.game_sessions && votingPeriod.game_sessions.date) {
          const date = new Date(votingPeriod.game_sessions.date);
          setGameDate(format(date, "dd.MM.yyyy", { locale: de }));
        }

        // Get the device ID from localStorage
        const deviceId = localStorage.getItem("deviceId");
        let userVotedFor: string | null = null;
        let hasVotedInThisSession = false;

        // Check if the device has already voted
        if (deviceId) {
          const { data: existingDeviceVote } = await (supabase
            .from("mvp_votes" as any)
            .select("player_id")
            .eq("voting_period_id", votingPeriod.id)
            .eq("voter_device_id", deviceId)
            .maybeSingle() as any);

          if (existingDeviceVote) {
            hasVotedInThisSession = true;
            userVotedFor = existingDeviceVote.player_id;
            setSelectedPlayer(existingDeviceVote.player_id);
          }
        }

        // Also check if authenticated user has already voted
        const { data: user } = await supabase.auth.getUser();

        if (!hasVotedInThisSession && user && user.user) {
          const { data: existingUserVote } = await (supabase
            .from("mvp_votes" as any)
            .select("player_id")
            .eq("voting_period_id", votingPeriod.id)
            .eq("user_id", user.user.id)
            .maybeSingle() as any);

          if (existingUserVote) {
            hasVotedInThisSession = true;
            userVotedFor = existingUserVote.player_id;
            setSelectedPlayer(existingUserVote.player_id);
          }
        }

        // Set hasVoted state based on our checks
        setHasVoted(hasVotedInThisSession);

        // Get the players from the game session
        const { data: teamPlayers, error: playersError } = await supabase
          .from("team_players")
          .select(
            `
            player_id,
            team_id,
            players (
              id,
              name,
              jersey_number,
              rating,
              role
            ),
            teams (
              id,
              name
            )
          `
          )
          .eq("game_session_id", votingPeriod.game_session_id);

        if (playersError) throw playersError;

        // Calculate team statistics
        const teamStatsMap: { [key: string]: { name: string; count: number } } = {};

        (teamPlayers || []).forEach((tp: any) => {
          if (tp.team_id && tp.teams) {
            if (!teamStatsMap[tp.team_id]) {
              teamStatsMap[tp.team_id] = {
                name: tp.teams.name || `Team ${tp.team_id.substring(0, 4)}`,
                count: 0,
              };
            }
            teamStatsMap[tp.team_id].count++;
          }
        });

        setTeamStats(teamStatsMap);

        // Transform the team player data into our Player type
        const playersWithTeams = (teamPlayers || [])
          .filter((tp: any) => tp.players !== null)
          .map((tp: any) => ({
            ...tp.players,
            teamId: tp.team_id || null,
            teamName: tp.teams?.name || null,
          }))
          .reduce((acc: any[], current: any) => {
            if (!acc.some((p) => p.id === current.id)) {
              // Convert to our Player type with required properties
              acc.push({
                id: current.id,
                name: current.name,
                jerseyNumber: current.jersey_number || undefined,
                rating: current.rating,
                status: "in", // Default status since all players in team_players were "in" the game
                role: current.role as Player["role"],
                teamId: current.teamId,
                teamName: current.teamName,
              });
            }
            return acc;
          }, [])
          .sort((a: any, b: any) => a.name.localeCompare(b.name));

        setPlayers(playersWithTeams);

        // If user has voted, find the player they voted for
        if (hasVotedInThisSession && userVotedFor) {
          const player = playersWithTeams.find((p: any) => p.id === userVotedFor);
          if (player) {
            setVotedPlayer(player);
          }
        }
      } catch (error) {
        console.error("Error fetching voting data:", error);
      } finally {
        setIsLoadingPlayers(false);
      }
    };

    if (!isCheckingVoting && hasActiveVoting) {
      fetchVotingPeriodAndPlayers();
    } else if (!isCheckingVoting && !hasActiveVoting) {
      navigate("/");
    }
  }, [hasActiveVoting, isCheckingVoting, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedPlayer && votingPeriodId) {
      const success = await submitVote(votingPeriodId, selectedPlayer);
      if (success) {
        setHasVoted(true);
        const player = players.find((p) => p.id === selectedPlayer);
        if (player) {
          setVotedPlayer(player);
        }
      }
    }
  };

  if (isCheckingVoting || isLoadingPlayers) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-6 max-w-screen-xl flex justify-center items-center">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-muted-foreground">Lade MVP-Daten...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header removeBoxShadow={true} />
      <main className="flex-1 px-4">
        <div className="space-y-6 max-w-screen-xl mx-auto pt-6">
          <div className="flex">
            <Button
              variant="ghost"
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 -ml-2"
              onClick={() => navigate(-1)}
            >
              <ChevronLeft className="mr-1 h-4 w-4" />
              Zurück
            </Button>
          </div>

          <Card className="w-full dark:bg-zinc-900 dark:border-zinc-800">
            <CardHeader className="pb-0">
              <div className="flex justify-between items-center">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Trophy className="h-6 w-6 text-amber-600 dark:text-amber-500" />
                    <CardTitle className="text-3xl dark:text-white">MVP Abstimmung</CardTitle>
                  </div>
                  <CardDescription className="dark:text-zinc-400 flex items-center gap-2">
                    <span>Wähle den wertvollsten Spieler des letzten Spiels</span>
                    {gameDate && (
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800"
                      >
                        <Calendar className="h-3 w-3 mr-1" />
                        {gameDate}
                      </Badge>
                    )}
                  </CardDescription>
                </div>
              </div>
              <Separator className="dark:bg-zinc-800 mt-4" />
            </CardHeader>

            <CardContent className="pt-6">
              {hasVoted && votedPlayer ? (
                <div className="flex flex-col items-center justify-center py-8 space-y-8">
                  <div className="w-20 h-20 md:w-24 md:h-24 relative flex items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900/30 border-2 border-blue-200 dark:border-blue-800">
                    <Trophy className="h-12 w-12 md:h-14 md:w-14 text-blue-500 dark:text-blue-400" />
                    <div className="absolute -bottom-2 -right-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full w-10 h-10 flex items-center justify-center border-2 border-white dark:border-zinc-900 shadow-sm">
                      <Check className="h-6 w-6" />
                    </div>
                  </div>

                  <div className="text-center space-y-3 max-w-lg mx-auto">
                    <h3 className="text-2xl font-bold text-blue-800 dark:text-blue-400">
                      Du hast erfolgreich abgestimmt!
                    </h3>
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-900/50">
                      <p className="text-blue-700 dark:text-blue-300 font-medium mb-1">
                        Deine Stimme für den MVP-Spieler:
                      </p>
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-xl font-bold">{votedPlayer.name}</span>
                        {votedPlayer.jerseyNumber && (
                          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-400 border-none">
                            #{votedPlayer.jerseyNumber}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400">
                      Vielen Dank für deine Teilnahme an der MVP-Abstimmung. Die Ergebnisse werden
                      nach Abschluss der Abstimmung bekanntgegeben.
                    </p>
                  </div>

                  <Button
                    onClick={() => navigate("/")}
                    className="mt-4 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
                  >
                    Zurück zur Startseite
                  </Button>
                </div>
              ) : (
                <div>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 border border-blue-100 dark:border-blue-900/30 mb-6">
                      <h3 className="text-blue-800 dark:text-blue-300 font-medium mb-2">
                        Abstimmungsregeln:
                      </h3>
                      <ul className="text-blue-700 dark:text-blue-400 text-sm space-y-1 list-disc list-inside">
                        <li>Du kannst nur einmal pro Spieltag abstimmen</li>
                        <li>
                          Wähle den Spieler, der deiner Meinung nach die beste Leistung gezeigt hat
                        </li>
                        <li>Die Abstimmung ist anonym</li>
                      </ul>
                    </div>

                    <div className="border dark:border-zinc-800 rounded-lg overflow-hidden">
                      <div className="bg-gray-50 dark:bg-zinc-800/50 p-3 border-b dark:border-zinc-800">
                        <h3 className="font-medium text-gray-800 dark:text-gray-300">
                          Wähle einen Spieler
                        </h3>
                      </div>

                      <RadioGroup
                        value={selectedPlayer || ""}
                        onValueChange={setSelectedPlayer}
                        className="p-4"
                      >
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                          {players.map((player) => (
                            <div
                              key={player.id}
                              className={cn(
                                "relative border rounded-lg transition-all duration-200",
                                selectedPlayer === player.id
                                  ? "border-blue-500 dark:border-blue-500 ring-2 ring-blue-200 dark:ring-blue-900"
                                  : "border-gray-200 dark:border-zinc-800 hover:border-blue-300 dark:hover:border-blue-800/70"
                              )}
                            >
                              <label
                                htmlFor={player.id}
                                className="p-3 flex items-start gap-3 cursor-pointer w-full h-full block"
                              >
                                <RadioGroupItem
                                  value={player.id || ""}
                                  id={player.id}
                                  className="mt-1 border-blue-500 dark:border-blue-500"
                                />
                                <div className="min-w-0 flex-1">
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="text-base font-medium leading-none dark:text-white hover:text-blue-700 dark:hover:text-blue-400">
                                      <PlayerName
                                        playerId={player.id}
                                        playerName={player.name}
                                        className="hover:text-blue-700 dark:hover:text-blue-400"
                                      />
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2 mt-1">
                                    {player.jerseyNumber && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs font-normal bg-gray-50 dark:bg-zinc-800 border-gray-200 dark:border-zinc-700"
                                      >
                                        #{player.jerseyNumber}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="flex justify-end space-x-4 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate("/")}
                        className="border-gray-300 dark:border-zinc-700 dark:text-zinc-300"
                      >
                        Abbrechen
                      </Button>
                      <Button
                        type="submit"
                        disabled={!selectedPlayer || isSubmitting}
                        className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
                      >
                        {isSubmitting ? <LoadingSpinner size="sm" className="mr-2" /> : null}
                        {isSubmitting ? "Wird gesendet..." : "Abstimmen"}
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
