import { lazy, Suspense } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useGameSessions } from "@/hooks/useGameSessions";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { PlayerProfileModal } from "@/components/player-profile";

// Lazy load components
const ParticipationOverTime = lazy(() => import("@/components/history/ParticipationOverTime"));
const ParticipationRate = lazy(() => import("@/components/history/ParticipationRate"));
const NewVsReturningPlayers = lazy(() => import("@/components/history/NewVsReturningPlayers"));
const IronManAward = lazy(() => import("@/components/history/IronManAward"));
const ComebackOfMonth = lazy(() => import("@/components/history/ComebackOfMonth"));

export default function Statistics() {
  // Load game sessions for statistics page
  const { loading } = useGameSessions({
    fetchCurrent: false,
    fetchPast: true,
  });
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header removeBoxShadow={true} />

      <main className="flex-1 px-4">
        <div className="space-y-6 max-w-screen-xl mx-auto pt-6 pb-8">
          <div className="flex">
            <Button
              variant="ghost"
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 -ml-2"
              onClick={() => navigate(-1)}
            >
              <ChevronLeft className="mr-1 h-4 w-4" />
              Zurück
            </Button>
          </div>

          <Suspense
            fallback={
              <div className="py-8 flex justify-center">
                <LoadingSpinner />
              </div>
            }
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:auto-rows-fr">
              <ParticipationOverTime />
              <ParticipationRate />
              <NewVsReturningPlayers />
              <IronManAward />
              <ComebackOfMonth />
            </div>
          </Suspense>

          {/* More statistics components can be added here in the future */}
        </div>
      </main>

      <Footer />

      {/* Player Profile Modal */}
      <PlayerProfileModal />
    </div>
  );
}
