import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";
import { Player } from "@/types";

interface PlayerProfileContextType {
  isOpen: boolean;
  selectedPlayer: Player | null;
  openProfile: (player: Player) => void;
  closeProfile: () => void;
}

const PlayerProfileContext = createContext<PlayerProfileContextType | undefined>(undefined);

export const PlayerProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  const openProfile = useCallback((player: Player) => {
    setSelectedPlayer(player);
    setIsOpen(true);
  }, []);

  const closeProfile = useCallback(() => {
    setIsOpen(false);
    // Keep the player data for closing animation, then clear
    setTimeout(() => setSelectedPlayer(null), 300);
  }, []);

  return (
    <PlayerProfileContext.Provider value={{ isOpen, selectedPlayer, openProfile, closeProfile }}>
      {children}
    </PlayerProfileContext.Provider>
  );
};

export const usePlayerProfile = (): PlayerProfileContextType => {
  const context = useContext(PlayerProfileContext);
  if (context === undefined) {
    throw new Error("usePlayerProfile must be used within a PlayerProfileProvider");
  }
  return context;
};
