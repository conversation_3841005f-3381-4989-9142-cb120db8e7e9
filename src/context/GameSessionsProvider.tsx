import React, { createContext, useContext, useState, useEffect, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { GameSession, Team } from "@/types";
import { toast } from "@/components/ui/use-toast";
import { useLoadingState } from "@/hooks/useLoadingState";
import { useSessionMutations } from "@/hooks/sessions/useSessionMutations";
import { useSessionDuration } from "@/hooks/sessions/useSessionDuration";

interface GameSessionsContextType {
  currentSession: GameSession | null;
  pastSessions: GameSession[];
  gameSessions: GameSession[];
  loading: boolean;
  createGameSession: (session: Partial<GameSession>) => Promise<GameSession | null>;
  updateGameSession: (id: string, updates: Partial<GameSession>) => Promise<GameSession | null>;
  updateGameDuration: (
    gameSessionId: string,
    playerCount: number,
    teamSettings: {
      allow3Teams: boolean;
      duration2Teams: number;
      duration3Teams: number;
      duration4Teams: number;
    }
  ) => Promise<{ duration_minutes: number } | null>;
  refetchCurrent: () => void;
  refetchPast: () => void;
  refetchAll: () => void;
  markPastSessionsRequested: () => void;
}

// Create context with a default value
const GameSessionsContext = createContext<GameSessionsContextType | null>(null);

// Custom hook to use the GameSessions context
export const useGameSessions = (options?: { fetchCurrent?: boolean; fetchPast?: boolean }) => {
  const context = useContext(GameSessionsContext);

  if (!context) {
    throw new Error("useGameSessions must be used within a GameSessionsProvider");
  }

  // Determine what to fetch based on options
  useEffect(() => {
    if (options?.fetchCurrent) {
      context.refetchCurrent();
    }
    if (options?.fetchPast) {
      // Mark that past sessions were requested for lazy loading
      context.markPastSessionsRequested();
      context.refetchPast();
    }
  }, [options?.fetchCurrent, options?.fetchPast, context]);

  return context;
};

// Provider component
export const GameSessionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [currentSession, setCurrentSession] = useState<GameSession | null>(null);
  const [pastSessions, setPastSessions] = useState<GameSession[]>([]);
  const [gameSessions, setGameSessions] = useState<GameSession[]>([]);
  const { loading, startLoading, stopLoading } = useLoadingState(false);

  // Fetch tracking
  const loadedDataRef = useRef({
    current: false,
    past: false,
  });

  // Track if past sessions were ever requested (for lazy loading)
  const pastSessionsRequestedRef = useRef(false);

  // Prevent fetching multiple times simultaneously
  const isFetchingRef = useRef({
    current: false,
    past: false,
  });

  // Import session mutations
  const { createGameSession, updateGameSession } = useSessionMutations();
  const { updateGameDuration } = useSessionDuration();

  // Log when provider is initialized
  useEffect(() => {
    console.log("🌎 GameSessionsProvider initialized");
  }, []);

  // Setup realtime subscription for current session changes
  useEffect(() => {
    console.log("🔌 Setting up game sessions subscription (Provider)");

    const channel = supabase
      .channel("game_sessions_changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "game_sessions" },
        (payload: {
          eventType: string;
          new: { status?: "played" | "cancelled" | "archived" | "scheduled" } | null;
          old: any;
        }) => {
          console.log("📡 Game session change detected (Provider)", payload.eventType);

          // If a session status changed to played/cancelled/archived, refresh both
          if (
            payload.new &&
            (payload.new.status === "played" ||
              payload.new.status === "cancelled" ||
              payload.new.status === "archived")
          ) {
            console.log("🔄 Game status changed to non-active, refreshing current session");
            fetchCurrentSession();
            // Only fetch past sessions if they were previously requested (lazy loading)
            if (pastSessionsRequestedRef.current) {
              console.log("🔄 Past sessions were requested before, refreshing them too");
              fetchPastSessions();
            }
          } else {
            // For other changes, only refresh current session
            console.log("🔄 Refreshing current session only");
            fetchCurrentSession();
          }
        }
      )
      .on("postgres_changes", { event: "*", schema: "public", table: "player_signups" }, () => {
        console.log("📡 Player signup change detected (Provider)");
        // For player signups, only current session needs refresh
        fetchCurrentSession();
      })
      .subscribe();

    return () => {
      console.log("🧹 Cleaning up game sessions subscription (Provider)");
      supabase.removeChannel(channel);
    };
  }, []);

  // Fetch current active session
  const fetchCurrentSession = async () => {
    if (isFetchingRef.current.current) {
      console.log("⏳ Current session fetch already in progress");
      return;
    }

    isFetchingRef.current.current = true;
    startLoading();

    try {
      console.log("📡 Fetching current session...");
      const { data, error } = await supabase
        .from("game_sessions")
        .select("*")
        .eq("status", "scheduled")
        .order("date", { ascending: false })
        .limit(1);

      if (error) throw error;

      const mappedSession =
        data && data.length > 0
          ? {
            id: data[0].id,
            date: new Date(data[0].date),
            signupOpensAt: new Date(data[0].signup_opens_at),
            isSignupOpen: data[0].is_signup_open || false,
            isTeamGenerated: data[0].is_team_generated || false,
            status: (data[0].status || "scheduled") as GameSession["status"],
            players: [],
            confirmedPlayers: [],
            reservePlayers: [],
            duration_minutes: data[0].duration_minutes || undefined,
          }
          : null;

      console.log("Current session data:", mappedSession ? "Found" : "None");

      // Always set the current session, even if null
      setCurrentSession(mappedSession);

      // Update gameSessions to maintain current and past sessions
      setGameSessions((prev) => {
        // Remove any existing scheduled sessions
        const withoutCurrent = prev.filter((s) => s.status !== "scheduled");
        // Add the new current session if it exists
        return mappedSession ? [mappedSession, ...withoutCurrent] : withoutCurrent;
      });

      loadedDataRef.current.current = true;
    } catch (error) {
      console.error("❌ Error fetching current session:", error);
      toast({
        title: "Error",
        description: "Failed to fetch current session",
        variant: "destructive",
      });
    } finally {
      stopLoading();
      isFetchingRef.current.current = false;
    }
  };

  // Fetch past sessions
  const fetchPastSessions = async () => {
    if (isFetchingRef.current.past) {
      console.log("⏳ Past sessions fetch already in progress");
      return;
    }

    isFetchingRef.current.past = true;
    startLoading();

    try {
      console.log("📡 Fetching past sessions...");
      const { data, error } = await supabase
        .from("game_sessions")
        .select("*")
        .in("status", ["played", "cancelled", "archived"])
        .order("date", { ascending: false });

      if (error) throw error;

      const mappedPastSessions = data
        ? data.map((session) => ({
          id: session.id,
          date: new Date(session.date),
          signupOpensAt: new Date(session.signup_opens_at),
          isSignupOpen: session.is_signup_open || false,
          isTeamGenerated: session.is_team_generated || false,
          status: (session.status || "scheduled") as GameSession["status"],
          players: [],
          confirmedPlayers: [],
          reservePlayers: [],
          duration_minutes: session.duration_minutes || undefined,
        }))
        : [];

      setPastSessions(mappedPastSessions);

      // Update gameSessions to maintain current and past sessions
      setGameSessions((prev) => {
        // Keep only current session if it exists
        const currentOnly = prev.filter((s) => s.status === "scheduled");
        return [...currentOnly, ...mappedPastSessions];
      });

      loadedDataRef.current.past = true;

      // Automatically fetch team data for past sessions since most use cases need it
      if (mappedPastSessions.length > 0) {
        await fetchTeamsForSessions(mappedPastSessions);
      }
    } catch (error) {
      console.error("❌ Error fetching past sessions:", error);
      toast({
        title: "Error",
        description: "Failed to fetch past sessions",
        variant: "destructive",
      });
    } finally {
      stopLoading();
      isFetchingRef.current.past = false;
    }
  };

  // Fetch teams for sessions
  const fetchTeamsForSessions = async (sessions: GameSession[]) => {

    try {
      const sessionIds = sessions.map((session) => session.id).filter(Boolean);

      if (!sessionIds.length) return;

      console.log("📡 Fetching teams for sessions:", sessionIds);
      const { data, error } = await supabase
        .from("teams")
        .select(
          `
          id,
          name,
          average_rating,
          game_session_id,
          team_players(
            player_id,
            player:players(id, name, jersey_number, rating)
          )
        `
        )
        .in("game_session_id", sessionIds);

      if (error) throw error;

      if (data && data.length > 0) {
        console.log("📦 Processing teams data...");
        const teamsBySession: Record<string, Team[]> = {};

        data.forEach((team) => {
          if (!team.game_session_id) return;

          if (!teamsBySession[team.game_session_id]) {
            teamsBySession[team.game_session_id] = [];
          }

          const players = team.team_players
            .map((tp: any) =>
              tp.player
                ? {
                  id: tp.player.id,
                  name: tp.player.name,
                  jerseyNumber: tp.player.jersey_number,
                  rating: tp.player.rating || 0,
                  status: "in" as const,
                  is_active: true,
                }
                : null
            )
            .filter(Boolean);

          teamsBySession[team.game_session_id].push({
            id: team.id,
            name: team.name || `Team ${teamsBySession[team.game_session_id].length + 1}`,
            averageRating: team.average_rating || 0,
            players: players,
          });
        });

        // Update past sessions with team data
        const updatedSessions = sessions.map((session) => {
          if (session.id && teamsBySession[session.id]) {
            const sessionTeams = teamsBySession[session.id];
            return {
              ...session,
              teams: sessionTeams,
              teamCount: sessionTeams.length,
              playerCount: sessionTeams.reduce(
                (total, team) => total + (team.players?.length || 0),
                0
              ),
            };
          }
          return session;
        });

        setPastSessions(updatedSessions);

        // Update all game sessions
        setGameSessions((prev) =>
          prev.map((session) => {
            const updatedSession = updatedSessions.find((s) => s.id === session.id);
            return updatedSession || session;
          })
        );


      }
    } catch (error) {
      console.error("❌ Error fetching teams:", error);
    }
  };

  // Mark that past sessions were requested (for lazy loading)
  const markPastSessionsRequested = () => {
    pastSessionsRequestedRef.current = true;
  };

  // Refetch functions for use by consumers
  const refetchCurrent = () => {
    if (!loadedDataRef.current.current) {
      fetchCurrentSession();
    }
  };

  const refetchPast = () => {
    if (!loadedDataRef.current.past) {
      fetchPastSessions();
    }
  };

  const refetchAll = () => {
    loadedDataRef.current = {
      current: false,
      past: false,
    };
    fetchCurrentSession();
    fetchPastSessions();
  };

  // Context value
  const value = {
    currentSession,
    pastSessions,
    gameSessions,
    loading,
    createGameSession,
    updateGameSession,
    updateGameDuration,
    refetchCurrent,
    refetchPast,
    refetchAll,
    markPastSessionsRequested,
  };

  return <GameSessionsContext.Provider value={value}>{children}</GameSessionsContext.Provider>;
};
