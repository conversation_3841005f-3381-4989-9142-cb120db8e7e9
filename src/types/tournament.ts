import { Team } from ".";

export interface TournamentMatch {
  id: string;
  teamA: Team;
  teamB: Team;
  goalsA: number | null;
  goalsB: number | null;
  isCompleted: boolean;
  startTime?: Date;
  endTime?: Date;
  matchNumber: number;
  field?: string;
  timeSlot?: number;
}

export interface TournamentConfig {
  totalDurationMinutes: number;
  breakBetweenMatchesMinutes: number;
  matchDurationMinutes: number;
  numberOfTeams: number;
  numberOfFields: number;
}

export interface TournamentStanding {
  team: Team;
  played: number;
  won: number;
  drawn: number;
  lost: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
}

export interface Tournament {
  id?: string;
  gameSessionId: string;
  config: TournamentConfig;
  matches: TournamentMatch[];
  standings: TournamentStanding[];
  currentMatchIndex: number;
  isActive: boolean;
  startTime?: Date;
  endTime?: Date;
}
