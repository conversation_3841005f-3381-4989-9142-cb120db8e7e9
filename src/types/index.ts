// Extend existing types
export interface Player {
  id?: string;
  name: string;
  jerseyNumber?: number;
  rating: number;
  status: "in" | "out" | "pending";
  signupTimestamp?: Date;
  role?: PlayerRole;
  reserveNumber?: number;
  is_active: boolean;
}

export type PlayerRole =
  | "goalkeeper"
  | "defender"
  | "midfielder"
  | "striker"
  | "allrounder"
  | undefined;

export interface GameSession {
  id?: string;
  date: Date;
  signupOpensAt: Date;
  isSignupOpen: boolean;
  isTeamGenerated: boolean;
  status: "scheduled" | "played" | "cancelled" | "archived";
  players?: Player[];
  confirmedPlayers?: Player[];
  reservePlayers?: Player[];
  // Added properties for history display
  playerCount?: number;
  teamCount?: number;
  teams?: Team[];
  // Added property for duration
  duration_minutes?: number;
}

export interface Team {
  id?: string;
  name: string;
  averageRating: number;
  players: Player[];
  color?: string | number;
}

// Add admin credentials
export const ADMIN_CREDENTIALS = {
  username: "admin",
  password: "interNRW2025",
};

export interface MVPVotingPeriod {
  id: string;
  game_session_id: string;
  created_at: string;
  is_open: boolean;
  ends_at: string;
}

export interface MVPVote {
  id: string;
  voting_period_id: string;
  player_id: string;
  voter_device_id: string;
  created_at: string;
}
