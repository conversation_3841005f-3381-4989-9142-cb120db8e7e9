import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { vi } from "vitest";

// Define a custom render function that includes providers
export function renderWithProviders(ui: ReactElement, options?: Omit<RenderOptions, "wrapper">) {
  const AllProviders = ({ children }: { children: React.ReactNode }) => {
    return (
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        {children}
        <Toaster />
      </ThemeProvider>
    );
  };

  return render(ui, { wrapper: AllProviders, ...options });
}

// Mock for Supabase client
export const mockSupabaseClient = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  single: vi.fn(),
  data: null,
  error: null,
};

// Helper to create mock teams
export const createMockTeams = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `team-${i + 1}`,
    name: `Team ${String.fromCharCode(65 + i)}`, // Team A, Team B, etc.
    players: [],
    is_active: true,
    rating: 80 + i,
    status: "in",
  }));
};

// Helper to create mock tournament matches with customizable properties
export const createMockTournamentMatches = (
  teams: any[],
  count: number,
  options?: {
    completedMatches?: number; // How many matches should be marked as completed
    goalsRange?: [number, number]; // Range for random goals [min, max]
    startTimeBase?: Date; // Base time for the first match
    matchDurationMinutes?: number; // Duration of each match in minutes
    specificGoals?: Array<{ index: number; goalsA: number; goalsB: number }>; // Specific goals for specific matches
  }
) => {
  const {
    completedMatches = 0,
    goalsRange = [0, 3],
    startTimeBase = new Date("2023-01-01T10:00:00"),
    matchDurationMinutes = 10,
    specificGoals = [],
  } = options || {};

  return Array.from({ length: count }, (_, i) => {
    const teamAIndex = i % teams.length;
    const teamBIndex = (i + 1) % teams.length;

    // Calculate if this match should be completed
    const isCompleted = i < completedMatches;

    // Check if specific goals are defined for this match
    const specificGoalEntry = specificGoals.find((entry) => entry.index === i);

    // Generate goals based on completion status and specific goals
    let goalsA = null;
    let goalsB = null;

    if (specificGoalEntry) {
      // Use specific goals if defined
      goalsA = specificGoalEntry.goalsA;
      goalsB = specificGoalEntry.goalsB;
    } else if (isCompleted) {
      // Generate random goals if the match is completed and no specific goals
      goalsA = Math.floor(Math.random() * (goalsRange[1] - goalsRange[0] + 1)) + goalsRange[0];
      goalsB = Math.floor(Math.random() * (goalsRange[1] - goalsRange[0] + 1)) + goalsRange[0];
    }

    // Calculate start and end times
    const startTime = isCompleted
      ? new Date(startTimeBase.getTime() + i * (matchDurationMinutes + 2) * 60 * 1000) // Add 2 minutes break between matches
      : undefined;
    const endTime =
      isCompleted && startTime
        ? new Date(startTime.getTime() + matchDurationMinutes * 60 * 1000)
        : undefined;

    return {
      id: `match-${i + 1}`,
      teamA: teams[teamAIndex],
      teamB: teams[teamBIndex],
      goalsA,
      goalsB,
      isCompleted,
      matchNumber: i + 1,
      field: `Feld ${Math.floor(i / 2) + 1}`,
      timeSlot: Math.floor(i / 2) + 1,
      startTime,
      endTime,
    };
  });
};

// Keep the old function name for backward compatibility
export const createMockMatches = createMockTournamentMatches;

// Helper to create a mock tournament with customizable properties
export const createMockTournament = (
  teams: any[],
  matchCount: number,
  options?: {
    id?: string;
    gameSessionId?: string;
    totalDurationMinutes?: number;
    breakBetweenMatchesMinutes?: number;
    matchDurationMinutes?: number;
    numberOfFields?: number;
    isActive?: boolean;
    startTime?: Date;
    endTime?: Date;
    currentMatchIndex?: number;
    // Optionen für die Matches können entweder als vorgefertigte Matches oder als Konfiguration übergeben werden
    matches?: any[]; // Vorgefertigte Matches, wenn vorhanden
    matchOptions?: {
      // Konfiguration für die Matches, wenn keine vorgefertigten Matches vorhanden sind
      completedMatches?: number;
      goalsRange?: [number, number];
      startTimeBase?: Date;
      specificGoals?: Array<{ index: number; goalsA: number; goalsB: number }>;
    };
  }
) => {
  const {
    id = "tournament-1",
    gameSessionId = "session-1",
    totalDurationMinutes = 60,
    breakBetweenMatchesMinutes = 2,
    matchDurationMinutes = 10,
    numberOfFields = 1,
    isActive = true,
    startTime = new Date(),
    endTime = undefined,
    currentMatchIndex = 0,
    matches: providedMatches = null,
    matchOptions = {},
  } = options || {};

  // Verwende entweder die bereitgestellten Matches oder erstelle neue
  const matches =
    providedMatches ||
    createMockTournamentMatches(teams, matchCount, {
      completedMatches: matchOptions.completedMatches || (matchCount > 1 ? 1 : 0),
      matchDurationMinutes,
      startTimeBase: matchOptions.startTimeBase || new Date("2023-01-01T10:00:00"),
      goalsRange: matchOptions.goalsRange,
      specificGoals: matchOptions.specificGoals || [],
    });

  return {
    id,
    gameSessionId,
    config: {
      totalDurationMinutes,
      breakBetweenMatchesMinutes,
      matchDurationMinutes,
      numberOfTeams: teams.length,
      numberOfFields,
    },
    matches,
    standings: teams.map((team) => ({
      team,
      played: 0,
      won: 0,
      drawn: 0,
      lost: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      goalDifference: 0,
      points: 0,
    })),
    currentMatchIndex,
    isActive,
    startTime,
    endTime,
  };
};
