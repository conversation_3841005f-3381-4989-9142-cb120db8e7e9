import { supabase } from "@/integrations/supabase/client";
import { notifyMVPVotingOpened, notifyMVPVotingClosed } from "./whatsappService";

// Interface for MVP winner data returned to the client
export interface MVPWinner {
  playerName: string;
  jerseyNumber?: number | null;
  playerId?: string;
  matchId: string;
  voteCount?: number;
}

// Configuration constants
const VOTING_PERIOD_HOURS = 48;
const STALE_PERIOD_DAYS = 2;

/**
 * Calculate voting period end time
 */
const calculateEndTime = (hours: number = VOTING_PERIOD_HOURS): string => {
  const endsAt = new Date();
  endsAt.setHours(endsAt.getHours() + hours);
  return endsAt.toISOString();
};

/**
 * Clean up stale MVP voting periods
 * Closes any voting periods that are older than STALE_PERIOD_DAYS
 * except for the current game session
 */
const cleanupStaleVotingPeriods = async (gameSessionId: string): Promise<void> => {
  const twoDaysAgo = new Date();
  twoDaysAgo.setDate(twoDaysAgo.getDate() - STALE_PERIOD_DAYS);

  // Close all open voting periods that are older than STALE_PERIOD_DAYS
  // and not associated with the current game
  await supabase
    .from("mvp_voting_periods")
    .update({ is_open: false })
    .lt("created_at", twoDaysAgo.toISOString())
    .eq("is_open", true)
    .neq("game_session_id", gameSessionId);
};

/**
 * Create or reopen an MVP voting period for a game session
 * If a period exists but is closed, it will be reopened
 * If no period exists, a new one will be created
 */
export const createOrReopenVotingPeriod = async (gameSessionId: string): Promise<boolean> => {
  try {
    // First cleanup any stale voting periods from other games
    await cleanupStaleVotingPeriods(gameSessionId);

    // Check if a voting period already exists for this game
    const { data: existingPeriod, error: checkError } = await supabase
      .from("mvp_voting_periods")
      .select(
        `
        id, 
        is_open,
        game_sessions (date)
      `
      )
      .eq("game_session_id", gameSessionId)
      .maybeSingle();

    if (checkError) throw checkError;

    const endsAt = calculateEndTime();
    const gameDate = existingPeriod?.game_sessions?.date;

    if (!existingPeriod) {
      // Create new voting period if none exists
      const { data: newPeriod, error: createError } = await supabase
        .from("mvp_voting_periods")
        .insert({
          game_session_id: gameSessionId,
          is_open: true,
          ends_at: endsAt,
        })
        .select("game_sessions (date)")
        .single();

      if (createError) throw createError;

      // Notify users about new voting period
      await notifyMVPVotingOpened(new Date(newPeriod.game_sessions.date));
      return true;
    }

    if (!existingPeriod.is_open) {
      // Reopen existing period if it was closed
      const { error: reopenError } = await supabase
        .from("mvp_voting_periods")
        .update({ is_open: true, ends_at: endsAt })
        .eq("id", existingPeriod.id);

      if (reopenError) throw reopenError;

      // Notify users about reopened voting
      await notifyMVPVotingOpened(new Date(gameDate));
    }

    return true;
  } catch (error) {
    console.error("Error in MVP voting period operation:", error);
    return false;
  }
};

/**
 * Close a voting period and calculate MVP winners
 * This will:
 * 1. Close the voting period
 * 2. Calculate winners (players with highest votes)
 * 3. Save winners to database
 * 4. Send notification about winners
 */
export const closeVotingPeriodAndCalculateMVP = async (
  votingPeriodId: string
): Promise<boolean> => {
  try {
    // Get voting period details including game date for notification
    const { data: votingPeriod, error: periodError } = await supabase
      .from("mvp_voting_periods")
      .select(
        `
        id,
        game_session_id,
        game_sessions (date)
      `
      )
      .eq("id", votingPeriodId)
      .single();

    if (periodError) throw periodError;

    // Close the voting period
    const { error: updateError } = await supabase
      .from("mvp_voting_periods")
      .update({ is_open: false })
      .eq("id", votingPeriodId);

    if (updateError) throw updateError;

    // Get vote results sorted by vote count
    const { data: voteResults, error: voteError } = await supabase
      .from("mvp_vote_results")
      .select("player_id, player_name, vote_count")
      .eq("voting_period_id", votingPeriodId)
      .order("vote_count", { ascending: false });

    if (voteError) throw voteError;
    if (!voteResults?.length) return true; // No votes cast

    // Find all players with the highest vote count (could be multiple in case of tie)
    const maxVotes = voteResults[0].vote_count;
    const winners = voteResults.filter((result) => result.vote_count === maxVotes);

    // Prepare winner records for database
    const winnerRecords = winners.map((winner) => ({
      game_session_id: votingPeriod.game_session_id,
      player_id: winner.player_id,
      voting_period_id: votingPeriodId,
      vote_count: winner.vote_count,
    }));

    // Save winners to database (delete any existing winners first)
    await supabase.from("mvp_winners").delete().eq("voting_period_id", votingPeriodId);
    const { error: insertError } = await supabase.from("mvp_winners").insert(winnerRecords);

    if (insertError) throw insertError;

    // Send notification about winners
    await notifyMVPVotingClosed(
      new Date(votingPeriod.game_sessions.date),
      winners.map((w) => ({ playerName: w.player_name, matchId: votingPeriod.game_session_id }))
    );

    return true;
  } catch (error) {
    console.error("Error in MVP voting close operation:", error);
    return false;
  }
};

/**
 * Fetch MVP winners for a specific game session
 * Returns an empty array if no winners exist or if there was an error
 */
export const fetchMvpWinner = async (gameSessionId: string): Promise<MVPWinner[]> => {
  try {
    // First get the voting period ID for this game
    const { data: votingPeriod, error: periodError } = await supabase
      .from("mvp_voting_periods")
      .select("id")
      .eq("game_session_id", gameSessionId)
      .maybeSingle();

    if (periodError) throw periodError;
    if (!votingPeriod) return [];

    // Then get all winners for this voting period
    const { data: winners, error: winnersError } = await supabase
      .from("mvp_winners")
      .select(
        `
        player_id,
        vote_count,
        players (name, jersey_number)
      `
      )
      .eq("voting_period_id", votingPeriod.id);

    if (winnersError) throw winnersError;
    if (!winners?.length) return [];

    // Transform database records to MVPWinner interface
    return winners.map((winner) => ({
      playerName: winner.players.name,
      jerseyNumber: winner.players.jersey_number,
      playerId: winner.player_id,
      matchId: gameSessionId,
      voteCount: winner.vote_count,
    }));
  } catch (error) {
    console.error("Error fetching MVP winners:", error);
    return [];
  }
};

/**
 * Fetch MVP winners for multiple game sessions
 * Returns a flattened array of all winners
 */
export const fetchMvpWinners = async (gameSessionIds: string[]): Promise<MVPWinner[]> => {
  const winners = await Promise.all(gameSessionIds.map(fetchMvpWinner));
  return winners.flat();
};
