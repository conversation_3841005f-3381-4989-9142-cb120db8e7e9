import { supabase } from "@/integrations/supabase/client";

interface AppSettings {
  key: string;
  value: string;
}

export const pinService = {
  async getPin(): Promise<AppSettings | null> {
    const { data, error } = await supabase
      .from("app_settings")
      .select("*")
      .eq("key", "app_pin")
      .single<AppSettings>();

    if (error && error.code !== "PGRST116") {
      throw new Error(`<PERSON><PERSON> beim Abrufen des PINs: ${error.message}`);
    }

    return data;
  },

  async updatePin(pin: string): Promise<void> {
    const existingPin = await this.getPin();

    let result;

    if (existingPin) {
      result = await supabase.from("app_settings").update({ value: pin }).eq("key", "app_pin");
    } else {
      result = await supabase.from("app_settings").insert({ key: "app_pin", value: pin });
    }

    if (result.error) {
      throw new Error(`<PERSON><PERSON> beim <PERSON> des PINs: ${result.error.message}`);
    }
  },
};
