import { supabase } from "@/integrations/supabase/client";
import { Tables } from "@/integrations/supabase/types";

export const adminService = {
  async getAdminUser(username: string): Promise<Tables<"admin_users"> | null> {
    try {
      const { data, error } = await supabase
        .from("admin_users")
        .select("*")
        .eq("username", username)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error fetching admin user:", error);
      return null;
    }
  },

  async verifyAdminCredentials(username: string, password: string): Promise<boolean> {
    try {
      const adminUser = await this.getAdminUser(username);
      if (!adminUser) return false;

      // Note: In production, you should use proper password hashing
      return adminUser.password_hash === password;
    } catch (error) {
      console.error("Error verifying admin credentials:", error);
      return false;
    }
  },
};
