import { supabase } from "@/integrations/supabase/client";
import { MatchResult } from "@/types/match-results";

export async function getMatchResultsForSession(gameSessionId: string): Promise<MatchResult[]> {
  const { data, error } = await supabase
    .from("match_results")
    .select("*")
    .eq("game_session_id", gameSessionId)
    .order("match_order", { ascending: true });

  if (error) throw error;
  return data as MatchResult[];
}

export async function addMatchResult(
  result: Omit<MatchResult, "id" | "created_at" | "updated_at">
): Promise<MatchResult> {
  const { data, error } = await supabase.from("match_results").insert(result).select().single();

  if (error) throw error;
  return data as MatchResult;
}

export async function updateMatchResult(
  id: string,
  updates: Partial<MatchResult>
): Promise<MatchResult> {
  const { data, error } = await supabase
    .from("match_results")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data as MatchR<PERSON>ult;
}

export async function deleteMatchResult(id: string): Promise<void> {
  const { error } = await supabase.from("match_results").delete().eq("id", id);

  if (error) throw error;
}
