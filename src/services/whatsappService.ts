import { Player, GameSession, Team } from "@/types";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useEffect } from "react";
import { MVPWinner } from "./mvpService";

// Determine whether we're in development mode or production
const DEV_MODE = import.meta.env.DEV;

// WhatsApp API token from environment variables
const WHATSAPP_API_TOKEN = import.meta.env.VITE_WHATSAPP_API_TOKEN;

// Global state for WhatsApp notifications status
let isWhatsAppNotificationsEnabled: boolean | null = null;

// Initialize the subscription to settings changes
let subscriptionInitialized = false;

/**
 * Initialize subscription to app_settings changes
 * This should be called once during application initialization
 */
export function initializeWhatsAppSettingsSubscription() {
  if (subscriptionInitialized) return;

  // Get initial value
  fetchWhatsAppNotificationSetting();

  // Set up subscription for changes
  try {
    const subscription = supabase
      .channel("app_settings_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "app_settings",
          filter: "key=eq.whatsapp_notifications_enabled",
        },
        (payload) => {
          // Update our global state when the setting changes
          const newValue = payload.new && (payload.new as any).value;
          if (newValue !== undefined) {
            isWhatsAppNotificationsEnabled = newValue === "true";
            console.log("WhatsApp notification setting updated:", isWhatsAppNotificationsEnabled);
          }
        }
      )
      .subscribe();

    subscriptionInitialized = true;

    // For cleanup if needed (e.g., in React components)
    return () => {
      subscription.unsubscribe();
      subscriptionInitialized = false;
    };
  } catch (error) {
    console.error("Error setting up WhatsApp notification subscription:", error);
    subscriptionInitialized = true; // Mark as initialized to prevent repeated attempts
    return () => {
      subscriptionInitialized = false;
    };
  }
}

/**
 * React hook to subscribe to WhatsApp notification setting changes
 * Use this in components that need to react to setting changes
 */
export function useWhatsAppNotificationSetting() {
  useEffect(() => {
    return initializeWhatsAppSettingsSubscription();
  }, []);

  return isWhatsAppNotificationsEnabled;
}

/**
 * Fetch the current WhatsApp notification setting from the database
 * This is called on startup and when the subscription isn't initialized yet
 */
async function fetchWhatsAppNotificationSetting(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("app_settings")
      .select("value")
      .eq("key", "whatsapp_notifications_enabled")
      .single();

    if (error) {
      console.error("Error fetching WhatsApp notification settings:", error);
      if (isWhatsAppNotificationsEnabled === null) {
        isWhatsAppNotificationsEnabled = false; // Default value on error
      }
      return isWhatsAppNotificationsEnabled;
    }

    isWhatsAppNotificationsEnabled = data?.value === "true";
    return isWhatsAppNotificationsEnabled;
  } catch (error) {
    console.error("Error checking WhatsApp notification settings:", error);
    if (isWhatsAppNotificationsEnabled === null) {
      isWhatsAppNotificationsEnabled = false; // Default value on error
    }
    return isWhatsAppNotificationsEnabled;
  }
}

/**
 * Check if WhatsApp notifications are enabled
 * @returns Promise<boolean> Whether notifications are enabled
 */
export async function areWhatsAppNotificationsEnabled(): Promise<boolean> {
  // If we haven't initialized the subscription or don't have a value yet, fetch it directly
  if (isWhatsAppNotificationsEnabled === null || !subscriptionInitialized) {
    return fetchWhatsAppNotificationSetting();
  }

  // Otherwise return the current state
  return isWhatsAppNotificationsEnabled;
}

/**
 * Send a message to the WhatsApp service
 * @param message The message to send
 * @returns Response from the API
 */
export async function sendWhatsAppMessage(message: string) {
  try {
    // Initialize subscription if not already done
    if (!subscriptionInitialized) {
      initializeWhatsAppSettingsSubscription();
    }

    // Check if WhatsApp notifications are enabled
    const notificationsEnabled = await areWhatsAppNotificationsEnabled();
    if (!notificationsEnabled) {
      console.log("WhatsApp notifications are disabled. Message not sent:", message);
      return { success: false, reason: "notifications_disabled" };
    }

    // Always use DEV_MODE for testMode
    const devMode = DEV_MODE;

    // Check if the API token is available
    if (!WHATSAPP_API_TOKEN) {
      console.error("WhatsApp API token not found in environment variables");
      return { success: false, reason: "missing_api_token" };
    }

    const response = await fetch("https://wa.internrw.de/.netlify/functions/internrw-wa-send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${WHATSAPP_API_TOKEN}`,
      },
      body: JSON.stringify({
        message,
        testMode: devMode,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to send WhatsApp message: ${response.status} ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error sending WhatsApp message:", error);
    throw error;
  }
}

/**
 * Send a notification when a player signs up for a game
 * @param player The player who signed up
 * @returns Response from the API
 */
export async function notifyPlayerSignup(player: Player) {
  const message = `✅ *${player.name}* hat sich zum Spiel angemeldet.`;
  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when a player signs off from a game
 * @param player The player who signed off
 * @returns Response from the API
 */
export async function notifyPlayerSignoff(player: Player) {
  const message = `❌ *${player.name}* hat sich vom Spiel abgemeldet.`;
  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when game registration opens
 * @param session The game session that was opened for registration
 * @returns Response from the API
 */
export async function notifyGameRegistrationOpened(session: GameSession) {
  const dateStr = format(session.date, "EEEE, dd. MMMM yyyy", { locale: de });
  const timeStr = format(session.date, "HH:mm", { locale: de });

  const message = `🟢 *ANMELDUNG GEÖFFNET*\n\nDie Anmeldung für das Spiel am *${dateStr}* um *${timeStr} Uhr* ist jetzt geöffnet. Bitte meldet euch rechtzeitig an oder ab!`;
  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when game registration closes
 * @param session The game session that was closed for registration
 * @returns Response from the API
 */
export async function notifyGameRegistrationClosed(session: GameSession) {
  const dateStr = format(session.date, "EEEE, dd. MMMM yyyy", { locale: de });
  const timeStr = format(session.date, "HH:mm", { locale: de });

  const message = `🔴 *ANMELDUNG GESCHLOSSEN*\n\nDie Anmeldung für das Spiel am *${dateStr}* um *${timeStr} Uhr* wurde geschlossen. Keine weiteren An- oder Abmeldungen möglich.`;
  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when teams are generated and saved
 * @param session The game session
 * @param teams The generated teams
 * @returns Response from the API
 */
export async function notifyTeamsGenerated(session: GameSession, teams: Team[]) {
  const dateStr = format(session.date, "EEEE, dd. MMMM yyyy", { locale: de });
  const timeStr = format(session.date, "HH:mm", { locale: de });

  let message = `⚽ *TEAMS GENERIERT*\n\nDie Teams für das Spiel am *${dateStr}* um *${timeStr} Uhr* wurden erstellt:\n\n`;

  teams.forEach((team, index) => {
    message += `*Team ${team.name}*:\n`;
    team.players.forEach((player) => {
      message += `- ${player.name}\n`;
    });
    if (index < teams.length - 1) {
      message += "\n";
    }
  });

  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when MVP voting opens for a game session
 * @param gameDate The date of the game
 * @returns Response from the API
 */
export async function notifyMVPVotingOpened(gameDate: Date) {
  const dateStr = format(gameDate, "EEEE, dd. MMMM yyyy", { locale: de });

  const message = `🏆 *MVP VOTING GESTARTET*\n\nDie MVP-Abstimmung für das Spiel vom *${dateStr}* ist jetzt eröffnet!\n\nWer war heute der wertvollste Spieler auf dem Platz? Gib jetzt deine Stimme ab und würdige die herausragende Leistung deiner Mitspieler! 🌟\n\nDeine Stimme zählt - mach mit bei der MVP-Wahl! 🗳️`;
  return sendWhatsAppMessage(message);
}

/**
 * Send a notification when MVP voting closes and announce the winner(s)
 * @param gameDate The date of the game
 * @param winners Array of MVP winners
 * @returns Response from the API
 */
export async function notifyMVPVotingClosed(gameDate: Date, winners: MVPWinner[]) {
  const dateStr = format(gameDate, "EEEE, dd. MMMM yyyy", { locale: de });

  let message = `🎉 *MVP VOTING BEENDET*\n\nDie Stimmen für das Spiel vom *${dateStr}* sind ausgezählt!\n\n`;

  if (winners.length === 1) {
    message += `👑 *MVP des Spiels*:\n*${winners[0].playerName}*\n\nHerzlichen Glückwunsch zu dieser herausragenden Leistung! 🌟`;
  } else if (winners.length > 1) {
    message += `👑 *MVPs des Spiels*:\n`;
    winners.forEach((winner) => {
      message += `*${winner.playerName}*\n`;
    });
    message += `\nHerzlichen Glückwunsch an alle Gewinner für ihre außergewöhnlichen Leistungen! 🌟`;
  }

  return sendWhatsAppMessage(message);
}
