import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  saveTournament,
  getTournamentByGameSessionId,
  deleteTournamentMatch,
  addTournamentMatch,
  updateTournamentStatus,
  updateTournamentMatchResult,
} from "../tournamentDbService";
import { supabase } from "@/integrations/supabase/client";
import { TournamentMatch } from "@/types/tournament";
import { toast } from "@/components/ui/use-toast";
import { createMockTeams, createMockTournament } from "@/test/testUtils";

// Mock dependencies
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    or: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    maybeSingle: vi.fn().mockReturnThis(),
  },
}));

vi.mock("@/components/ui/use-toast", () => ({
  toast: vi.fn(),
}));

describe("tournamentDbService", () => {
  // Create mock teams using the helper function
  const mockTeams = createMockTeams(3).map((team) => ({
    id: team.id,
    name: team.name,
    players: [],
    averageRating: team.rating || 0,
  }));

  // Create a mock tournament with our customized matches
  const mockTournament = createMockTournament(mockTeams, 3, {
    id: "tournament-1",
    gameSessionId: "session-1",
    totalDurationMinutes: 60,
    breakBetweenMatchesMinutes: 2,
    matchDurationMinutes: 10,
    numberOfFields: 1,
    isActive: true,
    currentMatchIndex: 0,
    matchOptions: {
      completedMatches: 2,
      specificGoals: [
        { index: 0, goalsA: 3, goalsB: 1 },
        { index: 1, goalsA: 2, goalsB: 0 },
      ],
    },
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  describe("saveTournament", () => {
    it("should save a tournament successfully", async () => {
      // Mock tournament insert response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: { id: "tournament-1" },
              error: null,
            }),
          }) as any
      );

      // Mock match insert responses
      mockTournament.matches.forEach(() => {
        vi.mocked(supabase.from).mockImplementationOnce(
          () =>
            ({
              insert: vi.fn().mockReturnThis(),
              select: vi.fn().mockReturnThis(),
              single: vi.fn().mockResolvedValue({
                data: { id: "match-id" },
                error: null,
              }),
            }) as any
        );
      });

      // Mock standings insert responses
      mockTournament.standings.forEach(() => {
        vi.mocked(supabase.from).mockImplementationOnce(
          () =>
            ({
              insert: vi.fn().mockReturnThis(),
              select: vi.fn().mockReturnThis(),
              single: vi.fn().mockResolvedValue({
                data: { id: "standing-id" },
                error: null,
              }),
            }) as any
        );
      });

      // Mock tournament update response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await saveTournament(mockTournament);

      // Verify the result
      expect(result).not.toBeNull();
      expect(result?.id).toBe("tournament-1");
      expect(result?.matches.length).toBe(mockTournament.matches.length);
      expect(result?.standings.length).toBe(mockTournament.standings.length);
      expect(result?.isActive).toBe(true);
    });

    it("should handle tournament insert error", async () => {
      // Mock tournament insert error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await saveTournament(mockTournament);

      // Verify the result
      expect(result).toBeNull();
      expect(toast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Fehler",
          variant: "destructive",
        })
      );
    });
  });

  describe("getTournamentByGameSessionId", () => {
    it("should get a tournament by game session ID", async () => {
      // Mock tournament query response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: {
                id: "tournament-1",
                game_session_id: "session-1",
                total_duration_minutes: 60,
                break_between_matches_minutes: 2,
                match_duration_minutes: 10,
                number_of_teams: 3,
                number_of_fields: 1,
                is_active: true,
                start_time: new Date().toISOString(),
                end_time: null,
              },
              error: null,
            }),
          }) as any
      );

      // Mock matches query response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            order: vi.fn().mockResolvedValue({
              data: [
                {
                  id: "match-1",
                  team_a_id: "team-1",
                  team_b_id: "team-2",
                  goals_a: 3,
                  goals_b: 1,
                  is_completed: true,
                  match_number: 1,
                  field: "Feld 1 (Spielrunde 1)",
                  start_time: new Date().toISOString(),
                  end_time: new Date().toISOString(),
                  teams_a: { id: "team-1", name: "Team A", average_rating: 80 },
                  teams_b: { id: "team-2", name: "Team B", average_rating: 81 },
                },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock standings query response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            order: vi.fn().mockResolvedValue({
              data: [
                {
                  id: "standing-1",
                  played: 1,
                  won: 1,
                  drawn: 0,
                  lost: 0,
                  goals_for: 3,
                  goals_against: 1,
                  goal_difference: 2,
                  points: 3,
                  teams: { id: "team-1", name: "Team A", average_rating: 80 },
                },
              ],
              error: null,
            }),
          }) as any
      );

      const result = await getTournamentByGameSessionId("session-1");

      // Verify the result
      expect(result).not.toBeNull();
      expect(result?.id).toBe("tournament-1");
      expect(result?.gameSessionId).toBe("session-1");
      expect(result?.matches.length).toBe(1);
      expect(result?.standings.length).toBe(1);
    });

    it("should return null when tournament is not found", async () => {
      // Mock tournament query response with no data
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await getTournamentByGameSessionId("non-existent-session");

      // Verify the result
      expect(result).toBeNull();
    });
  });

  describe("deleteTournamentMatch", () => {
    it("should delete a tournament match successfully", async () => {
      // Mock delete response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            delete: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock fetch matches for renumbering
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            order: vi.fn().mockResolvedValue({
              data: [
                { id: "match-2", match_number: 2 },
                { id: "match-3", match_number: 3 },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock update match numbers (for each match that needs renumbering)
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await deleteTournamentMatch("tournament-1", "match-1");

      // Verify the result
      expect(result).toBe(true);
    });

    it("should handle delete error", async () => {
      // Mock delete error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            delete: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await deleteTournamentMatch("tournament-1", "match-1");

      // Verify the result
      expect(result).toBe(false);
      expect(toast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Fehler",
          variant: "destructive",
        })
      );
    });
  });

  describe("addTournamentMatch", () => {
    it("should add a tournament match successfully", async () => {
      const mockMatch: TournamentMatch = {
        id: "",
        teamA: mockTeams[0],
        teamB: mockTeams[1],
        goalsA: 0,
        goalsB: 0,
        isCompleted: false,
        matchNumber: 4,
        field: "Feld 1",
        timeSlot: 2,
      };

      // Mock insert response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: { id: "new-match-id" },
              error: null,
            }),
          }) as any
      );

      const result = await addTournamentMatch("tournament-1", mockMatch);

      // Verify the result
      expect(result).not.toBeNull();
      expect(result?.id).toBe("new-match-id");
    });

    it("should handle insert error", async () => {
      const mockMatch: TournamentMatch = {
        id: "",
        teamA: mockTeams[0],
        teamB: mockTeams[1],
        goalsA: 0,
        goalsB: 0,
        isCompleted: false,
        matchNumber: 4,
        field: "Feld 1",
        timeSlot: 2,
      };

      // Mock insert error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await addTournamentMatch("tournament-1", mockMatch);

      // Verify the result
      expect(result).toBeNull();
      expect(toast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Fehler",
          variant: "destructive",
        })
      );
    });
  });

  describe("updateTournamentStatus", () => {
    it("should update tournament status successfully", async () => {
      // Mock update response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await updateTournamentStatus("tournament-1", false, undefined, new Date());

      // Verify the result
      expect(result).toBe(true);
    });

    it("should handle update error", async () => {
      // Mock update error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await updateTournamentStatus("tournament-1", false);

      // Verify the result
      expect(result).toBe(false);
    });
  });

  describe("updateTournamentMatchResult", () => {
    it("should update match result and recalculate standings", async () => {
      // Mock get match response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: {
                tournament_id: "tournament-1",
                team_a_id: "team-1",
                team_b_id: "team-2",
              },
              error: null,
            }),
          }) as any
      );

      // Mock update match response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock get matches for team A standings
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            or: vi.fn().mockResolvedValue({
              data: [
                {
                  goals_a: 3,
                  goals_b: 1,
                  team_a_id: "team-1",
                  team_b_id: "team-2",
                },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock check existing standings for team A
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: { id: "standing-1" },
              error: null,
            }),
          }) as any
      );

      // Mock update standings for team A
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock get matches for team B standings
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            or: vi.fn().mockResolvedValue({
              data: [
                {
                  goals_a: 3,
                  goals_b: 1,
                  team_a_id: "team-1",
                  team_b_id: "team-2",
                },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock check existing standings for team B
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: { id: "standing-2" },
              error: null,
            }),
          }) as any
      );

      // Mock update standings for team B
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await updateTournamentMatchResult("match-1", 3, 1);

      // Verify the result
      expect(result).toBe(true);
    });

    it("should create new standings when none exist", async () => {
      // Mock get match response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: {
                tournament_id: "tournament-1",
                team_a_id: "team-1",
                team_b_id: "team-2",
              },
              error: null,
            }),
          }) as any
      );

      // Mock update match response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock get matches for team A standings
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            or: vi.fn().mockResolvedValue({
              data: [
                {
                  goals_a: 3,
                  goals_b: 1,
                  team_a_id: "team-1",
                  team_b_id: "team-2",
                },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock check existing standings for team A - no standings exist
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock insert standings for team A
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock get matches for team B standings
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            or: vi.fn().mockResolvedValue({
              data: [
                {
                  goals_a: 3,
                  goals_b: 1,
                  team_a_id: "team-1",
                  team_b_id: "team-2",
                },
              ],
              error: null,
            }),
          }) as any
      );

      // Mock check existing standings for team B - no standings exist
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            maybeSingle: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      // Mock insert standings for team B
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            insert: vi.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }) as any
      );

      const result = await updateTournamentMatchResult("match-1", 3, 1);

      // Verify the result
      expect(result).toBe(true);
    });

    it("should handle match query error", async () => {
      // Mock get match error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await updateTournamentMatchResult("match-1", 3, 1);

      // Verify the result
      expect(result).toBe(false);
      expect(toast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Fehler",
          variant: "destructive",
        })
      );
    });

    it("should handle match update error", async () => {
      // Mock get match response
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: {
                tournament_id: "tournament-1",
                team_a_id: "team-1",
                team_b_id: "team-2",
              },
              error: null,
            }),
          }) as any
      );

      // Mock update match error
      vi.mocked(supabase.from).mockImplementationOnce(
        () =>
          ({
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockResolvedValue({
              data: null,
              error: { message: "Database error" },
            }),
          }) as any
      );

      const result = await updateTournamentMatchResult("match-1", 3, 1);

      // Verify the result
      expect(result).toBe(false);
      expect(toast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Fehler",
          variant: "destructive",
        })
      );
    });
  });
});
