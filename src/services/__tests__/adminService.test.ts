import { describe, it, expect, vi, beforeEach } from "vitest";
import { adminService } from "../adminService";
import { supabase } from "@/integrations/supabase/client";
import { PostgrestSingleResponse, PostgrestError } from "@supabase/supabase-js";

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
  },
}));

describe("adminService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getAdminUser", () => {
    it("should return admin user when found", async () => {
      const mockAdminUser = {
        id: "123",
        username: "admin",
        password_hash: "hashed_password",
        created_at: "2024-01-01T00:00:00Z",
      };

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockAdminUser,
          error: null,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.getAdminUser("admin");
      expect(result).toEqual(mockAdminUser);
    });

    it("should return null when admin user is not found", async () => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.getAdminUser("nonexistent");
      expect(result).toBeNull();
    });

    it("should return null when there is an error", async () => {
      const mockError = { message: "Database error" } as PostgrestError;

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: mockError,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.getAdminUser("admin");
      expect(result).toBeNull();
    });
  });

  describe("verifyAdminCredentials", () => {
    it("should return true when credentials match", async () => {
      const mockAdminUser = {
        id: "123",
        username: "admin",
        password_hash: "correct_password",
        created_at: "2024-01-01T00:00:00Z",
      };

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockAdminUser,
          error: null,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.verifyAdminCredentials("admin", "correct_password");
      expect(result).toBe(true);
    });

    it("should return false when password does not match", async () => {
      const mockAdminUser = {
        id: "123",
        username: "admin",
        password_hash: "correct_password",
        created_at: "2024-01-01T00:00:00Z",
      };

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockAdminUser,
          error: null,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.verifyAdminCredentials("admin", "wrong_password");
      expect(result).toBe(false);
    });

    it("should return false when admin user is not found", async () => {
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.verifyAdminCredentials("nonexistent", "password");
      expect(result).toBe(false);
    });

    it("should return false when there is an error", async () => {
      const mockError = { message: "Database error" } as PostgrestError;

      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: mockError,
        } as PostgrestSingleResponse<any>),
      } as any);

      const result = await adminService.verifyAdminCredentials("admin", "password");
      expect(result).toBe(false);
    });
  });
});
