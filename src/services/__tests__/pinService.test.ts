import { describe, it, expect, beforeEach, vi } from "vitest";
import { supabase } from "@/integrations/supabase/client";
import { pinService } from "../pinService";
import { PostgrestSingleResponse, PostgrestError } from "@supabase/supabase-js";

interface AppSettings {
  key: string;
  value: string;
}

describe("pinService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getPin", () => {
    it("should return PIN when it exists", async () => {
      const mockPin: AppSettings = { key: "app_pin", value: "1234" };
      const mockResponse: PostgrestSingleResponse<AppSettings> = {
        data: mockPin,
        error: null,
        count: null,
        status: 200,
        statusText: "OK",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockResponse),
        }),
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await pinService.getPin();
      expect(result).toEqual(mockPin);
      expect(mockSelect).toHaveBeenCalled();
    });

    it("should return null when PIN doesn't exist", async () => {
      const mockError: PostgrestError = {
        code: "PGRST116",
        message: "Not found",
        details: "The requested resource was not found",
        hint: "Check if the resource exists",
        name: "PostgrestError",
      };
      const mockResponse: PostgrestSingleResponse<AppSettings> = {
        data: null,
        error: mockError,
        count: null,
        status: 404,
        statusText: "Not Found",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockResponse),
        }),
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
      } as any);

      const result = await pinService.getPin();
      expect(result).toBeNull();
      expect(mockSelect).toHaveBeenCalled();
    });

    it("should throw error when query fails", async () => {
      const mockError: PostgrestError = {
        code: "PGRST500",
        message: "Database error",
        details: "An error occurred while querying the database",
        hint: "Check the database connection",
        name: "PostgrestError",
      };
      const mockResponse: PostgrestSingleResponse<AppSettings> = {
        data: null,
        error: mockError,
        count: null,
        status: 500,
        statusText: "Internal Server Error",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockResponse),
        }),
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
      } as any);

      await expect(pinService.getPin()).rejects.toThrow(
        "Fehler beim Abrufen des PINs: Database error"
      );
      expect(mockSelect).toHaveBeenCalled();
    });
  });

  describe("updatePin", () => {
    it("should update existing PIN", async () => {
      const mockPin: AppSettings = { key: "app_pin", value: "1234" };
      const newPin = "5678";

      const mockGetResponse: PostgrestSingleResponse<AppSettings> = {
        data: mockPin,
        error: null,
        count: null,
        status: 200,
        statusText: "OK",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockGetResponse),
        }),
      });

      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null,
          count: null,
          status: 200,
          statusText: "OK",
        }),
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
        update: mockUpdate,
      } as any);

      await pinService.updatePin(newPin);

      expect(mockUpdate).toHaveBeenCalledWith({ value: newPin });
      expect(mockUpdate().eq).toHaveBeenCalledWith("key", "app_pin");
    });

    it("should insert new PIN when none exists", async () => {
      const newPin = "5678";

      const mockError: PostgrestError = {
        code: "PGRST116",
        message: "Not found",
        details: "The requested resource was not found",
        hint: "Check if the resource exists",
        name: "PostgrestError",
      };
      const mockGetResponse: PostgrestSingleResponse<AppSettings> = {
        data: null,
        error: mockError,
        count: null,
        status: 404,
        statusText: "Not Found",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockGetResponse),
        }),
      });

      const mockInsert = vi.fn().mockResolvedValue({
        data: null,
        error: null,
        count: null,
        status: 201,
        statusText: "Created",
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
        insert: mockInsert,
      } as any);

      await pinService.updatePin(newPin);

      expect(mockInsert).toHaveBeenCalledWith({
        key: "app_pin",
        value: newPin,
      });
    });

    it("should throw error when update fails", async () => {
      const mockPin: AppSettings = { key: "app_pin", value: "1234" };

      const mockGetResponse: PostgrestSingleResponse<AppSettings> = {
        data: mockPin,
        error: null,
        count: null,
        status: 200,
        statusText: "OK",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockGetResponse),
        }),
      });

      const mockError: PostgrestError = {
        code: "PGRST500",
        message: "Update failed",
        details: "An error occurred while updating the record",
        hint: "Check the update conditions",
        name: "PostgrestError",
      };

      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: mockError,
          count: null,
          status: 500,
          statusText: "Internal Server Error",
        }),
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
        update: mockUpdate,
      } as any);

      await expect(pinService.updatePin("5678")).rejects.toThrow(
        "Fehler beim Speichern des PINs: Update failed"
      );
    });

    it("should throw error when insert fails", async () => {
      const mockError: PostgrestError = {
        code: "PGRST116",
        message: "Not found",
        details: "The requested resource was not found",
        hint: "Check if the resource exists",
        name: "PostgrestError",
      };
      const mockGetResponse: PostgrestSingleResponse<AppSettings> = {
        data: null,
        error: mockError,
        count: null,
        status: 404,
        statusText: "Not Found",
      };

      const mockSelect = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue(mockGetResponse),
        }),
      });

      const mockInsertError: PostgrestError = {
        code: "PGRST500",
        message: "Insert failed",
        details: "An error occurred while inserting the record",
        hint: "Check the insert conditions",
        name: "PostgrestError",
      };

      const mockInsert = vi.fn().mockResolvedValue({
        data: null,
        error: mockInsertError,
        count: null,
        status: 500,
        statusText: "Internal Server Error",
      });

      vi.mocked(supabase.from).mockReturnValue({
        select: mockSelect,
        insert: mockInsert,
      } as any);

      await expect(pinService.updatePin("5678")).rejects.toThrow(
        "Fehler beim Speichern des PINs: Insert failed"
      );
    });
  });
});
