import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  getMatchResultsForSession,
  addMatchResult,
  updateMatchResult,
  deleteMatchResult,
} from "../matchResultsService";
import { supabase } from "@/integrations/supabase/client";

// Mock Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
  },
}));

describe("matchResultsService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getMatchResultsForSession", () => {
    it("should return match results for a game session", async () => {
      const mockResults = [
        {
          id: "1",
          game_session_id: "session-1",
          team_a_id: "team-a-1",
          team_b_id: "team-b-1",
          goals_a: 3,
          goals_b: 2,
          match_order: 1,
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
        {
          id: "2",
          game_session_id: "session-1",
          team_a_id: "team-a-2",
          team_b_id: "team-b-2",
          goals_a: 1,
          goals_b: 1,
          match_order: 2,
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
      ];

      // Setup the mock to return the expected data
      const mockResponse = { data: mockResults, error: null };
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue(mockResponse),
      } as any);

      const results = await getMatchResultsForSession("session-1");

      expect(results).toEqual(mockResults);
      expect(supabase.from).toHaveBeenCalledWith("match_results");
    });

    it("should throw an error when the database query fails", async () => {
      const mockError = new Error("Database error");

      // Setup the mock to return an error
      const mockResponse = { data: null, error: mockError };
      vi.mocked(supabase.from).mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnValue(mockResponse),
      } as any);

      await expect(getMatchResultsForSession("session-1")).rejects.toThrow(mockError);
    });
  });

  describe("addMatchResult", () => {
    it("should add a new match result", async () => {
      const newResult = {
        game_session_id: "session-1",
        team_a_id: "team-a-1",
        team_b_id: "team-b-1",
        goals_a: 3,
        goals_b: 2,
        match_order: 1,
      };

      const mockResponse = {
        id: "1",
        ...newResult,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      };

      // Setup the mock to return the expected data
      vi.mocked(supabase.from).mockReturnValue({
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockReturnValue({ data: mockResponse, error: null }),
      } as any);

      const result = await addMatchResult(newResult);

      expect(result).toEqual(mockResponse);
      expect(supabase.from).toHaveBeenCalledWith("match_results");
    });

    it("should throw an error when the insert fails", async () => {
      const mockError = new Error("Insert failed");

      // Setup the mock to return an error
      vi.mocked(supabase.from).mockReturnValue({
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockReturnValue({ data: null, error: mockError }),
      } as any);

      await expect(
        addMatchResult({
          game_session_id: "session-1",
          team_a_id: "team-a-1",
          team_b_id: "team-b-1",
          goals_a: 3,
          goals_b: 2,
          match_order: 1,
        })
      ).rejects.toThrow(mockError);
    });
  });

  describe("updateMatchResult", () => {
    it("should update an existing match result", async () => {
      const updates = {
        goals_a: 4,
        goals_b: 2,
      };

      const mockResponse = {
        id: "1",
        game_session_id: "session-1",
        team_a_id: "team-a-1",
        team_b_id: "team-b-1",
        goals_a: 4,
        goals_b: 2,
        match_order: 1,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-02T00:00:00Z",
      };

      // Setup the mock to return the expected data
      vi.mocked(supabase.from).mockReturnValue({
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockReturnValue({ data: mockResponse, error: null }),
      } as any);

      const result = await updateMatchResult("1", updates);

      expect(result).toEqual(mockResponse);
      expect(supabase.from).toHaveBeenCalledWith("match_results");
    });

    it("should throw an error when the update fails", async () => {
      const mockError = new Error("Update failed");

      // Setup the mock to return an error
      vi.mocked(supabase.from).mockReturnValue({
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockReturnValue({ data: null, error: mockError }),
      } as any);

      await expect(updateMatchResult("1", { goals_a: 4 })).rejects.toThrow(mockError);
    });
  });

  describe("deleteMatchResult", () => {
    it("should delete a match result", async () => {
      // Setup the mock to return success
      vi.mocked(supabase.from).mockReturnValue({
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({ data: null, error: null }),
      } as any);

      await expect(deleteMatchResult("1")).resolves.not.toThrow();

      expect(supabase.from).toHaveBeenCalledWith("match_results");
    });

    it("should throw an error when the delete fails", async () => {
      const mockError = new Error("Delete failed");

      // Setup the mock to return an error
      vi.mocked(supabase.from).mockReturnValue({
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnValue({ data: null, error: mockError }),
      } as any);

      await expect(deleteMatchResult("1")).rejects.toThrow(mockError);
    });
  });
});
