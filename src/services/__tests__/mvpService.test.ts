import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  createOrReopenVotingPeriod,
  closeVotingPeriodAndCalculateMVP,
  fetchMvpWinner,
  fetchMvpWinners,
} from "../mvpService";
import { notifyMVPVotingOpened, notifyMVPVotingClosed } from "../whatsappService";
import { supabase } from "@/integrations/supabase/client";

// Mock the WhatsApp notification functions
vi.mock("../whatsappService", () => ({
  notifyMVPVotingOpened: vi.fn(),
  notifyMVPVotingClosed: vi.fn(),
}));

// Mock the Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          maybeSingle: vi.fn(),
          single: vi.fn(),
          order: vi.fn(),
        })),
        neq: vi.fn(() => ({
          eq: vi.fn(() => ({
            lt: vi.fn(),
          })),
        })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

describe("MVP Service", () => {
  let mockSupabase: typeof supabase;

  beforeEach(() => {
    vi.clearAllMocks();
    mockSupabase = vi.mocked(supabase);
  });

  describe("createOrReopenVotingPeriod", () => {
    it("should create a new voting period when none exists", async () => {
      // Mock the cleanup call first (update for stale periods)
      const updateMock = vi.fn().mockResolvedValue({ data: null, error: null });

      // Mock the check for existing period (returns null - no existing period)
      const maybeSingleMock = vi.fn().mockResolvedValue({ data: null, error: null });

      // Mock the insert for new period
      const singleMock = vi.fn().mockResolvedValue({
        data: { game_sessions: { date: "2024-01-01T20:00:00Z" } },
        error: null,
      });

      // Set up the mock chain for multiple calls
      mockSupabase.from
        .mockReturnValueOnce({
          update: () => ({
            lt: () => ({
              eq: () => ({
                neq: updateMock,
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            eq: () => ({
              maybeSingle: maybeSingleMock,
            }),
          }),
        })
        .mockReturnValueOnce({
          insert: () => ({
            select: () => ({
              single: singleMock,
            }),
          }),
        });

      const result = await createOrReopenVotingPeriod("game-123");

      expect(result).toBe(true);
      expect(notifyMVPVotingOpened).toHaveBeenCalledTimes(1);
      expect(mockSupabase.from).toHaveBeenCalledWith("mvp_voting_periods");
    });

    it("should handle errors gracefully", async () => {
      // Mock the cleanup call to succeed
      const updateMock = vi.fn().mockResolvedValue({ data: null, error: null });

      // Mock the check for existing period to fail
      const maybeSingleMock = vi.fn().mockRejectedValue(new Error("Database error"));

      mockSupabase.from
        .mockReturnValueOnce({
          update: () => ({
            lt: () => ({
              eq: () => ({
                neq: updateMock,
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            eq: () => ({
              maybeSingle: maybeSingleMock,
            }),
          }),
        });

      const result = await createOrReopenVotingPeriod("game-123");

      expect(result).toBe(false);
      expect(notifyMVPVotingOpened).not.toHaveBeenCalled();
    });
  });

  describe("closeVotingPeriodAndCalculateMVP", () => {
    it("should close voting period and handle no votes", async () => {
      mockSupabase.from.mockImplementation(() => ({
        select: () => ({
          eq: () => ({
            single: vi.fn().mockResolvedValue({
              data: {
                id: "voting-123",
                game_session_id: "game-123",
                game_sessions: { date: "2024-01-01T20:00:00Z" },
              },
              error: null,
            }),
            order: vi.fn().mockResolvedValue({ data: [], error: null }),
          }),
        }),
        update: () => ({
          eq: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
      }));

      const result = await closeVotingPeriodAndCalculateMVP("voting-123");

      expect(result).toBe(true);
      expect(notifyMVPVotingClosed).not.toHaveBeenCalled();
    });

    it("should calculate winners and send notification when votes exist", async () => {
      const voteResults = [
        { player_id: "player-1", player_name: "Player 1", vote_count: 3 },
        { player_id: "player-2", player_name: "Player 2", vote_count: 3 },
      ];

      mockSupabase.from.mockImplementation(() => ({
        select: () => ({
          eq: () => ({
            single: vi.fn().mockResolvedValue({
              data: {
                id: "voting-123",
                game_session_id: "game-123",
                game_sessions: { date: "2024-01-01T20:00:00Z" },
              },
              error: null,
            }),
            order: vi.fn().mockResolvedValue({ data: voteResults, error: null }),
          }),
        }),
        update: () => ({
          eq: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
        delete: () => ({
          eq: vi.fn().mockResolvedValue({ data: null, error: null }),
        }),
        insert: () => ({
          select: () => ({
            single: vi.fn().mockResolvedValue({ data: null, error: null }),
          }),
        }),
      }));

      const result = await closeVotingPeriodAndCalculateMVP("voting-123");

      expect(result).toBe(true);
      expect(notifyMVPVotingClosed).toHaveBeenCalledTimes(1);
    });
  });

  describe("fetchMvpWinner", () => {
    it("should return empty array when no voting period exists", async () => {
      mockSupabase.from.mockImplementation(() => ({
        select: () => ({
          eq: () => ({
            maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
          }),
        }),
      }));

      const result = await fetchMvpWinner("game-123");
      expect(result).toEqual([]);
    });

    it("should return winners when they exist", async () => {
      // Mock the first call to get voting period
      const maybeSingleMock = vi.fn().mockResolvedValue({
        data: { id: "voting-123" },
        error: null,
      });

      // Mock the second call to get winners
      const winnersMock = vi.fn().mockResolvedValue({
        data: [
          {
            player_id: "player-1",
            vote_count: 3,
            players: {
              name: "Player 1",
              jersey_number: 10,
            },
          },
        ],
        error: null,
      });

      // Set up the mock chain for the two sequential calls
      mockSupabase.from
        .mockReturnValueOnce({
          select: () => ({
            eq: () => ({
              maybeSingle: maybeSingleMock,
            }),
          }),
        })
        .mockReturnValueOnce({
          select: () => ({
            eq: winnersMock,
          }),
        });

      const result = await fetchMvpWinner("game-123");

      expect(result).toHaveLength(1);
      expect(result[0].playerName).toBe("Player 1");
    });
  });

  describe("fetchMvpWinners", () => {
    it("should fetch winners for multiple game sessions", async () => {
      mockSupabase.from.mockImplementation(() => ({
        select: () => ({
          eq: () => ({
            maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
          }),
        }),
      }));

      const gameIds = ["game-1", "game-2"];
      const result = await fetchMvpWinners(gameIds);

      expect(Array.isArray(result)).toBe(true);
    });
  });
});
