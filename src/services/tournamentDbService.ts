import { supabase } from "@/integrations/supabase/client";
import {
  Tournament,
  TournamentMatch,
  TournamentStanding,
  TournamentConfig,
} from "@/types/tournament";
import { Team } from "@/types";
import { toast } from "@/components/ui/use-toast";

/**
 * Save a tournament to the database
 * @param tournament The tournament to save
 * @returns The saved tournament with updated IDs
 */
export async function saveTournament(tournament: Tournament): Promise<Tournament | null> {
  try {
    // First, save the tournament configuration
    const { data: tournamentData, error: tournamentError } = await supabase
      .from("tournaments")
      .insert({
        game_session_id: tournament.gameSessionId,
        total_duration_minutes: tournament.config.totalDurationMinutes,
        break_between_matches_minutes: tournament.config.breakBetweenMatchesMinutes,
        match_duration_minutes: tournament.config.matchDurationMinutes,
        number_of_teams: tournament.config.numberOfTeams,
        number_of_fields: tournament.config.numberOfFields,
        is_active: tournament.isActive,
        start_time: tournament.startTime,
        end_time: tournament.endTime,
      })
      .select()
      .single();

    if (tournamentError) {
      console.error("Error saving tournament:", tournamentError);
      throw tournamentError;
    }

    const tournamentId = tournamentData.id;

    // Then, save all matches
    const matchPromises = tournament.matches.map(async (match) => {
      // Store timeSlot in the field metadata as "Feld X (Spielrunde Y)"
      const fieldWithTimeSlot = match.timeSlot
        ? `${match.field} (Spielrunde ${match.timeSlot})`
        : match.field;

      const { data: matchData, error: matchError } = await supabase
        .from("tournament_matches")
        .insert({
          tournament_id: tournamentId,
          team_a_id: match.teamA.id,
          team_b_id: match.teamB.id,
          goals_a: match.goalsA,
          goals_b: match.goalsB,
          is_completed: match.isCompleted,
          match_number: match.matchNumber,
          field: fieldWithTimeSlot,
          start_time: match.startTime,
          end_time: match.endTime,
        })
        .select()
        .single();

      if (matchError) {
        console.error("Error saving tournament match:", matchError);
        throw matchError;
      }

      return {
        ...match,
        id: matchData.id,
      };
    });

    // Wait for all matches to be saved
    const savedMatches = await Promise.all(matchPromises);

    // Finally, save all standings
    const standingPromises = tournament.standings.map(async (standing) => {
      const { data: standingData, error: standingError } = await supabase
        .from("tournament_standings")
        .insert({
          tournament_id: tournamentId,
          team_id: standing.team.id,
          played: standing.played,
          won: standing.won,
          drawn: standing.drawn,
          lost: standing.lost,
          goals_for: standing.goalsFor,
          goals_against: standing.goalsAgainst,
          goal_difference: standing.goalDifference,
          points: standing.points,
        })
        .select()
        .single();

      if (standingError) {
        console.error("Error saving tournament standing:", standingError);
        throw standingError;
      }

      return {
        ...standing,
        id: standingData.id,
      };
    });

    // Wait for all standings to be saved
    const savedStandings = await Promise.all(standingPromises);

    // Make sure the tournament is active in the database
    const { error: activateError } = await supabase
      .from("tournaments")
      .update({
        is_active: true,
        start_time: tournament.startTime
          ? tournament.startTime.toISOString()
          : new Date().toISOString(),
      })
      .eq("id", tournamentId);

    if (activateError) {
      console.error("Error activating tournament:", activateError);
    }

    // Return the saved tournament with updated IDs
    return {
      ...tournament,
      id: tournamentId,
      matches: savedMatches,
      standings: savedStandings,
      isActive: true,
      startTime: tournament.startTime || new Date(),
    };
  } catch (error) {
    console.error("Error in saveTournament:", error);
    toast({
      title: "Fehler",
      description: "Das Turnier konnte nicht gespeichert werden.",
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Get a tournament by game session ID
 * @param gameSessionId The game session ID
 * @returns The tournament or null if not found
 */
export async function getTournamentByGameSessionId(
  gameSessionId: string
): Promise<Tournament | null> {
  try {
    // First, get the tournament
    const { data: tournamentData, error: tournamentError } = await supabase
      .from("tournaments")
      .select()
      .eq("game_session_id", gameSessionId)
      .maybeSingle();

    if (tournamentError) {
      console.error("Error getting tournament:", tournamentError);
      throw tournamentError;
    }

    if (!tournamentData) {
      return null;
    }

    // Then, get all matches for this tournament
    const { data: matchesData, error: matchesError } = await supabase
      .from("tournament_matches")
      .select(
        `
        id,
        team_a_id,
        team_b_id,
        goals_a,
        goals_b,
        is_completed,
        match_number,
        field,
        start_time,
        end_time,
        teams_a:teams!tournament_matches_team_a_id_fkey (
          id,
          name,
          average_rating
        ),
        teams_b:teams!tournament_matches_team_b_id_fkey (
          id,
          name,
          average_rating
        )
      `
      )
      .eq("tournament_id", tournamentData.id)
      .order("match_number", { ascending: true });

    if (matchesError) {
      console.error("Error getting tournament matches:", matchesError);
      throw matchesError;
    }

    // Then, get all standings for this tournament
    const { data: standingsData, error: standingsError } = await supabase
      .from("tournament_standings")
      .select(
        `
        id,
        played,
        won,
        drawn,
        lost,
        goals_for,
        goals_against,
        goal_difference,
        points,
        teams:teams (
          id,
          name,
          average_rating
        )
      `
      )
      .eq("tournament_id", tournamentData.id)
      .order("points", { ascending: false });

    if (standingsError) {
      console.error("Error getting tournament standings:", standingsError);
      throw standingsError;
    }

    // Format the data to match our types
    const matches: TournamentMatch[] = matchesData.map((match) => {
      // Extract timeSlot from field if available (format: "Feld X (Spielrunde Y)" or "Feld X (Zeitfenster Y)")
      let field = match.field;
      let timeSlot: number | undefined = undefined;

      if (field && (field.includes("(Spielrunde ") || field.includes("(Zeitfenster "))) {
        // Support both new "Spielrunde" and old "Zeitfenster" format for backward compatibility
        const spielrundeMatches = field.match(/Feld (\d+) \(Spielrunde (\d+)\)/);
        const zeitfensterMatches = field.match(/Feld (\d+) \(Zeitfenster (\d+)\)/);

        const matches = spielrundeMatches || zeitfensterMatches;

        if (matches && matches.length >= 3) {
          field = `Feld ${matches[1]}`;
          timeSlot = parseInt(matches[2]);
        }
      }

      return {
        id: match.id,
        teamA: {
          id: match.teams_a.id,
          name: match.teams_a.name,
          averageRating: match.teams_a.average_rating,
          players: [],
        },
        teamB: {
          id: match.teams_b.id,
          name: match.teams_b.name,
          averageRating: match.teams_b.average_rating,
          players: [],
        },
        goalsA: match.goals_a,
        goalsB: match.goals_b,
        isCompleted: match.is_completed,
        matchNumber: match.match_number,
        field,
        timeSlot,
        startTime: match.start_time ? new Date(match.start_time) : undefined,
        endTime: match.end_time ? new Date(match.end_time) : undefined,
      };
    });

    const standings: TournamentStanding[] = standingsData.map((standing) => ({
      team: {
        id: standing.teams.id,
        name: standing.teams.name,
        averageRating: standing.teams.average_rating,
        players: [],
      },
      played: standing.played,
      won: standing.won,
      drawn: standing.drawn,
      lost: standing.lost,
      goalsFor: standing.goals_for,
      goalsAgainst: standing.goals_against,
      goalDifference: standing.goal_difference,
      points: standing.points,
    }));

    // Create the tournament config
    const config: TournamentConfig = {
      totalDurationMinutes: tournamentData.total_duration_minutes,
      breakBetweenMatchesMinutes: tournamentData.break_between_matches_minutes,
      matchDurationMinutes: tournamentData.match_duration_minutes,
      numberOfTeams: tournamentData.number_of_teams,
      numberOfFields: tournamentData.number_of_fields,
    };

    // Make sure the tournament is active if it has matches but no end_time
    if (matches.length > 0 && !tournamentData.end_time && !tournamentData.is_active) {
      console.log("Activating tournament that should be active");
      const { error: activateError } = await supabase
        .from("tournaments")
        .update({
          is_active: true,
          start_time: tournamentData.start_time || new Date().toISOString(),
        })
        .eq("id", tournamentData.id);

      if (activateError) {
        console.error("Error activating tournament:", activateError);
      } else {
        tournamentData.is_active = true;
        if (!tournamentData.start_time) {
          tournamentData.start_time = new Date().toISOString();
        }
      }
    }

    // Return the tournament
    return {
      id: tournamentData.id,
      gameSessionId,
      config,
      matches,
      standings,
      currentMatchIndex: -1, // We don't store this in the database
      isActive: tournamentData.is_active,
      startTime: tournamentData.start_time ? new Date(tournamentData.start_time) : undefined,
      endTime: tournamentData.end_time ? new Date(tournamentData.end_time) : undefined,
    };
  } catch (error) {
    console.error("Error in getTournamentByGameSessionId:", error);
    toast({
      title: "Fehler",
      description: "Das Turnier konnte nicht geladen werden.",
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Delete a tournament match
 * @param tournamentId The tournament ID
 * @param matchId The match ID
 * @returns True if successful, false otherwise
 */
export async function deleteTournamentMatch(
  tournamentId: string,
  matchId: string
): Promise<boolean> {
  try {
    // Delete the match from the database
    const { error } = await supabase.from("tournament_matches").delete().eq("id", matchId);

    if (error) {
      console.error("Error deleting tournament match:", error);
      throw error;
    }

    // Renumber the matches
    const { data: matches, error: fetchError } = await supabase
      .from("tournament_matches")
      .select("id, match_number")
      .eq("tournament_id", tournamentId)
      .order("match_number", { ascending: true });

    if (fetchError) {
      console.error("Error fetching matches for renumbering:", fetchError);
      return true; // Still return true because the match was deleted
    }

    // Update match numbers
    for (let i = 0; i < matches.length; i++) {
      const match = matches[i];
      if (match.match_number !== i + 1) {
        const { error: updateError } = await supabase
          .from("tournament_matches")
          .update({ match_number: i + 1 })
          .eq("id", match.id);

        if (updateError) {
          console.error(`Error updating match number for match ${match.id}:`, updateError);
        }
      }
    }

    return true;
  } catch (error) {
    console.error("Error in deleteTournamentMatch:", error);
    toast({
      title: "Fehler",
      description: "Das Spiel konnte nicht gelöscht werden.",
      variant: "destructive",
    });
    return false;
  }
}

/**
 * Add a new match to a tournament
 * @param tournamentId The tournament ID
 * @param match The match to add
 * @returns The saved match with updated ID if successful, null otherwise
 */
export async function addTournamentMatch(
  tournamentId: string,
  match: TournamentMatch
): Promise<TournamentMatch | null> {
  try {
    // Store timeSlot in the field metadata as "Feld X (Spielrunde Y)"
    const fieldWithTimeSlot = match.timeSlot
      ? `${match.field} (Spielrunde ${match.timeSlot})`
      : match.field;

    const { data, error } = await supabase
      .from("tournament_matches")
      .insert({
        tournament_id: tournamentId,
        team_a_id: match.teamA.id,
        team_b_id: match.teamB.id,
        goals_a: match.goalsA,
        goals_b: match.goalsB,
        is_completed: match.isCompleted,
        match_number: match.matchNumber,
        field: fieldWithTimeSlot,
        start_time: match.startTime,
        end_time: match.endTime,
      })
      .select()
      .single();

    if (error) {
      console.error("Error adding tournament match:", error);
      throw error;
    }

    return {
      ...match,
      id: data.id,
    };
  } catch (error) {
    console.error("Error in addTournamentMatch:", error);
    toast({
      title: "Fehler",
      description: "Das Spiel konnte nicht hinzugefügt werden.",
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Update tournament status (active/inactive)
 * @param tournamentId The tournament ID
 * @param isActive Whether the tournament is active
 * @param startTime Optional start time
 * @param endTime Optional end time
 * @returns True if successful, false otherwise
 */
export async function updateTournamentStatus(
  tournamentId: string,
  isActive: boolean,
  startTime?: Date,
  endTime?: Date
): Promise<boolean> {
  try {
    const updateData: any = {
      is_active: isActive,
    };

    if (startTime) {
      updateData.start_time = startTime.toISOString();
    }

    if (endTime) {
      updateData.end_time = endTime.toISOString();
    }

    const { error } = await supabase.from("tournaments").update(updateData).eq("id", tournamentId);

    if (error) {
      console.error("Error updating tournament status:", error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Error in updateTournamentStatus:", error);
    return false;
  }
}

/**
 * Update a tournament match result and recalculate standings
 * @param matchId The match ID
 * @param goalsA Goals scored by team A
 * @param goalsB Goals scored by team B
 * @returns True if successful, false otherwise
 */
export async function updateTournamentMatchResult(
  matchId: string,
  goalsA: number,
  goalsB: number
): Promise<boolean> {
  try {
    // First, get the match to get team IDs and tournament ID
    const { data: matchData, error: matchError } = await supabase
      .from("tournament_matches")
      .select("tournament_id, team_a_id, team_b_id")
      .eq("id", matchId)
      .single();

    if (matchError) {
      console.error("Error getting tournament match:", matchError);
      throw matchError;
    }

    // Update the match result
    const { error: updateError } = await supabase
      .from("tournament_matches")
      .update({
        goals_a: goalsA,
        goals_b: goalsB,
        is_completed: true,
        end_time: new Date().toISOString(),
      })
      .eq("id", matchId);

    if (updateError) {
      console.error("Error updating tournament match result:", updateError);
      throw updateError;
    }

    // Now recalculate standings for both teams
    await updateTeamStandings(matchData.tournament_id, matchData.team_a_id);
    await updateTeamStandings(matchData.tournament_id, matchData.team_b_id);

    return true;
  } catch (error) {
    console.error("Error in updateTournamentMatchResult:", error);
    toast({
      title: "Fehler",
      description: "Das Spielergebnis konnte nicht gespeichert werden.",
      variant: "destructive",
    });
    return false;
  }
}

/**
 * Update standings for a specific team in a tournament
 * @param tournamentId The tournament ID
 * @param teamId The team ID
 */
async function updateTeamStandings(tournamentId: string, teamId: string): Promise<void> {
  try {
    // Get all completed matches for this team in this tournament
    const { data: matches, error: matchesError } = await supabase
      .from("tournament_matches")
      .select("goals_a, goals_b, team_a_id, team_b_id")
      .eq("tournament_id", tournamentId)
      .eq("is_completed", true)
      .or(`team_a_id.eq.${teamId},team_b_id.eq.${teamId}`);

    if (matchesError) {
      console.error("Error getting matches for standings calculation:", matchesError);
      throw matchesError;
    }

    // Calculate standings
    let played = 0;
    let won = 0;
    let drawn = 0;
    let lost = 0;
    let goalsFor = 0;
    let goalsAgainst = 0;

    matches.forEach((match) => {
      played++;

      if (match.team_a_id === teamId) {
        // Team is team A
        goalsFor += match.goals_a || 0;
        goalsAgainst += match.goals_b || 0;

        if (match.goals_a > match.goals_b) {
          won++;
        } else if (match.goals_a < match.goals_b) {
          lost++;
        } else {
          drawn++;
        }
      } else {
        // Team is team B
        goalsFor += match.goals_b || 0;
        goalsAgainst += match.goals_a || 0;

        if (match.goals_b > match.goals_a) {
          won++;
        } else if (match.goals_b < match.goals_a) {
          lost++;
        } else {
          drawn++;
        }
      }
    });

    const goalDifference = goalsFor - goalsAgainst;
    const points = won * 3 + drawn;

    // Update or insert standings
    const { data: existingStanding, error: checkError } = await supabase
      .from("tournament_standings")
      .select("id")
      .eq("tournament_id", tournamentId)
      .eq("team_id", teamId)
      .maybeSingle();

    if (checkError) {
      console.error("Error checking existing standings:", checkError);
      throw checkError;
    }

    if (existingStanding) {
      // Update existing standing
      const { error: updateError } = await supabase
        .from("tournament_standings")
        .update({
          played,
          won,
          drawn,
          lost,
          goals_for: goalsFor,
          goals_against: goalsAgainst,
          goal_difference: goalDifference,
          points,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingStanding.id);

      if (updateError) {
        console.error("Error updating standings:", updateError);
        throw updateError;
      }
    } else {
      // Insert new standing
      const { error: insertError } = await supabase.from("tournament_standings").insert({
        tournament_id: tournamentId,
        team_id: teamId,
        played,
        won,
        drawn,
        lost,
        goals_for: goalsFor,
        goals_against: goalsAgainst,
        goal_difference: goalDifference,
        points,
      });

      if (insertError) {
        console.error("Error inserting standings:", insertError);
        throw insertError;
      }
    }
  } catch (error) {
    console.error("Error updating team standings:", error);
    // We don't throw here to prevent breaking the match update flow
  }
}
