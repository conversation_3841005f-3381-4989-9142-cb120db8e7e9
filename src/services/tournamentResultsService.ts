import { supabase } from "@/integrations/supabase/client";
import { Tournament } from "@/types/tournament";
import { MatchResult } from "@/types/match-results";
import {
  addMatchResult,
  updateMatchResult,
  getMatchResultsForSession,
} from "./matchResultsService";
import { toast } from "@/components/ui/use-toast";

/**
 * Syncs tournament match results to the match_results table
 * @param tournament The tournament containing matches to sync
 * @param showToast Whether to show toast notifications (default: true)
 * @returns Promise<boolean> indicating success or failure
 */
export async function syncTournamentResultsToMatchResults(
  tournament: Tournament,
  showToast: boolean = true
): Promise<boolean> {
  try {
    if (!tournament.gameSessionId) {
      console.error("Cannot sync tournament results: No game session ID");
      return false;
    }

    // Get existing match results for this session
    const existingResults = await getMatchResultsForSession(tournament.gameSessionId);
    console.debug(
      `[syncTournamentResultsToMatchResults] Found ${existingResults.length} existing results for session ${tournament.gameSessionId}`
    );

    // Create a map of existing results for quick lookup
    // Use a composite key of team_a_id + team_b_id + match_number to identify matches
    const existingResultsMap = new Map<string, MatchResult>();
    existingResults.forEach((result) => {
      // Create keys that include match_order to distinguish between multiple matches with the same teams
      const key = `${result.team_a_id}-${result.team_b_id}-${result.match_order}`;
      const reverseKey = `${result.team_b_id}-${result.team_a_id}-${result.match_order}`;
      existingResultsMap.set(key, result);
      existingResultsMap.set(reverseKey, result);
    });

    // Track processed matches to avoid duplicates within this sync operation
    const processedMatches = new Set<string>();
    let updatedCount = 0;
    let addedCount = 0;

    // Process each tournament match
    for (const match of tournament.matches) {
      // Skip matches that don't have results yet
      if (!match.isCompleted || match.goalsA === null || match.goalsB === null) {
        continue;
      }

      // Skip matches where teams don't have IDs
      if (!match.teamA.id || !match.teamB.id) {
        console.warn(`Skipping match #${match.matchNumber}: Missing team IDs`);
        continue;
      }

      // Create a key for this match that includes the match number
      const matchKey = `${match.teamA.id}-${match.teamB.id}-${match.matchNumber}`;
      const reverseMatchKey = `${match.teamB.id}-${match.teamA.id}-${match.matchNumber}`;

      // Skip if we've already processed this match in this sync operation
      if (processedMatches.has(matchKey) || processedMatches.has(reverseMatchKey)) {
        continue;
      }

      // Mark this match as processed
      processedMatches.add(matchKey);
      processedMatches.add(reverseMatchKey);

      // Check for existing result with the same match number first
      let existingResult =
        existingResultsMap.get(matchKey) || existingResultsMap.get(reverseMatchKey);

      // If not found by match number, try to find by team combination and field
      if (!existingResult) {
        existingResult = existingResults.find(
          (r) =>
            ((r.team_a_id === match.teamA.id && r.team_b_id === match.teamB.id) ||
              (r.team_a_id === match.teamB.id && r.team_b_id === match.teamA.id)) &&
            r.field === match.field
        );
      }

      // Prepare match result data
      const resultData: Partial<MatchResult> = {
        game_session_id: tournament.gameSessionId,
        team_a_id: match.teamA.id,
        team_b_id: match.teamB.id,
        goals_a: match.goalsA,
        goals_b: match.goalsB,
        field: match.field || null,
        match_order: match.matchNumber,
        time_slot: match.startTime ? match.startTime.toISOString() : null,
      };

      if (existingResult) {
        // Update existing result
        await updateMatchResult(existingResult.id, resultData);
        updatedCount++;
      } else {
        // Add new result
        await addMatchResult(resultData as any);
        addedCount++;
      }
    }

    console.debug(
      `[syncTournamentResultsToMatchResults] Updated ${updatedCount} and added ${addedCount} match results`
    );
    return true;
  } catch (error) {
    console.error("Error syncing tournament results:", error);
    if (showToast) {
      toast({
        title: "Fehler",
        description: "Die Turnierergebnisse konnten nicht synchronisiert werden.",
        variant: "destructive",
      });
    }
    return false;
  }
}
