import { supabase } from "@/integrations/supabase/client";
import { Feedback } from "@/types/feedback";

export async function submitFeedback(feedback: Omit<Feedback, "id" | "createdAt">) {
  console.log("Received feedback data:", feedback);

  const insertData: any = {
    content: feedback.content,
    submitted_by: feedback.submittedBy,
    is_game_feedback: feedback.isGameFeedback,
    rating: feedback.rating,
    positives: feedback.positives,
    improvements: feedback.improvements,
    notes: feedback.notes,
  };

  // Debugge gameSessionId
  if (feedback.isGameFeedback) {
    console.log(
      "Game feedback - session ID:",
      feedback.gameSessionId,
      "Type:",
      typeof feedback.gameSessionId
    );

    if (feedback.gameSessionId) {
      insertData.game_session_id = feedback.gameSessionId;
      console.log("Added game_session_id to insert data:", insertData.game_session_id);
    } else {
      console.log("No gameSessionId provided for game feedback");
    }
  }

  console.log("Final insert data:", insertData);

  const { data, error } = await supabase.from("feedback").insert(insertData).select();

  if (error) {
    console.error("Error submitting feedback:", error);
    throw new Error(error.message);
  }

  return data?.[0];
}

export async function getRecentSessions(limit = 5) {
  const { data, error } = await supabase
    .from("game_sessions")
    .select("*")
    .in("status", ["played", "archived"])
    .order("date", { ascending: false })
    .limit(limit);

  if (error) {
    console.error("Error fetching recent sessions:", error);
    throw new Error(error.message);
  }

  return data.map((session) => ({
    id: session.id,
    date: new Date(session.date),
    status: session.status,
    formattedDate: new Date(session.date).toLocaleDateString("de-DE", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  }));
}
