---
description: 
globs: 
alwaysApply: true
---
# Documentation Rule

Any time a new functionality is added or an existing one significantly changed, Cursor must prompt to:

- Add or update the related `.mdc` file
- Check if user-facing text or flows have changed (update docs/screenshots if needed)
- Reference the relevant `.mdc` file in pull request descriptions

Encourage writing product-focused documentation, not just code internals.
