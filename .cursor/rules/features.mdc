---
description: 
globs: 
alwaysApply: true
---
# Feature Documentation Rule

For each new feature or major enhancement, create a documentation file inside:

`.cursor/rules/features/feature-name.mdc`

Each feature doc should include:
- **What**: Short summary of the feature
- **Why**: Motivation behind it
- **How**: Technical explanation
- **Where**: Affected files or components
- **Screenshots or Diagrams** (optional)

This makes the codebase easier to understand and maintain over time.
