---
description: 
globs: 
alwaysApply: false
---
# Code-based MVP Calculation

## What
A system to calculate and manage Most Valuable Player (MVP) voting and winners for football game sessions. This replaces the previous database trigger/function implementation with a more flexible and maintainable code-based solution.

## Why
- Better testability: We can now write comprehensive tests for the MVP calculation logic
- More flexibility: Easier to modify and extend the logic in TypeScript rather than in SQL
- Better error handling: We can handle edge cases and provide better error messages
- Improved maintainability: The code is now in one place with the rest of the application logic

## How
1. Created a new migration file to remove the database trigger and function
2. Added new functions to the MVP service:
   - `calculateAndSaveMVPWinners`: Calculates and saves MVP winners for a voting period
   - `closeVotingPeriodAndCalculateMVP`: Closes a voting period and calculates MVP winners
   - `fetchMvpWinner`: Retrieves MVP winners for a single game session
   - `fetchMvpWinners`: Retrieves MVP winners for multiple game sessions
3. Added comprehensive tests for all scenarios including:
   - Single winner cases
   - Multiple winner (tie) cases
   - Error handling cases
   - Empty vote results
   - Database operation failures

## Where
- `src/services/mvpService.ts`: Main implementation of MVP calculation logic
- `src/services/__tests__/mvpService.test.ts`: Comprehensive test suite
- Database tables used:
  - `mvp_voting_periods`: Manages voting periods
  - `mvp_vote_results`: Stores individual votes
  - `mvp_winners`: Records MVP winners
  - `players`: Player information

## Key Features
- Support for multiple MVP winners with tied votes
- Proper error handling and logging for all operations
- Manual control over when to calculate MVP winners
- Comprehensive test coverage for different scenarios
- Type-safe implementation with TypeScript
- Clear separation of concerns between voting and winner calculation

## Technical Details

### Data Flow
1. Voting period is closed via `closeVotingPeriodAndCalculateMVP`
2. System fetches all votes for the period
3. Highest vote count is determined
4. All players with the highest vote count are selected as winners
5. Previous MVP entries are cleared
6. New MVP winners are saved

### Error Handling
- Database operation errors are caught and logged
- Proper error messages for different scenarios
- Graceful fallbacks for edge cases
- No data loss during winner calculation

### Testing
Test suite covers:
- Successful MVP calculations
- Tie handling
- Error scenarios
- Empty vote results
- Multiple game sessions
- Edge cases

## Example Usage

```typescript
// Close voting and calculate MVP
const success = await closeVotingPeriodAndCalculateMVP('voting-period-id');

// Fetch MVP winners for a game
const winners = await fetchMvpWinner('game-session-id');
// Example response:
// [{
//   playerName: "John Doe",
//   jerseyNumber: 10,
//   playerId: "player-123",
//   matchId: "game-session-456",
//   voteCount: 5
// }]
```
