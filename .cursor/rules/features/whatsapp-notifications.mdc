---
description: 
globs: 
alwaysApply: false
---
# WhatsApp Notifications Feature

## What
A feature that sends WhatsApp notifications to a group chat for important game events:
- When players sign up for or sign off from a football game
- When game registration opens (with game date and time details)
- When game registration closes
- When teams are generated and approved (showing team compositions)

## Why
- Improves real-time communication between team members
- Keeps all players informed about who is joining or leaving a game
- Provides immediate updates about game registration status
- Allows players to see team compositions as soon as they're finalized
- Increases engagement and awareness about game participation
- Provides immediate updates without requiring players to check the app

## How
- Created a dedicated WhatsApp notification service (`whatsappService.ts`)
- Integrated with an existing external WhatsApp API endpoint
- Added notification calls to the player status change workflow
- Added notification calls to the game registration toggle workflow
- Added notification calls to the team approval workflow
- Implemented proper error handling that doesn't affect the core functionality

### Technical Implementation
The service makes API calls to an external WhatsApp service endpoint at `https://wa.internrw.de/.netlify/functions/internrw-wa-send` 
with appropriate authorization headers.

Messages are formatted with emoji indicators:
- ✅ for player signups
- ❌ for player sign-offs
- 🟢 for registration opening
- 🔴 for registration closing
- ⚽ for team generation

The implementation includes a testMode parameter for testing without sending actual messages.

## Where
- `src/services/whatsappService.ts` - Core service with WhatsApp messaging functionality
- `src/components/player-list/index.tsx` - Integration for player signup/signoff notifications
- `src/components/admin/GameManagementPage.tsx` - Integration for game registration and team generation notifications

## Future Enhancements
- Add configurable notification settings for admins
- Extend notifications for other events (match results, etc.)
- Include more game details in notifications (location, special instructions)
- Add ability for players to opt-in/opt-out of certain notification types
