---
description: 
globs: 
alwaysApply: false
---
# WhatsApp Notifications Toggle

## What
A toggle switch in the admin settings panel that allows administrators to enable or disable WhatsApp notifications for player events (sign-ups, sign-offs) and game events (registration opening/closing, team generation).

## Why
- Provides flexibility to control when notifications are sent
- Allows for silent operation during testing or maintenance
- Prevents notification spam during development or when making bulk changes
- Gives administrators control over communication channels

## How
- Added a new setting in the Supabase `app_settings` table: `whatsapp_notifications_enabled`
- Modified the `useTeamSettings` hook to include this setting
- Added a toggle switch in the AdminSettings component
- Updated the WhatsApp service to check this setting before sending any messages
- When disabled, messages are logged but not sent to the WhatsApp API
- Implemented Supabase Realtime Subscriptions for instant updates when settings change
- Created a Supabase migration file to initialize the setting

## Where
- `src/hooks/useTeamSettings.ts` - Added the new setting to the hook
- `src/services/whatsappService.ts` - Added realtime subscription to notification settings
- `src/components/admin/AdminSettings.tsx` - Added the toggle UI component
- `supabase/migrations/20240730000000_add_whatsapp_notification_setting.sql` - Database migration

## Technical Details
The implementation follows these steps:
1. Before any WhatsApp message is sent, the service checks the `whatsapp_notifications_enabled` setting
2. If disabled, the message is logged but not sent to the API
3. The setting is stored in Supabase and managed through the existing settings framework
4. The UI toggle is placed in the admin settings panel under a new "Notifications" section
5. To get real-time updates when settings change, we use Supabase Realtime:
   - A subscription is set up to listen for changes to `whatsapp_notifications_enabled`
   - The global state is updated instantly when the setting changes in the database
   - This approach eliminates the need for manual cache invalidation
   - The React hook `useWhatsAppNotificationSetting()` is available for components that need the current value
