---
description: 
globs: 
alwaysApply: false
---
# Feature: PayPal Payment Link for Game Fee

**What**: Adds a PayPal payment link to the game information card after signups close.
**Why**: To provide players with a convenient way to pay their share of the pitch rental fee directly after the player count (and thus the cost per player) is confirmed.
**How**:
- Added configurable settings in `AdminSettings.tsx` and `useTeamSettings.ts` for:
    - Cost-per-player for 2, 3, and 4 team scenarios.
    - The base PayPal.me link (e.g., `https://www.paypal.me/YourName/`).
- Updated Supabase `app_settings` table via migration (`supabase/migrations/YYYYMMDDHHMMSS_add_paypal_config.sql`) to store these settings.
- Modified `GameInfoCard.tsx` to:
    - Fetch cost and PayPal link settings using `useTeamSettings`.
    - Calculate the correct cost based on the number of confirmed players (`inPlayersCount`).
    - Construct a PayPal link using the configured base URL and the calculated amount (e.g., `{BASE_URL}{COST}/EUR`).
    - Conditionally display a payment link section only when `signupOpen` is false and `hasEnoughPlayers` is true.
**Where**:
- `src/components/admin/AdminSettings.tsx`: Admin UI for configuring costs and PayPal link.
- `src/hooks/useTeamSettings.ts`: Hook managing settings state and Supabase interaction.
- `src/components/game/GameInfoCard.tsx`: Displays the game info and the conditional PayPal link.
- `supabase/migrations/YYYYMMDDHHMMSS_add_paypal_config.sql`: Database migration for new settings.
- `app_settings` table in Supabase.

**Screenshots or Diagrams**: (Optional: Add screenshots of the admin settings and the game info card with the link visible)
