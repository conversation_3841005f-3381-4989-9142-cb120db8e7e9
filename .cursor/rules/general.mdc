---
description: 
globs: 
alwaysApply: true
---
# General Project Rules

## Description
This is a React + Vite + Tailwind web application hosted on Vercel, using Supabase for backend services and authentication. The app is built for organizing football games for a group called "Inter NRW".

## Tech Stack
- Frontend: React + Vite + TailwindCSS
- Backend: Supabase (PostgreSQL, Auth, Storage)
- Hosting: Vercel
- Documentation lives in `.cursor/rules` and `/docs`

## General Principles

### 🧠 Simplicity First
- Keep solutions as **simple** and **minimal** as possible.
- Do not introduce abstraction layers, generics, or patterns unless there's a **clear and current need**.
- Prefer code that is **easy to understand over clever code**.

### 🧼 Clean Code Guidelines
- Each component/function should do **one thing well**.
- Favor **small components** and **pure functions**.
- Always name things clearly: components, variables, props, etc.
- Avoid unnecessary comments — the code should speak for itself.
- Use **early returns** to reduce nesting.

### 🛠️ Minimal & Targeted Changes
- Only modify what's **actually needed**.
- Avoid "drive-by" cleanups unless part of an intentional refactor.
- Don't restructure folders or files unless the current setup is breaking usability or maintainability.

### 🚫 Avoid Overengineering
- Avoid generic "utils" folders, base components, or abstract services unless **multiple real cases** demand it.
- YAGNI principle: "You Ain’t Gonna Need It".
- Do not build for imaginary future use cases.

### Naming Conventions
- Components: `PascalCase`
- Files: `kebab-case` or `camelCase.js`
- Pages: `/routes/[name]/index.tsx`
- Styles: Tailwind only; no external CSS modules

### ✅ Testing of new futures
- Every **new functionality** must include a corresponding **test file**.
- Use **React Testing Library** and **Vitest** for testing UI logic and interactions.
- Tests should:
  - Cover **the key user behavior**
  - Avoid excessive mocking – test what matters
  - Be fast, focused, and colocated (same folder as component)
- Snapshot testing is **optional**, not mandatory.

## Important Directories
- `/src/components`: Reusable components
- `/src/pages`: Route-based files
- `/supabase`: Server interactions
- `/src/hooks`: Custom logic hooks
- `/src/types`: Shared TypeScript types

Always document significant features and changes as `.mdc` files and place them into `.cursor/rules/features/`.
