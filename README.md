# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/8a15544e-e57f-4721-b232-e36066ace5d0

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/8a15544e-e57f-4721-b232-e36066ace5d0) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/8a15544e-e57f-4721-b232-e36066ace5d0) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## Lazy Loading Optimizations

We've implemented two types of lazy loading strategies to improve the application performance:

### 1. Component Code Splitting with React.lazy

Using React's built-in `lazy` function and `Suspense`, we've implemented code splitting to reduce the initial bundle size:

1. **Page-Level Code Splitting**:

   - All major pages are loaded lazily using `React.lazy`
   - A loading spinner is shown while the code is being fetched
   - This significantly reduces the initial JavaScript bundle size

2. **Component-Level Code Splitting**:

   - Heavy components within pages are also lazy loaded
   - Components are only loaded when they are needed (e.g., only when a specific tab is active)
   - Each component has its own fallback UI while loading

3. **Conditional Loading**:
   - Some components are only loaded based on specific conditions (e.g., TeamDisplay is only loaded when teams are available)
   - Admin panel components are loaded only when their respective tabs are active

### 2. Data Fetching Optimizations

1. **GameSessions Hook**:

   - Now accepts options to specify which data to fetch (`fetchCurrent` and `fetchPast`)
   - Components can request only the data they need
   - Uses `useRef` to prevent duplicate fetches

2. **Players Hook**:

   - Accepts an optional `sessionId` parameter to fetch only players for a specific session
   - Optimized real-time subscriptions with filters to reduce unnecessary updates

3. **TeamEditor Hook**:
   - Only initializes and fetches data when needed based on the active session
   - Uses real-time subscriptions with precise filters to minimize data transfer

### Component Optimizations

1. **GameInfo Component**:

   - Only fetches current session data, not past sessions
   - Uses memoization to prevent unnecessary recalculations

2. **MatchHistory Component**:

   - Only fetches past sessions, not current session
   - Implements lazy loading of team data when a match is expanded

3. **AdminPanel Component**:

   - Uses an `activeTab` state to only fetch data needed for the current tab
   - Memoizes computed values to reduce unnecessary recalculations

4. **PlayerManagement Component**:
   - Uses memoization for filtered players and pagination data
   - Only fetches player data for the current session

These optimizations significantly reduce:

- The initial bundle size and loading time
- The number of database queries
- Unnecessary re-renders of components
- Data transferred between client and server
- Memory usage from cached data

The application now loads code and data just-in-time when it's needed, rather than eagerly loading everything upfront.

## Team Creation and Balancing Algorithm

Our application uses a practical algorithm to create balanced teams for casual play, recognizing that most players are flexible in their positions.

### How Team Generation Works

The team generation process is designed for recreational play where only a few players have specialized roles:

1. **Team Count Determination**:

   - The system works exclusively with player groups of 10, 15, or 20 players, creating 2, 3, or 4 teams respectively
   - For sessions with 11-14 players, only the first 10 players are used for teams (the rest are reserves)
   - For sessions with 16-19 players, only the first 15 players are used for teams (the rest are reserves)
   - For sessions with more than 20 players, only the first 20 players are used for teams (the rest are reserves)
   - Each team will have exactly 5 players, ensuring perfectly even team sizes
   - This provides ideal conditions for fair 5v5 matches with an optional rotation system

2. **Simplified Role Priority System**:

   - The algorithm now focuses on the two most critical specialized positions:
     1. **Goalkeepers** - Most important specialized role, distributed first (one per team when possible)
     2. **Strikers** - Second highest priority, distributed one per team when possible
   - All other roles (allrounders, defenders, midfielders, unassigned) are treated equally and distributed based on ratings
   - This streamlined approach recognizes that in recreational play, most position distinctions beyond goalkeeper and striker are fluid

3. **Guaranteed Equal Team Sizes**:

   - The algorithm strictly enforces that all teams have the exact number of players specified in the target size
   - For example, in a 20-player session, all 4 teams will have exactly 5 players each
   - This equal distribution is maintained throughout the entire algorithm, preventing scenarios where one team has 6 players and another has 4
   - After role distribution, a special balancing step ensures any team size imbalances are corrected

4. **Rating-Based Distribution**:

   - After goalkeepers and strikers are distributed, remaining players are assigned based on team ratings
   - Players are sorted by skill rating (highest first)
   - Lower-rated teams receive higher-rated players to maintain overall balance

5. **Final Team Balancing**:

   - The algorithm performs player swaps between teams to optimize overall balance
   - Special roles (goalkeepers and strikers) are protected during this process
   - The goal is to create teams with similar average skill levels while respecting key positions

6. **Explicit Manual Approval Workflow**:
   - After teams are generated, they must be explicitly approved by an administrator
   - This two-step process allows review of the generated teams before they are saved
   - Administrators can regenerate teams if they are not satisfied with the initial distribution
   - Teams are saved to the database only after explicit approval

### Performance Optimizations

The team generation and approval process has been optimized to provide a smooth user experience:

1. **State Persistence**:

   - Generated teams are cached to prevent loss during page refreshes or API operations
   - Teams remain in the UI until explicitly approved or regenerated

2. **Loading Indicators**:

   - Clear loading indicators show when teams are being generated or saved
   - Prevents confusion during database operations

3. **Reduced Rendering**:
   - State updates are optimized to minimize unnecessary renders
   - Debounced state updates prevent UI flicker during rapid changes

### Implementation Details

The team generation is implemented in the `teamUtils.ts` file with two main functions:

1. **`generateTeams(players: Player[], teamCount: number): Team[]`**:

   - Takes an array of players and desired team count
   - Prioritizes the distribution of goalkeepers and strikers (one per team when possible)
   - Pools all other players together for rating-based distribution
   - Strictly enforces equal team sizes
   - Returns an array of balanced teams with identical player counts

2. **`balanceTeamsBySwapping(teams: Team[], maxSwaps: number = 5): Team[]`**:
   - Further optimizes team balance through strategic player swaps
   - Protects goalkeepers and strikers during the swapping process
   - Maintains the exact team size during all swaps
   - Uses a variance-minimization approach to create fair teams

The team approval workflow is managed through the `useTeamEditor` hook, which:

- Separates generation and approval into distinct steps
- Provides persistent state between page refreshes
- Optimizes database operations to reduce loading times
- Ensures team data integrity during the save process

This simplified implementation addresses the common reality in recreational sports:

- Only a few positions (typically goalkeeper and striker) are truly specialized
- Most players can and will play multiple positions
- Team balance is primarily achieved through even distribution of skill levels
- Equal team sizes are essential for fair play

### Specific Scenarios

#### Scenario 1: 10 Players (2 Teams)

For a session with 10 signups that includes specialized roles:

```
- 3 Goalkeepers: GK1 (95), GK2 (85), GK3 (75)
- 2 Strikers: ST1 (90), ST2 (85)
- 5 Flexible Players: P1-P5 (ratings between 70-95)
```

**Distribution Process**:

1. The algorithm creates 2 teams of 5 players each
2. Goalkeepers are distributed first: 1 per team (GK1 to Team A, GK2 to Team B)
3. Strikers are distributed next: 1 per team (ST1 to Team A, ST2 to Team B)
4. Remaining players (including GK3) are distributed based on ratings for balance

**Result**:

- Team A: GK1 (95), ST1 (90), P2 (90), P4 (75), P5 (70) - Avg: 84
- Team B: GK2 (85), ST2 (85), P1 (95), P3 (80), GK3 (75) - Avg: 84

Both teams have 5 players each, with Team B having an extra goalkeeper who can play outfield.

#### Scenario 2: 15 Players (3 Teams)

For a session with 15 signups that includes specialized roles:

```
- 3 Goalkeepers: GK1 (99), GK2 (90), GK3 (80)
- 3 Strikers: ST1 (95), ST2 (85), ST3 (82)
- 9 Flexible Players: P1-P9 (ratings between 70-95)
```

**Distribution Process**:

1. The algorithm creates 3 teams of 5 players each
2. Each team receives exactly one goalkeeper (GK1 to Team A, GK2 to Team B, GK3 to Team C)
3. Each team receives exactly one striker (ST1 to Team A, ST2 to Team B, ST3 to Team C)
4. Remaining players are distributed to balance overall team ratings

**Result**:

- Team A: GK1 (99), ST1 (95), P7 (75), P8 (72), P9 (70) - Avg: 82.2
- Team B: GK2 (90), ST2 (85), P3 (85), P5 (80), P6 (78) - Avg: 83.6
- Team C: GK3 (80), ST3 (82), P1 (95), P2 (90), P4 (82) - Avg: 85.8

All three teams have exactly 5 players, with balanced distribution of the specialized roles.

#### Scenario 3: 21 Players (4 Teams with Reserves)

In a typical recreational setting with 21 players:

```
- 2 Goalkeepers: GK1 (95), GK2 (85)
- 3 Specialized Strikers: ST1 (90), ST2 (85), ST3 (80)
- 16 Flexible players (ratings between 70-95)
```

**Distribution Process**:

1. The system creates 4 teams with target sizes of 5 players each (+ 1 reserve)
2. First, distribute 2 goalkeepers (2 teams get one goalkeeper each)
3. Next, distribute 3 strikers (3 teams get one striker each)
4. Finally, distribute remaining players based on ratings

**Result**:

- Four balanced teams with 5 players each plus 1 reserve player
- Two teams have one goalkeeper each
- Three teams have one striker each
- All teams have exactly 5 players (the reserve is tracked separately)
- Teams have similar average skill ratings

These scenarios demonstrate the algorithm's key improvements:

1. Strict enforcement of equal team sizes (exactly 5 players per team)
2. Focus only on the truly specialized positions (goalkeeper and striker)
3. Simplified distribution for all other roles based on ratings
4. Protection of specialized roles during balancing
5. Optimized for the reality of casual recreational play
