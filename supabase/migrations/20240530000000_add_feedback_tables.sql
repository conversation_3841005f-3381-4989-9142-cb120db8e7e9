

-- Create the general feedback table
CREATE TABLE public.feedback (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  content TEXT NOT NULL,
  submitted_by TEXT,
  is_game_feedback BOOLEAN NOT NULL DEFAULT false,
  game_session_id UUID REFERENCES public.game_sessions(id),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  positives TEXT,
  improvements TEXT,
  notes TEXT
);

-- Enable RLS and allow admin access (customize as needed)
alter table public.feedback enable row level security;

ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."feedback";

GRANT ALL ON TABLE "public"."feedback" TO "anon";
GRANT ALL ON TABLE "public"."feedback" TO "authenticated";
GRANT ALL ON TABLE "public"."feedback" TO "service_role";

CREATE POLICY "Allow full access to feedback" ON "public"."feedback" USING (true) WITH CHECK (true);

CREATE POLICY "Allow select for all" ON "public"."feedback" FOR SELECT USING (true);
