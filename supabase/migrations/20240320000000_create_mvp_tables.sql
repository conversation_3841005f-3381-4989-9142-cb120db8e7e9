-- Create table for MVP voting periods
CREATE TABLE public.mvp_voting_periods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_session_id UUID REFERENCES public.game_sessions(id) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  is_open BOOLEAN NOT NULL DEFAULT true,
  ends_at TIMESTAMPTZ NOT NULL DEFAULT (now() + interval '24 hours'),
  UNIQUE (game_session_id)
);

-- Create indexes for better performance on voting periods
CREATE INDEX idx_mvp_voting_periods_game_session ON mvp_voting_periods(game_session_id);
CREATE INDEX idx_mvp_voting_periods_is_open ON mvp_voting_periods(is_open);
CREATE INDEX idx_mvp_voting_periods_ends_at ON mvp_voting_periods(ends_at);

-- Enable Row Level Security
ALTER TABLE public.mvp_voting_periods ENABLE ROW LEVEL SECURITY;

-- Allow everyone to read voting periods
CREATE POLICY "Anyone can view voting periods" ON public.mvp_voting_periods
  FOR SELECT USING (true);

-- Drop mvp_votes if it exists (to reset)
DROP TABLE IF EXISTS public.mvp_votes CASCADE;

-- Create table for MVP votes
CREATE TABLE public.mvp_votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voting_period_id UUID NOT NULL REFERENCES public.mvp_voting_periods(id),
  player_id UUID NOT NULL REFERENCES public.players(id),
  voter_device_id TEXT NOT NULL, -- Prevent duplicate votes from same device
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE (voting_period_id, voter_device_id)
);

-- Indexes for vote counting and lookup
CREATE INDEX mvp_votes_voting_period_voter_idx ON mvp_votes(voting_period_id, voter_device_id);
CREATE INDEX mvp_votes_player_idx ON mvp_votes(player_id);

-- Allow everyone to view votes
CREATE POLICY "Anyone can view votes" ON public.mvp_votes
  FOR SELECT USING (true);

-- Allow voting only if period is open
CREATE POLICY "Anyone can vote during open period" ON public.mvp_votes
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.mvp_voting_periods
      WHERE id = voting_period_id AND is_open = true
    )
  );

-- Create a view for aggregated vote results
CREATE OR REPLACE VIEW public.mvp_vote_results AS
SELECT 
    v.voting_period_id,
    v.player_id,
    p.name as player_name,
    COUNT(*) as vote_count,
    ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY v.voting_period_id)), 2) as vote_percentage
FROM mvp_votes v
JOIN players p ON p.id = v.player_id
GROUP BY v.voting_period_id, v.player_id, p.name;

-- Create table for MVP winners
CREATE TABLE public.mvp_winners (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  game_session_id UUID NOT NULL REFERENCES public.game_sessions(id),
  player_id UUID NOT NULL REFERENCES public.players(id),
  voting_period_id UUID NOT NULL REFERENCES public.mvp_voting_periods(id),
  vote_count INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(game_session_id),      -- One MVP per game
  UNIQUE(voting_period_id)      -- One MVP per voting period
);

-- Enable Row Level Security
ALTER TABLE public.mvp_votes DISABLE ROW LEVEL SECURITY;

-- Enable Row Level Security
ALTER TABLE public.mvp_voting_periods DISABLE ROW LEVEL SECURITY;

-- Enable Row Level Security
ALTER TABLE public.mvp_winners DISABLE ROW LEVEL SECURITY;

-- Create function to calculate and store MVP winner
CREATE OR REPLACE FUNCTION public.calculate_mvp_winner()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed when a voting period is being closed
  IF OLD.is_open = true AND NEW.is_open = false THEN
    -- Insert the winner into mvp_winners table
    INSERT INTO mvp_winners (game_session_id, player_id, voting_period_id, vote_count)
    SELECT 
      NEW.game_session_id,
      vote_counts.player_id,
      NEW.id,
      vote_counts.vote_count
    FROM (
      SELECT 
        player_id,
        COUNT(*) as vote_count
      FROM mvp_votes
      WHERE voting_period_id = NEW.id
      GROUP BY player_id
      ORDER BY COUNT(*) DESC
      LIMIT 1
    ) vote_counts
    ON CONFLICT (voting_period_id) DO UPDATE
    SET 
      player_id = EXCLUDED.player_id,
      vote_count = EXCLUDED.vote_count;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for MVP calculation
DROP TRIGGER IF EXISTS calculate_mvp_after_voting_closed ON mvp_voting_periods;
CREATE TRIGGER calculate_mvp_after_voting_closed
  AFTER UPDATE ON mvp_voting_periods
  FOR EACH ROW
  EXECUTE FUNCTION calculate_mvp_winner();

