-- Migration to remove the automatic MVP winner calculation from the database
-- and move this logic to the application code

-- Drop the trigger that calculates MVP winners when a voting period is closed
DROP TRIGGER IF EXISTS calculate_mvp_after_voting_closed ON mvp_voting_periods;

-- Drop the function that was used by the trigger
DROP FUNCTION IF EXISTS public.calculate_mvp_winner();

-- We'll keep the mvp_winners table and the mvp_vote_results view as they are
-- The application code will now be responsible for inserting records into mvp_winners 