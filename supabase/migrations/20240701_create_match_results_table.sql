-- Create table for storing match results for each game session
create table public.match_results (
  id uuid primary key default gen_random_uuid(),
  game_session_id uuid not null references public.game_sessions(id) on delete cascade,
  team_a_id uuid not null references public.teams(id) on delete cascade,
  team_b_id uuid not null references public.teams(id) on delete cascade,
  goals_a integer not null,
  goals_b integer not null,
  field text,         -- optional: field/court name or number
  match_order integer, -- optional: order of the match in the session
  time_slot timestamptz, -- optional: when the match was played
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- Index for fast lookup by session
create index idx_match_results_game_session on public.match_results(game_session_id);

-- Enable RLS and allow admin access (customize as needed)
alter table public.match_results enable row level security;

ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."match_results";

GRANT ALL ON TABLE "public"."game_sessions" TO "anon";
GRANT ALL ON TABLE "public"."game_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."game_sessions" TO "service_role";

CREATE POLICY "Allow full access to match_results" ON "public"."match_results" USING (true) WITH CHECK (true);

CREATE POLICY "Allow select for all" ON "public"."match_results" FOR SELECT USING (true);
