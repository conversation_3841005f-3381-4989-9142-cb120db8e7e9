-- Create tournament tables for tournament mode
-- This migration adds tables to store tournament data

-- Tournament table to store tournament configurations and metadata
CREATE TABLE public.tournaments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_session_id UUID NOT NULL REFERENCES public.game_sessions(id) ON DELETE CASCADE,
  total_duration_minutes INTEGER NOT NULL,
  break_between_matches_minutes INTEGER NOT NULL,
  match_duration_minutes INTEGER NOT NULL,
  number_of_teams INTEGER NOT NULL,
  number_of_fields INTEGER NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT false,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Index for fast lookup by game session
CREATE INDEX idx_tournaments_game_session ON public.tournaments(game_session_id);

-- Tournament matches table to store individual matches in a tournament
CREATE TABLE public.tournament_matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  team_a_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  team_b_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  goals_a INTEGER,
  goals_b INTEGER,
  is_completed BOOLEAN NOT NULL DEFAULT false,
  match_number INTEGER NOT NULL,
  field TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Index for fast lookup by tournament
CREATE INDEX idx_tournament_matches_tournament ON public.tournament_matches(tournament_id);

-- Tournament standings table to store team standings in a tournament
CREATE TABLE public.tournament_standings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  played INTEGER NOT NULL DEFAULT 0,
  won INTEGER NOT NULL DEFAULT 0,
  drawn INTEGER NOT NULL DEFAULT 0,
  lost INTEGER NOT NULL DEFAULT 0,
  goals_for INTEGER NOT NULL DEFAULT 0,
  goals_against INTEGER NOT NULL DEFAULT 0,
  goal_difference INTEGER NOT NULL DEFAULT 0,
  points INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(tournament_id, team_id)
);

-- Index for fast lookup by tournament
CREATE INDEX idx_tournament_standings_tournament ON public.tournament_standings(tournament_id);

-- Enable RLS and allow admin access
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_standings ENABLE ROW LEVEL SECURITY;

-- Add tables to realtime publication
ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."tournaments";
ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."tournament_matches";
ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."tournament_standings";

-- Grant access to authenticated users
GRANT ALL ON TABLE "public"."tournaments" TO "anon";
GRANT ALL ON TABLE "public"."tournaments" TO "authenticated";
GRANT ALL ON TABLE "public"."tournaments" TO "service_role";

GRANT ALL ON TABLE "public"."tournament_matches" TO "anon";
GRANT ALL ON TABLE "public"."tournament_matches" TO "authenticated";
GRANT ALL ON TABLE "public"."tournament_matches" TO "service_role";

GRANT ALL ON TABLE "public"."tournament_standings" TO "anon";
GRANT ALL ON TABLE "public"."tournament_standings" TO "authenticated";
GRANT ALL ON TABLE "public"."tournament_standings" TO "service_role";

-- Create RLS policies
CREATE POLICY "Allow full access to tournaments" ON "public"."tournaments" USING (true) WITH CHECK (true);
CREATE POLICY "Allow select for all tournaments" ON "public"."tournaments" FOR SELECT USING (true);

CREATE POLICY "Allow full access to tournament_matches" ON "public"."tournament_matches" USING (true) WITH CHECK (true);
CREATE POLICY "Allow select for all tournament_matches" ON "public"."tournament_matches" FOR SELECT USING (true);

CREATE POLICY "Allow full access to tournament_standings" ON "public"."tournament_standings" USING (true) WITH CHECK (true);
CREATE POLICY "Allow select for all tournament_standings" ON "public"."tournament_standings" FOR SELECT USING (true);

-- Note: We're not using database functions or triggers
-- All standings calculations will be handled in the application code
