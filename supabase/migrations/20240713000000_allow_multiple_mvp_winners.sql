-- Migration to allow multiple MVP winners for the same game/voting period
-- This removes the unique constraints that limit to one winner per game/voting period

-- First, drop the existing unique constraints
ALTER TABLE public.mvp_winners DROP CONSTRAINT IF EXISTS mvp_winners_game_session_id_key;
ALTER TABLE public.mvp_winners DROP CONSTRAINT IF EXISTS mvp_winners_voting_period_id_key;

-- Add a new constraint to ensure a player can only win once per voting period
-- (to prevent duplicate entries for the same player)
ALTER TABLE public.mvp_winners 
  ADD CONSTRAINT mvp_winners_voting_period_player_unique 
  UNIQUE (voting_period_id, player_id);

-- Drop and recreate the view without adding jersey_number
DROP VIEW IF EXISTS public.mvp_vote_results;
CREATE VIEW public.mvp_vote_results AS
SELECT 
    v.voting_period_id,
    v.player_id,
    p.name as player_name,
    COUNT(*) as vote_count,
    ROUND((COUNT(*) * 100.0 / NULLIF(SUM(COUNT(*)) OVER (PARTITION BY v.voting_period_id), 0)), 2) as vote_percentage
FROM mvp_votes v
JOIN players p ON p.id = v.player_id
GROUP BY v.voting_period_id, v.player_id, p.name;

-- Update the existing MVP winner calculation function to support multiple winners
CREATE OR REPLACE FUNCTION public.calculate_mvp_winner()
RETURNS TRIGGER AS $$
DECLARE
  max_votes INTEGER;
BEGIN
  -- Only proceed when a voting period is being closed
  IF OLD.is_open = true AND NEW.is_open = false THEN
    -- Find the maximum vote count for this voting period
    SELECT MAX(vote_count) INTO max_votes
    FROM (
      SELECT COUNT(*) as vote_count
      FROM mvp_votes
      WHERE voting_period_id = NEW.id
      GROUP BY player_id
    ) vote_counts;
    
    -- Only proceed if there are votes
    IF max_votes IS NOT NULL AND max_votes > 0 THEN
      -- Delete any existing MVP winners for this voting period
      DELETE FROM mvp_winners
      WHERE voting_period_id = NEW.id;
      
      -- Insert all players with the highest vote count as MVP winners
      INSERT INTO mvp_winners (game_session_id, player_id, voting_period_id, vote_count)
      SELECT 
        NEW.game_session_id,
        player_id,
        NEW.id,
        COUNT(*) as vote_count
      FROM mvp_votes
      WHERE voting_period_id = NEW.id
      GROUP BY player_id
      HAVING COUNT(*) = max_votes;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add a comment explaining the change
COMMENT ON TABLE public.mvp_winners IS 'Stores MVP winners for each game session. Multiple players can be MVPs for the same game if they have the same number of votes.'; 