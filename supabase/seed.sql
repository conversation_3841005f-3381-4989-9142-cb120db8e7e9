SET session_replication_role = replica;

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: admin_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."admin_users" ("id", "username", "password_hash", "created_at") VALUES
	('3141b3bb-6947-4970-b2ff-75794501873a', 'admin', 'interNRW2025', '2025-04-16 06:52:53.822857+00');


--
-- Data for Name: app_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."app_settings" ("id", "key", "value", "created_at", "updated_at") VALUES
	('016b6e10-8140-4f3e-bef6-261c8354fc1d', 'allow_3_teams', 'true', '2025-04-21 13:27:20.157014+00', '2025-04-21 13:27:20.157014+00'),
	('8e57f38f-3dd2-453b-b5d9-7b7af1d7a5a6', 'match_duration_2_teams', '90', '2025-04-26 12:48:02.23978+00', '2025-04-26 12:48:02.23978+00'),
	('c0365f09-ed41-499d-abd5-2356aadfc652', 'match_duration_3_teams', '120', '2025-04-26 12:48:02.23978+00', '2025-04-26 12:48:02.23978+00'),
	('cc176b17-0c73-435d-9c46-69e0daecf3fa', 'match_duration_4_teams', '90', '2025-04-26 12:48:02.23978+00', '2025-04-26 12:48:02.23978+00'),
	('3ca99dd3-5bd2-4e17-8e66-3e1170d7a1bf', 'app_pin', '2487', '2025-04-17 23:39:03.252811+00', '2025-04-17 23:39:03.252811+00');


--
-- Data for Name: game_sessions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."game_sessions" ("id", "date", "signup_opens_at", "is_signup_open", "is_team_generated", "created_at", "updated_at", "status", "duration_minutes") VALUES
	('0472273e-44ee-4c3f-aae4-9b94933fa1e9', '2025-05-16 19:00:00+00', '2025-05-12 10:00:00+00', true, false, '2025-05-10 20:40:56.270559+00', '2025-05-10 20:40:56.270559+00', 'scheduled', 120),
	('de5dac63-4ad8-4d6f-8af0-b4837b408c36', '2025-05-09 19:00:00+00', '2025-05-05 10:00:00+00', false, true, '2025-05-03 13:22:55.444629+00', '2025-05-03 13:22:55.444629+00', 'played', 90),
	('a5d6947b-b950-489b-9943-84408f1411e3', '2025-04-17 19:00:00+00', '2025-04-13 10:00:00+00', false, true, '2025-04-16 20:51:04.976416+00', '2025-04-16 20:51:04.976416+00', 'played', 90),
	('4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', '2025-05-02 19:00:00+00', '2025-04-28 10:00:00+00', false, true, '2025-04-29 12:24:21.63456+00', '2025-04-29 12:24:21.63456+00', 'played', 90),
	('402d65eb-fe84-487c-b362-e4f98684d2bb', '2025-04-25 19:00:00+00', '2025-04-21 10:00:00+00', false, true, '2025-04-17 16:41:31.41192+00', '2025-04-17 16:41:31.41192+00', 'played', 90);


--
-- Data for Name: feedback; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: teams; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."teams" ("id", "game_session_id", "name", "average_rating", "created_at") VALUES
	('69d81abb-aa08-4030-9ccf-a28298ebac41', 'a5d6947b-b950-489b-9943-84408f1411e3', 'Team A', 85.00, '2025-04-17 11:34:02.076461+00'),
	('0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', 'a5d6947b-b950-489b-9943-84408f1411e3', 'Team B', 83.60, '2025-04-17 11:34:02.303622+00'),
	('476fddbf-07ea-442e-a327-b17f47c27bac', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'Team Rot', 85.00, '2025-04-26 15:22:00.011529+00'),
	('fbdfa2a1-be27-4f4d-8ae3-46729a46bcd0', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'Team Blau', 84.20, '2025-04-26 15:22:00.234678+00'),
	('d0c2b11e-3b5f-4307-bfec-3483ca72204f', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'Team Schwarz', 85.20, '2025-04-26 15:22:00.562835+00'),
	('d9a93eac-2990-40a4-9a47-08ba639b3ed3', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'Team Weiß', 84.20, '2025-04-26 15:22:00.849328+00'),
	('e4164a5d-7922-42e7-9165-41a0a5728b88', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'Team Weiß ', 84.20, '2025-05-02 15:25:13.342367+00'),
	('922e0327-cabb-4e87-a0bd-8d35b1428a9a', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'Team Bunt', 85.00, '2025-05-02 15:25:13.568583+00'),
	('19528ee8-fe9c-4fa7-a84d-201fa09d58de', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'Team Bunt', 83.80, '2025-05-09 20:08:24.734152+00'),
	('4d342b15-5598-453e-be39-ee3f96ba1313', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'Team Weiß', 85.80, '2025-05-09 20:08:24.899863+00');


--
-- Data for Name: match_results; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."match_results" ("id", "game_session_id", "team_a_id", "team_b_id", "goals_a", "goals_b", "field", "match_order", "time_slot", "created_at", "updated_at") VALUES
	('22be32d8-08a9-4a3a-8b6f-96208af4fd4b', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'e4164a5d-7922-42e7-9165-41a0a5728b88', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', 10, 7, NULL, 1, NULL, '2025-05-04 09:47:04.176862+00', '2025-05-04 09:47:04.176862+00'),
	('34f9f10a-1e70-4c4c-99f8-faa5e19fff2e', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'e4164a5d-7922-42e7-9165-41a0a5728b88', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', 4, 10, NULL, 2, NULL, '2025-05-04 09:48:12.61048+00', '2025-05-04 09:48:12.61048+00'),
	('6eb4f842-f83c-4b9d-9fa0-84a5d8924e39', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'e4164a5d-7922-42e7-9165-41a0a5728b88', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', 10, 6, NULL, 3, NULL, '2025-05-04 09:48:52.185345+00', '2025-05-04 09:48:52.185345+00'),
	('976ae0a1-30d3-4ec4-9f64-b21d4e163d77', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', '4d342b15-5598-453e-be39-ee3f96ba1313', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', 13, 11, NULL, NULL, NULL, '2025-05-10 20:39:02.061712+00', '2025-05-10 20:39:02.061712+00'),
	('45bc292f-9da8-4fb2-97b3-a9bd67421430', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', '4d342b15-5598-453e-be39-ee3f96ba1313', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', 10, 7, NULL, NULL, NULL, '2025-05-10 20:39:51.658386+00', '2025-05-10 20:39:51.658386+00'),
	('b8ffb7e5-2d05-46c0-b152-428752d16f4d', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', '4d342b15-5598-453e-be39-ee3f96ba1313', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', 7, 6, NULL, NULL, NULL, '2025-05-10 20:40:15.280616+00', '2025-05-10 20:40:15.280616+00');


--
-- Data for Name: mvp_voting_periods; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: players; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."players" ("id", "name", "jersey_number", "rating", "created_at", "updated_at", "role") VALUES
	('60e007b7-79f4-48b1-b31b-052a1547b96b', 'Makram Serag', NULL, 85, '2025-05-06 07:34:25.943379+00', '2025-05-06 07:34:25.943379+00', 'allrounder'),
	('58c896fc-3282-4b64-b0a4-ab5954cedbab', 'Abdulkadir Elmi', 12, 83, '2025-05-06 07:45:07.140344+00', '2025-05-06 07:45:07.140344+00', 'midfielder'),
	('9b274b3b-7e45-4e11-b944-61a003c23614', 'Ercan Özkan', 66, 78, '2025-05-06 08:15:22.704295+00', '2025-05-06 08:15:22.704295+00', 'midfielder'),
	('531f0324-c83f-429f-9ed6-18036c5c4e95', 'Ahmed Abara', 9, 88, '2025-04-16 19:12:23.628963+00', '2025-04-16 19:12:23.628963+00', 'allrounder'),
	('9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'Akin Zengin', 50, 88, '2025-04-16 08:38:57.690786+00', '2025-04-16 08:38:57.690786+00', 'defender'),
	('74241d83-1fe8-4869-81d2-705c559c9139', 'Engin Güz', NULL, 80, '2025-05-06 08:16:29.369818+00', '2025-05-06 08:16:29.369818+00', 'allrounder'),
	('04b090ea-0d03-4157-8f16-fa743a6abe66', 'Aykut Oktay', 54, 82, '2025-04-16 13:30:45.380672+00', '2025-04-16 13:30:45.380672+00', 'defender'),
	('5cf0e107-24bd-46f4-b249-07784bbc8632', 'Bent Exner', 3, 87, '2025-04-16 19:17:27.7509+00', '2025-04-16 19:17:27.7509+00', 'midfielder'),
	('47810abe-d3a1-491b-b9dd-9074491885be', 'Brahim El-Bakali', 99, 90, '2025-04-16 19:22:02.760792+00', '2025-04-16 19:22:02.760792+00', 'striker'),
	('2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'Burak Ceyhan', 50, 75, '2025-04-16 13:28:26.112591+00', '2025-04-16 13:28:26.112591+00', 'allrounder'),
	('8b936fa1-3cea-4f44-9bdb-8a4387cd4672', 'Ermond Ademaj', 99, 90, '2025-04-16 19:21:45.295122+00', '2025-04-16 19:21:45.295122+00', 'striker'),
	('e258c985-90ec-44fa-999c-49da6459e336', 'Ersen Menekse', 27, 87, '2025-04-16 19:12:49.991178+00', '2025-04-16 19:12:49.991178+00', 'allrounder'),
	('6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'Ersin Basol', 10, 89, '2025-04-16 08:39:18.644618+00', '2025-04-16 08:39:18.644618+00', 'allrounder'),
	('29b55208-6056-424a-a25a-272f28851374', 'Ismail Bas', 64, 83, '2025-04-16 19:17:59.978486+00', '2025-04-16 19:17:59.978486+00', 'goalkeeper'),
	('889383b4-e342-4b87-ad59-35252463364e', 'Ferhat Özgün', NULL, 77, '2025-05-06 08:17:09.933096+00', '2025-05-06 08:17:09.933096+00', 'midfielder'),
	('cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'Kamil Dülger', 7, 89, '2025-04-16 08:36:33.789487+00', '2025-04-16 08:36:33.789487+00', 'allrounder'),
	('0e7b26d3-9200-4dc1-89bf-300048bb62e6', 'Kevin Riachy', NULL, 80, '2025-05-06 08:17:56.283255+00', '2025-05-06 08:17:56.283255+00', 'defender'),
	('f2e992b2-bab9-40e9-bd80-710ed46d5153', 'Kenan Zengin', 50, 87, '2025-04-16 19:18:43.14379+00', '2025-04-16 19:18:43.14379+00', 'allrounder'),
	('34effccd-1d53-4b60-8297-e1a5aad2f806', 'Anis Saidi', 3, 90, '2025-05-06 10:00:05.506561+00', '2025-05-06 10:00:05.506561+00', 'defender'),
	('0ed06269-db14-4fc3-ba87-2f96f32ed24e', 'Markus Frank', 6, 91, '2025-04-16 19:19:26.52448+00', '2025-04-16 19:19:26.52448+00', 'striker'),
	('74728903-da6d-4c3a-9f66-c71dc3c70619', 'Tevfik Dincer', 64, 89, '2025-04-16 13:28:51.494522+00', '2025-04-16 13:28:51.494522+00', 'striker'),
	('aa8c98fa-0e14-46ee-8dd1-5c145515bcad', 'Ali Tarssim', 99, 79, '2025-05-06 08:15:02.821608+00', '2025-05-06 08:15:02.821608+00', 'defender'),
	('92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'Muammer Cakir', 89, 80, '2025-04-16 08:36:07.703104+00', '2025-04-16 08:36:07.703104+00', 'striker'),
	('06c64ece-c34e-4623-88e5-2a623a694437', 'Esref Kapucu', 99, 85, '2025-04-23 17:54:33.685226+00', '2025-04-23 17:54:33.685226+00', 'midfielder'),
	('e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'Onur Batur', 16, 84, '2025-04-16 08:44:10.024766+00', '2025-04-16 08:44:10.024766+00', 'goalkeeper'),
	('5002252f-d354-4c90-a01c-902f4d36bfa2', 'Michael Meyer', NULL, 91, '2025-05-06 07:37:09.300037+00', '2025-05-06 07:37:09.300037+00', 'striker'),
	('fa1ca7de-f8e0-42c1-92c5-9a5244934bb0', 'Mohamed Serag', NULL, 72, '2025-05-06 07:35:03.84406+00', '2025-05-06 07:35:03.84406+00', 'defender'),
	('f8ac085d-82eb-4eb0-8f35-2606529dc899', 'Mouhcine Ben Aomar', 99, 84, '2025-04-16 19:22:44.046509+00', '2025-04-16 19:22:44.046509+00', 'allrounder'),
	('daf57809-6d25-42c2-9916-521b16952506', 'Salih Yildiz', 99, 79, '2025-04-26 14:24:02.862288+00', '2025-04-26 14:24:02.862288+00', 'midfielder'),
	('b9972dad-cd54-443c-928e-a4f899ce9998', 'Semih Oktay', 54, 76, '2025-04-16 19:20:54.619897+00', '2025-04-16 19:20:54.619897+00', 'allrounder'),
	('e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', 'Adem Kaya', 63, 86, '2025-04-16 13:06:42.214242+00', '2025-04-16 13:06:42.214242+00', 'allrounder'),
	('31923b90-dd7b-420e-846b-70f4ad05b073', 'Berat Kaya', 10, 86, '2025-04-16 08:44:14.23959+00', '2025-04-16 08:44:14.23959+00', 'midfielder'),
	('de607ae7-ca39-4533-b47d-672320613bbc', 'Emre Karadayi', 25, 85, '2025-04-16 08:44:28.272556+00', '2025-04-16 08:44:28.272556+00', 'midfielder'),
	('3371308b-0987-46cd-b05a-455531bdc609', 'Joker', 99, 80, '2025-05-12 09:51:34.215648+00', '2025-05-12 09:51:34.215648+00', 'allrounder'),
	('e502a4c1-f5cb-4e80-8b29-edacf9862dab', 'Sherief Ahmed', 99, 80, '2025-05-12 09:52:13.074295+00', '2025-05-12 09:52:13.074295+00', 'allrounder'),
	('fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'Erdem Kayaagil', 6, 86, '2025-04-16 13:06:51.604496+00', '2025-04-16 13:06:51.604496+00', 'defender'),
	('7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'Samet Kockar', 7, 88, '2025-04-16 08:44:20.007482+00', '2025-04-16 08:44:20.007482+00', 'allrounder'),
	('94e4f959-4706-4fd9-a56f-d0a9d319e798', 'Jobin Kolath', 23, 87, '2025-04-16 19:18:20.278598+00', '2025-04-16 19:18:20.278598+00', 'allrounder'),
	('98f532f2-510d-4e64-8a6d-63ed3e0f88e6', 'Samir Bouajaj', 11, 89, '2025-04-16 19:20:29.255991+00', '2025-04-16 19:20:29.255991+00', 'allrounder'),
	('f612cc5a-570f-4abf-aa5b-ba8a73450931', 'Shah Sayed', 5, 78, '2025-04-16 08:44:35.275696+00', '2025-04-16 08:44:35.275696+00', 'allrounder'),
	('e8bf9a1f-e826-4fad-a665-a4d2a1a6d8fc', 'Tayfun Cakir', 8, 78, '2025-04-16 19:21:20.632989+00', '2025-04-16 19:21:20.632989+00', 'defender'),
	('04305fca-dab6-4123-8bf9-4d2963856a82', 'Viktor Reich', 8, 82, '2025-04-16 13:07:20.323803+00', '2025-04-16 13:07:20.323803+00', 'midfielder'),
	('34e970d7-4f43-4e0e-9697-569abfe0b8db', 'Youssef Ahdour', 99, 78, '2025-04-16 19:22:19.295267+00', '2025-04-16 19:22:19.295267+00', 'allrounder'),
	('56990edf-853e-4ecb-ab88-17a7c1b10a0b', 'Bilal Abara', NULL, 80, '2025-05-06 07:36:57.584793+00', '2025-05-06 07:36:57.584793+00', 'midfielder');


--
-- Data for Name: mvp_votes; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: mvp_winners; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: player_signups; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."player_signups" ("id", "player_id", "game_session_id", "status", "signup_time") VALUES
	('a49e2cfc-d221-4491-aa00-ef0114c7efe4', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:52:42.783+00'),
	('4167b2c4-8f6f-449f-9111-50a1028b94d4', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:52:51.074+00'),
	('0521bea3-b08c-422d-ae87-406c4061e42d', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:00.255+00'),
	('bc827a72-5cac-4fc5-8e58-db23e9c74fe8', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:03.216+00'),
	('f84bc022-2c71-4572-8209-9278205deabf', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:11.4+00'),
	('05f3d384-0bbf-4cf3-926d-5cd0a1c8af06', 'de607ae7-ca39-4533-b47d-672320613bbc', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:13.719+00'),
	('9d1d635b-8748-48ef-8502-cda933519000', '31923b90-dd7b-420e-846b-70f4ad05b073', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:17.072+00'),
	('858d21a3-08c3-4092-9327-1da97dc47064', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:26.856+00'),
	('c7e9df99-b805-4c02-9f2f-8b76ffd2beac', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:31.504+00'),
	('8f8302e1-9a32-4fd1-ab56-c8d0685d4257', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:41.584+00'),
	('8c0ae89d-5b51-4979-a61d-ef392544e636', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:47.759+00'),
	('14a42875-a250-49a6-bb7d-27a2eab08a76', '04305fca-dab6-4123-8bf9-4d2963856a82', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:51.319+00'),
	('a2dc09d6-b478-49d0-a26b-540582baa549', '74728903-da6d-4c3a-9f66-c71dc3c70619', 'a5d6947b-b950-489b-9943-84408f1411e3', 'out', '2025-04-17 06:52:07.452+00'),
	('f66250fe-98a1-4331-8ef3-d0356c593421', '74728903-da6d-4c3a-9f66-c71dc3c70619', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-22 18:16:13.94+00'),
	('416fc251-c5e4-4811-a662-28daab8cb061', '98f532f2-510d-4e64-8a6d-63ed3e0f88e6', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-22 18:16:16.609+00'),
	('71332c4c-bdec-48a5-b831-4648ca394d7d', '531f0324-c83f-429f-9ed6-18036c5c4e95', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-22 20:13:02.069+00'),
	('4e60ee23-938a-4a6b-9445-c7f6a16d32ee', '47810abe-d3a1-491b-b9dd-9074491885be', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:52:00.703+00'),
	('ff61f2f4-4bef-4c6e-8905-e57493afde25', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:52:02.996+00'),
	('2abad8a4-8882-4b2a-9291-96b3132e7ba3', '04305fca-dab6-4123-8bf9-4d2963856a82', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:52:13.347+00'),
	('137b6091-68dc-43be-8cc4-3dd0bd318b17', '04b090ea-0d03-4157-8f16-fa743a6abe66', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:13.457+00'),
	('019e1193-17cc-461d-a32d-6317b89d999f', '5cf0e107-24bd-46f4-b249-07784bbc8632', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:14.949+00'),
	('ceb42385-e1aa-4f26-8640-6d25aa7a7a81', '94e4f959-4706-4fd9-a56f-d0a9d319e798', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:21.59+00'),
	('f1a9fbec-4bd7-403f-bda0-65adf14b49c5', 'e258c985-90ec-44fa-999c-49da6459e336', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:26.12+00'),
	('0cb421b8-d3e1-48f5-ae3b-a25e0ee2070f', '8b936fa1-3cea-4f44-9bdb-8a4387cd4672', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:27.263+00'),
	('2f902a6c-c2b9-4a50-a3f7-256851b5fbe1', 'e8bf9a1f-e826-4fad-a665-a4d2a1a6d8fc', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:34.495+00'),
	('80a6c4bf-9574-406b-b07e-235c4a590c3c', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-20 21:41:20.903+00'),
	('b0579bdd-bb7e-4509-b92a-148e4e04ae39', 'e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:37.71+00'),
	('c48c4870-895a-4691-8fb1-a7bf7beda4aa', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:40.644+00'),
	('d8c638f2-d8a3-4846-8286-8f7510d6619e', '31923b90-dd7b-420e-846b-70f4ad05b073', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:43.751+00'),
	('6eaa0fc7-9a23-453f-b4e8-65a76f400b8b', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:45.927+00'),
	('bff6ab78-8522-42b8-af07-6ca35aff3eaa', 'de607ae7-ca39-4533-b47d-672320613bbc', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:48.161+00'),
	('c924a6be-f5ad-469a-ac95-5e76f308badf', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:41:58.932+00'),
	('a3e953d9-e16b-4465-92fd-798d8a223543', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:42:00.6+00'),
	('54e922f1-60b1-4933-82f0-9aec96697ffb', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:42:06.467+00'),
	('fc954888-733d-4352-9666-2721e99cefa8', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 11:42:32.952+00'),
	('bbd35e4d-a747-451e-8d12-4dcddc7cbbd8', 'f2e992b2-bab9-40e9-bd80-710ed46d5153', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 12:47:57.478+00'),
	('4c9951d3-8e02-49e0-b9e6-09aa66e9820c', '29b55208-6056-424a-a25a-272f28851374', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-21 12:48:04.155+00'),
	('0ad4a649-cb51-49a2-9c3d-d1123ce94f9f', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:35.77+00'),
	('d96b8f50-450c-4612-891d-70092bc48718', 'b9972dad-cd54-443c-928e-a4f899ce9998', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:37.095+00'),
	('e54c01da-a00c-4897-8616-1d3e761759bc', 'f8ac085d-82eb-4eb0-8f35-2606529dc899', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:39.635+00'),
	('56305655-238e-4a77-91be-6c428291e5bb', '0ed06269-db14-4fc3-ba87-2f96f32ed24e', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:55:41.12+00'),
	('a2983ca8-1fea-450b-9bb6-3dd51565659d', '06c64ece-c34e-4623-88e5-2a623a694437', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'pending', '2025-04-23 17:56:39.971+00'),
	('c1c16718-c01d-49cd-8e36-40c52bed6d80', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:31:30.529+00'),
	('dd3396dc-0587-48e4-85ab-fecd59c0e1a4', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:31:39.067+00'),
	('00db01ff-babf-46c0-ad34-12b1263bbfcd', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:31:44.824+00'),
	('8af75eaa-7eb2-45dc-8409-8de006f54f48', '74728903-da6d-4c3a-9f66-c71dc3c70619', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:31:51.151+00'),
	('f5fae948-9292-4bbd-ac59-106a8a8d4da4', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:31:55.513+00'),
	('e05bc8d0-baca-4e83-b14b-d1a1b1583cae', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:32:02.009+00'),
	('5c26e5f7-d417-4cb8-8c74-42d9278e7145', 'e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:32:05.35+00'),
	('311956eb-00b5-48d5-b959-f40b9394553b', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:32:13.065+00'),
	('d148efc3-ef9c-4b5a-9f98-200a45bb8436', '29b55208-6056-424a-a25a-272f28851374', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:32:17.16+00'),
	('0abb596e-2b86-41b1-be8d-9b0668772eec', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 16:32:19.881+00'),
	('1dc314c2-e645-43fb-8c2d-558cb36ebe60', '04305fca-dab6-4123-8bf9-4d2963856a82', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1', 'pending', '2025-04-29 20:47:45.016+00'),
	('1cfeb03d-0b44-40ae-830b-7e0dbb77205f', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:06:38.351+00'),
	('f6f88e75-b3b7-4acb-85cd-6baeb8702e08', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:06:44.02+00'),
	('7bfcc2d9-c30a-4887-b75a-2a4cc87dd50c', '531f0324-c83f-429f-9ed6-18036c5c4e95', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:06:51.31+00'),
	('260c9fe8-77fe-4461-9926-78f3c5d9cb4c', '04305fca-dab6-4123-8bf9-4d2963856a82', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:06:56.914+00'),
	('250aeb2e-ba25-41b4-ab13-3e6c99f969bf', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:07:01.916+00'),
	('a0c8b35b-bde7-4861-a084-dfc58b39eaa0', '74728903-da6d-4c3a-9f66-c71dc3c70619', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-05 15:07:05.403+00'),
	('84001c54-3148-465b-b226-8ef922678de4', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:42:20.639+00'),
	('3e38c565-a24b-40d7-a917-0b3447d2ade0', '5002252f-d354-4c90-a01c-902f4d36bfa2', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:42:54.602+00'),
	('da0048f1-37e7-41e6-be32-7628edfd56c6', 'fa1ca7de-f8e0-42c1-92c5-9a5244934bb0', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:43:20.268+00'),
	('f5d63f07-d01e-40a5-b023-8c88519b43fd', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:43:29.669+00'),
	('1e53820a-2ceb-4322-b050-688c17830c17', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:43:55.619+00'),
	('16170cb2-8f61-4713-a580-aca909feda0a', '56990edf-853e-4ecb-ab88-17a7c1b10a0b', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:44:05.142+00'),
	('15438a48-5097-4dcd-8407-9e51fa37f0e3', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:44:16.303+00'),
	('990af8ce-bf3a-4fde-98c7-d85909e6e051', '58c896fc-3282-4b64-b0a4-ab5954cedbab', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:47:05.046+00'),
	('7066159e-7226-47a3-bb0d-78eb20f4e741', '47810abe-d3a1-491b-b9dd-9074491885be', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:53:17.626+00'),
	('8d11f2c6-8357-4682-8b5c-e86549671cdb', '31923b90-dd7b-420e-846b-70f4ad05b073', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:53:22.02+00'),
	('8122e002-3e3a-437a-8604-100d2f354456', '5cf0e107-24bd-46f4-b249-07784bbc8632', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:53:25.142+00'),
	('52012a75-777c-4cb7-a815-465746818d17', '04b090ea-0d03-4157-8f16-fa743a6abe66', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-06 07:53:27.906+00'),
	('8b22a618-018b-431f-aa7a-ceb71eddf586', '60e007b7-79f4-48b1-b31b-052a1547b96b', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-07 10:27:03.139+00'),
	('04cdff86-075d-49b9-a3f7-8159c50b2e9a', 'e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36', 'pending', '2025-05-08 21:28:52.337+00'),
	('28552093-78ea-48d5-82bf-53d3fcc6281e', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:52:39.021+00'),
	('47759422-3b3e-4847-8a85-679f7063b943', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:52:42.17+00'),
	('cfd708b0-79fb-46c5-8ec2-cbaf075ea03e', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:52:47.304+00'),
	('0b14bc89-a3bd-425d-ad85-81c6d2be2043', 'e502a4c1-f5cb-4e80-8b29-edacf9862dab', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:52:52.343+00'),
	('f547f2c0-cf83-4947-8743-e4650d6ab079', '531f0324-c83f-429f-9ed6-18036c5c4e95', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:56:28.874+00'),
	('4fa7651d-dfb1-4881-8bfd-a41dab507ec0', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:56:58.436+00'),
	('a94fb4ca-1dc8-499b-972a-8ded8bf5d0ee', 'de607ae7-ca39-4533-b47d-672320613bbc', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:58:24.949+00'),
	('cd6fbb38-e58e-460b-8bf7-8054c92764f2', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:59:19.582+00'),
	('264f3861-f6b4-47db-932a-2dd5973beea0', '0ed06269-db14-4fc3-ba87-2f96f32ed24e', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 09:59:56.223+00'),
	('6778311a-2a3f-4519-a573-3bd2962ada2b', '74241d83-1fe8-4869-81d2-705c559c9139', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:03:04.103+00'),
	('8f577d47-2628-4020-b1e7-16a0fd82ce16', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 10:04:57.09+00'),
	('68824c42-60e0-4cde-8f93-339696d849e9', '58c896fc-3282-4b64-b0a4-ab5954cedbab', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 10:05:01.713+00'),
	('05e3676f-7bee-421b-9846-aaccbfc70803', '31923b90-dd7b-420e-846b-70f4ad05b073', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:05:10.092+00'),
	('8078ca78-d7f6-4bbd-acf1-9c1c73ba66d7', '47810abe-d3a1-491b-b9dd-9074491885be', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:05:19.156+00'),
	('85e1cd71-7087-4bea-ab4c-45e877209a5a', '74728903-da6d-4c3a-9f66-c71dc3c70619', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 10:05:54.25+00'),
	('28108c51-a805-40b2-b9e1-6d1dd21ded5a', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:23:45.72+00'),
	('06e3a2dc-a348-45a5-ade9-ed7ce6bed196', '04b090ea-0d03-4157-8f16-fa743a6abe66', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:26:26.555+00'),
	('c4347825-a399-46ee-abe8-fe54242238a2', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:42:17.841+00'),
	('1cc590e4-0088-43de-bcba-45bb0f9226f5', 'fa1ca7de-f8e0-42c1-92c5-9a5244934bb0', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 10:49:08.382+00'),
	('c178ab0d-e477-4cd3-af69-fe50db9f4122', '60e007b7-79f4-48b1-b31b-052a1547b96b', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 10:49:17.482+00'),
	('6f2fef1f-15a9-44b9-aa1a-2a04c7b7001e', '94e4f959-4706-4fd9-a56f-d0a9d319e798', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 10:59:14.551+00'),
	('94d4870e-cb64-4f99-8745-55cb1d1395ed', '98f532f2-510d-4e64-8a6d-63ed3e0f88e6', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 11:05:32.348+00'),
	('996c2de7-bdff-4c7c-86bd-4d7f5e4e553f', '04305fca-dab6-4123-8bf9-4d2963856a82', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'in', '2025-05-12 16:51:42.747+00'),
	('ffbb8932-05e1-481d-a4fb-304f06642968', 'daf57809-6d25-42c2-9916-521b16952506', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 18:38:31.446+00'),
	('ad7d4285-486b-430c-aa74-b55b6a2eb482', '5002252f-d354-4c90-a01c-902f4d36bfa2', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 18:40:10.056+00'),
	('618d86f2-8208-4da4-8d5a-2c8b5282c319', 'e258c985-90ec-44fa-999c-49da6459e336', '0472273e-44ee-4c3f-aae4-9b94933fa1e9', 'out', '2025-05-12 18:40:22.938+00');


--
-- Data for Name: team_players; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."team_players" ("id", "team_id", "player_id", "game_session_id") VALUES
	('f4f96804-3acc-46e7-bfda-e37bbdb05ff3', 'e4164a5d-7922-42e7-9165-41a0a5728b88', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('e0ee514b-840c-4dda-ae93-9a4df3209353', 'e4164a5d-7922-42e7-9165-41a0a5728b88', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('3fce0fbc-90cf-48ce-b0e4-e136ffe4e21d', 'e4164a5d-7922-42e7-9165-41a0a5728b88', 'e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('8adde8d7-9eff-4748-9347-f5fd61404733', 'e4164a5d-7922-42e7-9165-41a0a5728b88', '29b55208-6056-424a-a25a-272f28851374', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('aa370c81-4932-40d0-bbcc-e5fe3f70d640', 'e4164a5d-7922-42e7-9165-41a0a5728b88', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('c8835efd-775b-4194-a223-f81f57a7dc96', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('b263423d-6d59-48da-969a-8b682f9b337d', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('ff88c80c-eb80-4d06-b6e9-1c8199541f4c', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('b4b7aaa8-5635-461e-9af5-df03aa2df76a', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('3a5c5fd2-58fa-4b5e-b034-995e9110419c', '922e0327-cabb-4e87-a0bd-8d35b1428a9a', '74728903-da6d-4c3a-9f66-c71dc3c70619', '4b6b29f9-7827-47f6-8bc2-c6f5f4c0cef1'),
	('73584574-ee41-4fc0-8ea8-12cb01be3496', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', '5002252f-d354-4c90-a01c-902f4d36bfa2', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('30d8eda5-f5cb-46b0-8e84-24de0bfb7747', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('72047ce4-93b6-4d30-a050-a6febfaf5f7a', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', '04305fca-dab6-4123-8bf9-4d2963856a82', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('0a02f74f-e6ce-45a4-938d-c3bc2f2d4752', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('3644d05d-02eb-453b-9624-695b412510e3', '19528ee8-fe9c-4fa7-a84d-201fa09d58de', 'fa1ca7de-f8e0-42c1-92c5-9a5244934bb0', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('3b47fb7f-d0e5-458d-b1a2-04ac919fb5c7', '4d342b15-5598-453e-be39-ee3f96ba1313', '74728903-da6d-4c3a-9f66-c71dc3c70619', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('a2de9dd7-0563-4d58-a32b-2ce2964cb53a', '4d342b15-5598-453e-be39-ee3f96ba1313', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('192f8304-37e9-42eb-b3bc-00d62ccd0128', '4d342b15-5598-453e-be39-ee3f96ba1313', '531f0324-c83f-429f-9ed6-18036c5c4e95', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('04f510e3-de74-449e-819c-bfdd0457e43a', '4d342b15-5598-453e-be39-ee3f96ba1313', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('657d13c0-3d8b-4e28-90fc-c0d329546b51', '4d342b15-5598-453e-be39-ee3f96ba1313', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'de5dac63-4ad8-4d6f-8af0-b4837b408c36'),
	('561b92c2-9f1d-4c20-a834-29076bc0516c', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('e1eccec9-b713-41c5-8d56-1a9b07c309f9', '69d81abb-aa08-4030-9ccf-a28298ebac41', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('3a292f83-54db-4794-a2b1-5c239a504d82', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('443d5bec-3e3c-41e9-8357-4f119cd843f0', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'de607ae7-ca39-4533-b47d-672320613bbc', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('7d802a02-0bec-4c89-b4e9-f61f8d43c6f6', '69d81abb-aa08-4030-9ccf-a28298ebac41', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('2cafdb3c-0d0f-46f1-8b2d-b07539b00d63', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('1210a18a-67fb-472d-9447-bfe93e860d86', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('49fd6bcb-7845-455c-b218-ddf4a60d141e', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '31923b90-dd7b-420e-846b-70f4ad05b073', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('31b0a4f5-f81a-495b-8646-f1bc5dee4fb6', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('c046294e-1f9f-4496-ae02-0472f675175c', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('02c21c07-c86d-421f-a00a-98a58635b98c', '476fddbf-07ea-442e-a327-b17f47c27bac', 'f2e992b2-bab9-40e9-bd80-710ed46d5153', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('e09bf601-46ed-4da9-9892-c5563a11633e', '476fddbf-07ea-442e-a327-b17f47c27bac', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('18150c56-631f-4a65-be91-c75ebbb6bafb', '476fddbf-07ea-442e-a327-b17f47c27bac', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('1de74c6b-602b-4550-b959-ab35709945f7', '476fddbf-07ea-442e-a327-b17f47c27bac', '04305fca-dab6-4123-8bf9-4d2963856a82', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('1f73dc37-ae60-4a24-9b14-5caa60e19075', 'fbdfa2a1-be27-4f4d-8ae3-46729a46bcd0', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('51475b02-80b1-4efd-90c5-a497d8ccb31c', 'fbdfa2a1-be27-4f4d-8ae3-46729a46bcd0', '74728903-da6d-4c3a-9f66-c71dc3c70619', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('1703dca2-7a61-4e70-84ee-eaccb020a276', 'fbdfa2a1-be27-4f4d-8ae3-46729a46bcd0', '29b55208-6056-424a-a25a-272f28851374', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('b123fe9d-309c-4fe7-9f52-41b872aead20', 'd0c2b11e-3b5f-4307-bfec-3483ca72204f', 'de607ae7-ca39-4533-b47d-672320613bbc', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('c02095ef-0f1e-47de-ae37-aa581be116f8', 'd0c2b11e-3b5f-4307-bfec-3483ca72204f', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('354c8550-5de3-4b2a-a22b-4247938b9381', 'd0c2b11e-3b5f-4307-bfec-3483ca72204f', 'e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('7eb0d8ef-ce29-49ed-81f3-f27e535d94e2', 'd0c2b11e-3b5f-4307-bfec-3483ca72204f', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('a9419d9f-c086-495c-a6e0-8f0c163a9316', 'd0c2b11e-3b5f-4307-bfec-3483ca72204f', '47810abe-d3a1-491b-b9dd-9074491885be', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('04507858-a46e-44de-8eea-0a5f33f98809', 'd9a93eac-2990-40a4-9a47-08ba639b3ed3', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('8f3222ff-1028-4810-8380-cf39034f2848', 'd9a93eac-2990-40a4-9a47-08ba639b3ed3', '31923b90-dd7b-420e-846b-70f4ad05b073', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('0e750aba-b652-49bb-86f9-2b91d9e0d978', 'd9a93eac-2990-40a4-9a47-08ba639b3ed3', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('d1e15b6c-bc86-42ad-90a7-2fd6ddaaf6ee', 'd9a93eac-2990-40a4-9a47-08ba639b3ed3', '98f532f2-510d-4e64-8a6d-63ed3e0f88e6', '402d65eb-fe84-487c-b362-e4f98684d2bb'),
	('d16dfd54-afff-454d-870c-9f18c1a59ca3', 'd9a93eac-2990-40a4-9a47-08ba639b3ed3', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', '402d65eb-fe84-487c-b362-e4f98684d2bb');


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--



--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 1, false);


--
-- PostgreSQL database dump complete
--

RESET ALL;
