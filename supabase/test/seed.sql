SET session_replication_role = replica;

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: admin_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."admin_users" ("id", "username", "password_hash", "created_at") VALUES
	('3141b3bb-6947-4970-b2ff-75794501873a', 'admin', 'interNRW2025', '2025-04-16 06:52:53.822857+00');


--
-- Data for Name: app_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."app_settings" ("id", "key", "value", "created_at", "updated_at") VALUES
	('3ca99dd3-5bd2-4e17-8e66-3e1170d7a1bf', 'app_pin', '1234', '2025-04-17 23:39:03.252811+00', '2025-04-17 23:39:03.252811+00');


--
-- Data for Name: game_sessions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."game_sessions" ("id", "date", "signup_opens_at", "is_signup_open", "is_team_generated", "created_at", "updated_at", "status") VALUES
	('402d65eb-fe84-487c-b362-e4f98684d2bb', '2025-04-25 19:00:00+00', '2025-04-21 10:00:00+00', true, false, '2025-04-17 16:41:31.41192+00', '2025-04-17 16:41:31.41192+00', 'scheduled'),
	('a5d6947b-b950-489b-9943-84408f1411e3', '2025-04-17 19:00:00+00', '2025-04-13 10:00:00+00', false, true, '2025-04-16 20:51:04.976416+00', '2025-04-16 20:51:04.976416+00', 'played');


--
-- Data for Name: players; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."players" ("id", "name", "jersey_number", "rating", "created_at", "updated_at") VALUES
	('92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'Muammer Cakir', 89, 79, '2025-04-16 08:36:07.703104+00', '2025-04-16 08:36:07.703104+00'),
	('9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'Akin Zengin', 50, 88, '2025-04-16 08:38:57.690786+00', '2025-04-16 08:38:57.690786+00'),
	('04b090ea-0d03-4157-8f16-fa743a6abe66', 'Aykut Oktay', 54, 81, '2025-04-16 13:30:45.380672+00', '2025-04-16 13:30:45.380672+00'),
	('31923b90-dd7b-420e-846b-70f4ad05b073', 'Berat Kaya', 10, 83, '2025-04-16 08:44:14.23959+00', '2025-04-16 08:44:14.23959+00'),
	('fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'Erdem Karaagil', 6, 87, '2025-04-16 13:06:51.604496+00', '2025-04-16 13:06:51.604496+00'),
	('2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'Burak Ceyhan', 50, 74, '2025-04-16 13:28:26.112591+00', '2025-04-16 13:28:26.112591+00'),
	('e63bb596-1fd5-4e5f-8e08-964b3af8b0c3', 'Adem Kaya', 63, 86, '2025-04-16 13:06:42.214242+00', '2025-04-16 13:06:42.214242+00'),
	('74728903-da6d-4c3a-9f66-c71dc3c70619', 'Tevfik Dincer', 64, 90, '2025-04-16 13:28:51.494522+00', '2025-04-16 13:28:51.494522+00'),
	('531f0324-c83f-429f-9ed6-18036c5c4e95', 'Ahmed Abara', 9, 89, '2025-04-16 19:12:23.628963+00', '2025-04-16 19:12:23.628963+00'),
	('e258c985-90ec-44fa-999c-49da6459e336', 'Ersen Menekse', 27, 88, '2025-04-16 19:12:49.991178+00', '2025-04-16 19:12:49.991178+00'),
	('6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'Ersin Basol', 10, 90, '2025-04-16 08:39:18.644618+00', '2025-04-16 08:39:18.644618+00'),
	('cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'Kamil Dülger', 7, 90, '2025-04-16 08:36:33.789487+00', '2025-04-16 08:36:33.789487+00'),
	('7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'Samet Kockar', 10, 89, '2025-04-16 08:44:20.007482+00', '2025-04-16 08:44:20.007482+00'),
	('e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'Onur Batur', 16, 82, '2025-04-16 08:44:10.024766+00', '2025-04-16 08:44:10.024766+00'),
	('f612cc5a-570f-4abf-aa5b-ba8a73450931', 'Shah Sayed', 5, 77, '2025-04-16 08:44:35.275696+00', '2025-04-16 08:44:35.275696+00'),
	('9fe9fa4a-99db-4b12-8702-d347a08d98c4', 'Esref Kapucu', 34, 84, '2025-04-16 08:44:05.091422+00', '2025-04-16 08:44:05.091422+00'),
	('04305fca-dab6-4123-8bf9-4d2963856a82', 'Viktor Reich', 8, 83, '2025-04-16 13:07:20.323803+00', '2025-04-16 13:07:20.323803+00'),
	('5cf0e107-24bd-46f4-b249-07784bbc8632', 'Bent Exner', 3, 87, '2025-04-16 19:17:27.7509+00', '2025-04-16 19:17:27.7509+00'),
	('29b55208-6056-424a-a25a-272f28851374', 'Ismail Bas', 64, 83, '2025-04-16 19:17:59.978486+00', '2025-04-16 19:17:59.978486+00'),
	('94e4f959-4706-4fd9-a56f-d0a9d319e798', 'Jobin Karapurackal', 23, 87, '2025-04-16 19:18:20.278598+00', '2025-04-16 19:18:20.278598+00'),
	('f2e992b2-bab9-40e9-bd80-710ed46d5153', 'Kenan Zengin', 50, 88, '2025-04-16 19:18:43.14379+00', '2025-04-16 19:18:43.14379+00'),
	('0ed06269-db14-4fc3-ba87-2f96f32ed24e', 'Markus Frank', 6, 91, '2025-04-16 19:19:26.52448+00', '2025-04-16 19:19:26.52448+00'),
	('98f532f2-510d-4e64-8a6d-63ed3e0f88e6', 'Samir Bouajaj', 11, 88, '2025-04-16 19:20:29.255991+00', '2025-04-16 19:20:29.255991+00'),
	('b9972dad-cd54-443c-928e-a4f899ce9998', 'Semih Oktay', 54, 78, '2025-04-16 19:20:54.619897+00', '2025-04-16 19:20:54.619897+00'),
	('e8bf9a1f-e826-4fad-a665-a4d2a1a6d8fc', 'Tayfun Cakir', 8, 78, '2025-04-16 19:21:20.632989+00', '2025-04-16 19:21:20.632989+00'),
	('de607ae7-ca39-4533-b47d-672320613bbc', 'Emre Karadayi', 25, 81, '2025-04-16 08:44:28.272556+00', '2025-04-16 08:44:28.272556+00'),
	('47810abe-d3a1-491b-b9dd-9074491885be', 'Brahim El-Bakali', 99, 90, '2025-04-16 19:22:02.760792+00', '2025-04-16 19:22:02.760792+00'),
	('f8ac085d-82eb-4eb0-8f35-2606529dc899', 'Mouhcine Ben Aomar', 99, 86, '2025-04-16 19:22:44.046509+00', '2025-04-16 19:22:44.046509+00'),
	('8b936fa1-3cea-4f44-9bdb-8a4387cd4672', 'Ermond Ademaj', 99, 91, '2025-04-16 19:21:45.295122+00', '2025-04-16 19:21:45.295122+00'),
	('34e970d7-4f43-4e0e-9697-569abfe0b8db', 'Youssef Ahdour', 99, 78, '2025-04-16 19:22:19.295267+00', '2025-04-16 19:22:19.295267+00');


--
-- Data for Name: player_signups; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."player_signups" ("id", "player_id", "game_session_id", "status", "signup_time") VALUES
	('a49e2cfc-d221-4491-aa00-ef0114c7efe4', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:52:42.783+00'),
	('4167b2c4-8f6f-449f-9111-50a1028b94d4', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:52:51.074+00'),
	('0521bea3-b08c-422d-ae87-406c4061e42d', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:00.255+00'),
	('bc827a72-5cac-4fc5-8e58-db23e9c74fe8', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:03.216+00'),
	('f84bc022-2c71-4572-8209-9278205deabf', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:11.4+00'),
	('05f3d384-0bbf-4cf3-926d-5cd0a1c8af06', 'de607ae7-ca39-4533-b47d-672320613bbc', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:13.719+00'),
	('9d1d635b-8748-48ef-8502-cda933519000', '31923b90-dd7b-420e-846b-70f4ad05b073', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:17.072+00'),
	('858d21a3-08c3-4092-9327-1da97dc47064', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:26.856+00'),
	('c7e9df99-b805-4c02-9f2f-8b76ffd2beac', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:31.504+00'),
	('8f8302e1-9a32-4fd1-ab56-c8d0685d4257', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:41.584+00'),
	('8c0ae89d-5b51-4979-a61d-ef392544e636', 'f612cc5a-570f-4abf-aa5b-ba8a73450931', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:47.759+00'),
	('14a42875-a250-49a6-bb7d-27a2eab08a76', '04305fca-dab6-4123-8bf9-4d2963856a82', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:53:51.319+00'),
	('f4bf04c8-9e07-4d8e-8f17-ca2a6539f51f', '9fe9fa4a-99db-4b12-8702-d347a08d98c4', 'a5d6947b-b950-489b-9943-84408f1411e3', 'in', '2025-04-16 20:54:00.344+00'),
	('a2dc09d6-b478-49d0-a26b-540582baa549', '74728903-da6d-4c3a-9f66-c71dc3c70619', 'a5d6947b-b950-489b-9943-84408f1411e3', 'out', '2025-04-17 06:52:07.452+00'),
	('a3e953d9-e16b-4465-92fd-798d8a223543', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'out', '2025-04-17 22:46:20.214+00'),
	('80a6c4bf-9574-406b-b07e-235c4a590c3c', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', '402d65eb-fe84-487c-b362-e4f98684d2bb', 'in', '2025-04-20 21:41:20.903+00');


--
-- Data for Name: push_subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."push_subscriptions" ("id", "endpoint", "keys", "created_at", "last_used") VALUES
	('9f2df1e1-5897-4a2e-8741-90324b81f7a3', 'https://fcm.googleapis.com/fcm/send/fX1ZuvG8lx0:APA91bE0R3tL43RtCwP0XMKvWYHt2qZUeRaw7yIHWOJYhz_v_TiKX_LEB2A9NottdTP5RISB15EnvWoFYJjiR-_njL4U-U-h1ptB516HN1i3paAlgdArbWbi_0DL0svplLRpucsjdQqN', '{"p256dh":"BHui9fcA16WMXjZxiwuTwSA3eBn60Fw1bh6LBfzWNNHMVAXGPmjk-t_duYJwBYUUdWAuFIbfM5YwiWhJpnmh9FA","auth":"ho-yHxlqMsWh5NOlza_TFQ"}', '2025-04-16 21:48:55.034+00', NULL),
	('7e240b03-e6ba-47d6-afa7-5f6c8044b652', 'https://fcm.googleapis.com/fcm/send/ekA9vvrFAYI:APA91bERQ-Y2u0cTFczQnFTb6OKxa1jd5v5A_6y8eM8i5THk9MrZJ5Ow5ybcoWSlcBrLCjYCDHsNjmjP2MxZHfuz5FNADVGoaIVF9tillLpocQIC7NuHzGCSAtI5jMblGRazdV7MmDz6', '{"p256dh":"BIr63o-4p_zIkrmk6uKqnlu0xi78q1kYBHO_1vmTflcBAE5rO-HXbTP8BnnyiqRCb7fFCshzbeYuKnV4Dc2Z6Gg","auth":"UY_Z4bcmLzk3xVdgzlsWGg"}', '2025-04-16 21:49:57.242+00', NULL),
	('9fef91ec-6053-4565-8a4f-ed4e4bfc6d5f', 'https://fcm.googleapis.com/fcm/send/eo_Ja02hbvI:APA91bFZF13u_4T9Erj1JSpNdRRF-sAeBpFxId_0KTJ3m_fRAn8RNtaNXx-s_0TGV_zFBJhRZJ5HxpXlA6J4JO1EA2hl62u3LHVAymidXD4-rjbya1i_IHe8yutDeKjr8Q1fwmjPOOg0', '{"p256dh":"BGbDzuLBXeEZNJxfNftI-fMJCqditlEaeA_yIlssLwUSEl31n3nYJdBNfnb9MKUwgvAXD_eWIzKAa5gpkhzdDm8","auth":"OXwS8uVvvGgafnloZLFqSA"}', '2025-04-16 22:02:42.759+00', NULL);


--
-- Data for Name: teams; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."teams" ("id", "game_session_id", "name", "average_rating", "created_at") VALUES
	('69d81abb-aa08-4030-9ccf-a28298ebac41', 'a5d6947b-b950-489b-9943-84408f1411e3', 'Team A', 85.00, '2025-04-17 11:34:02.076461+00'),
	('0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', 'a5d6947b-b950-489b-9943-84408f1411e3', 'Team B', 83.60, '2025-04-17 11:34:02.303622+00');


--
-- Data for Name: team_players; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."team_players" ("id", "team_id", "player_id", "game_session_id") VALUES
	('561b92c2-9f1d-4c20-a834-29076bc0516c', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'cd302ffa-59af-4237-b2d6-ddd7494b26b6', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('e1eccec9-b713-41c5-8d56-1a9b07c309f9', '69d81abb-aa08-4030-9ccf-a28298ebac41', '9856cbdd-8329-44da-9eff-2c03ba29d8d0', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('3a292f83-54db-4794-a2b1-5c239a504d82', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'fa49b40c-9c3c-4ca3-b208-d3b5625eaaa2', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('443d5bec-3e3c-41e9-8357-4f119cd843f0', '69d81abb-aa08-4030-9ccf-a28298ebac41', 'de607ae7-ca39-4533-b47d-672320613bbc', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('7d802a02-0bec-4c89-b4e9-f61f8d43c6f6', '69d81abb-aa08-4030-9ccf-a28298ebac41', '92f018ea-d045-4ec4-889a-1f5ae8c9b411', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('2cafdb3c-0d0f-46f1-8b2d-b07539b00d63', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '6836ecd7-2fc4-4cac-8d34-2edd4769a036', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('1210a18a-67fb-472d-9447-bfe93e860d86', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '7a612e27-6c6e-4cfb-9fdc-52225d190bc3', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('49fd6bcb-7845-455c-b218-ddf4a60d141e', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '31923b90-dd7b-420e-846b-70f4ad05b073', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('31b0a4f5-f81a-495b-8646-f1bc5dee4fb6', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', 'e78aadbb-5162-4550-b3ed-cdfb74a0e373', 'a5d6947b-b950-489b-9943-84408f1411e3'),
	('c046294e-1f9f-4496-ae02-0472f675175c', '0facc2b6-0a4d-4dbd-8031-5aa04f4cabab', '2c7d6946-1ed2-4c57-8464-4f7d79f5a8df', 'a5d6947b-b950-489b-9943-84408f1411e3');


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--



--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 1, false);


--
-- PostgreSQL database dump complete
--

RESET ALL;
