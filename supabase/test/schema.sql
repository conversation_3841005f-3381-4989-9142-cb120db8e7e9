

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_cron" WITH SCHEMA "pg_catalog";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "public";






CREATE SCHEMA IF NOT EXISTS "test";


ALTER SCHEMA "test" OWNER TO "postgres";


CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."delete_push_subscription"("endpoint_param" "text") RETURNS "void"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  DELETE FROM push_subscriptions WHERE endpoint = endpoint_param;
$$;


ALTER FUNCTION "public"."delete_push_subscription"("endpoint_param" "text") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."push_subscriptions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "endpoint" "text" NOT NULL,
    "keys" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "last_used" timestamp with time zone
);


ALTER TABLE "public"."push_subscriptions" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_all_push_subscriptions"() RETURNS SETOF "public"."push_subscriptions"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT * FROM push_subscriptions;
$$;


ALTER FUNCTION "public"."get_all_push_subscriptions"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_push_subscription"("endpoint_param" "text") RETURNS SETOF "public"."push_subscriptions"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT * FROM push_subscriptions WHERE endpoint = endpoint_param;
$$;


ALTER FUNCTION "public"."get_push_subscription"("endpoint_param" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_game_session_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  notification_type TEXT;
  notification_title TEXT;
  notification_body TEXT;
  game_date TEXT;
  game_time TEXT;
BEGIN
  -- Format date for notification
  game_date := to_char(NEW.date, 'DD.MM.YYYY');
  game_time := to_char(NEW.date, 'HH24:MI');
  
  -- Determine notification type and content
  IF TG_OP = 'INSERT' AND NEW.is_signup_open = true THEN
    -- New game with signup open
    notification_type := 'signup';
    notification_title := 'Neue Spielanmeldung geöffnet!';
    notification_body := 'Die Anmeldung für das Spiel am ' || game_date || ' um ' || game_time || ' Uhr ist jetzt geöffnet.';
  ELSIF TG_OP = 'UPDATE' AND OLD.is_signup_open = false AND NEW.is_signup_open = true THEN
    -- Signup opened
    notification_type := 'signup';
    notification_title := 'Spielanmeldung geöffnet!';
    notification_body := 'Die Anmeldung für das Spiel am ' || game_date || ' um ' || game_time || ' Uhr ist jetzt geöffnet.';
  ELSIF TG_OP = 'UPDATE' AND OLD.status <> 'cancelled' AND NEW.status = 'cancelled' THEN
    -- Game cancelled
    notification_type := 'cancel';
    notification_title := 'Spiel abgesagt!';
    notification_body := 'Das Spiel am ' || game_date || ' wurde abgesagt.';
  ELSIF TG_OP = 'DELETE' THEN
    -- Game deleted
    notification_type := 'cancel';
    notification_title := 'Spiel abgesagt!';
    notification_body := 'Ein geplantes Spiel wurde abgesagt.';
  ELSE
    -- No notification needed
    RETURN NEW;
  END IF;
  
  -- Only proceed if we have a notification to send
  IF notification_type IS NOT NULL THEN
    -- Call the edge function to send notification
    PERFORM net.http_post(
      url:='https://ztqiadbfgjcxlnsdeycb.supabase.co/functions/v1/send-notification',
      headers:='{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp0cWlhZGJmZ2pjeGxuc2RleWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3NDk0MjAsImV4cCI6MjA2MDMyNTQyMH0.aEAEryuMfAdc6sK34Ka0uxkAm3RwTeTTIhZ1_jZGe-w"}'::jsonb,
      body:=json_build_object(
        'notification', json_build_object(
          'title', notification_title,
          'body', notification_body,
          'data', json_build_object(
            'url', '/',
            'gameId', COALESCE(NEW.id, OLD.id)
          )
        )
      )::jsonb
    );
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_game_session_changes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_push_subscription"("endpoint_param" "text", "keys_param" "text", "created_at_param" timestamp with time zone) RETURNS "void"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  INSERT INTO push_subscriptions (endpoint, keys, created_at)
  VALUES (endpoint_param, keys_param, created_at_param);
$$;


ALTER FUNCTION "public"."insert_push_subscription"("endpoint_param" "text", "keys_param" "text", "created_at_param" timestamp with time zone) OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."admin_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "username" "text" NOT NULL,
    "password_hash" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."admin_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."app_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."app_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."game_sessions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "date" timestamp with time zone NOT NULL,
    "signup_opens_at" timestamp with time zone NOT NULL,
    "is_signup_open" boolean DEFAULT false,
    "is_team_generated" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "status" "text" DEFAULT 'scheduled'::"text",
    CONSTRAINT "game_sessions_status_check" CHECK (("status" = ANY (ARRAY['scheduled'::"text", 'played'::"text", 'cancelled'::"text", 'archived'::"text"])))
);


ALTER TABLE "public"."game_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."player_signups" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "player_id" "uuid" NOT NULL,
    "game_session_id" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "signup_time" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "player_signups_status_check" CHECK (("status" = ANY (ARRAY['in'::"text", 'out'::"text", 'pending'::"text"])))
);


ALTER TABLE "public"."player_signups" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."players" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "jersey_number" integer,
    "rating" integer DEFAULT 50,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "players_rating_check" CHECK ((("rating" >= 1) AND ("rating" <= 99)))
);


ALTER TABLE "public"."players" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."team_players" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "team_id" "uuid" NOT NULL,
    "player_id" "uuid" NOT NULL,
    "game_session_id" "uuid" NOT NULL
);


ALTER TABLE "public"."team_players" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "game_session_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "average_rating" numeric(5,2),
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."teams" OWNER TO "postgres";


ALTER TABLE ONLY "public"."admin_users"
    ADD CONSTRAINT "admin_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."admin_users"
    ADD CONSTRAINT "admin_users_username_key" UNIQUE ("username");



ALTER TABLE ONLY "public"."app_settings"
    ADD CONSTRAINT "app_settings_key_key" UNIQUE ("key");



ALTER TABLE ONLY "public"."app_settings"
    ADD CONSTRAINT "app_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."game_sessions"
    ADD CONSTRAINT "game_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."player_signups"
    ADD CONSTRAINT "player_signups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."player_signups"
    ADD CONSTRAINT "player_signups_player_id_game_session_id_key" UNIQUE ("player_id", "game_session_id");



ALTER TABLE ONLY "public"."players"
    ADD CONSTRAINT "players_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."push_subscriptions"
    ADD CONSTRAINT "push_subscriptions_endpoint_key" UNIQUE ("endpoint");



ALTER TABLE ONLY "public"."push_subscriptions"
    ADD CONSTRAINT "push_subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_players"
    ADD CONSTRAINT "team_players_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."team_players"
    ADD CONSTRAINT "team_players_team_id_player_id_game_session_id_key" UNIQUE ("team_id", "player_id", "game_session_id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_push_subscriptions_endpoint" ON "public"."push_subscriptions" USING "btree" ("endpoint");



CREATE OR REPLACE TRIGGER "game_session_delete_trigger" BEFORE DELETE ON "public"."game_sessions" FOR EACH ROW EXECUTE FUNCTION "public"."handle_game_session_changes"();



CREATE OR REPLACE TRIGGER "game_session_insert_trigger" AFTER INSERT ON "public"."game_sessions" FOR EACH ROW EXECUTE FUNCTION "public"."handle_game_session_changes"();



CREATE OR REPLACE TRIGGER "game_session_update_trigger" AFTER UPDATE ON "public"."game_sessions" FOR EACH ROW EXECUTE FUNCTION "public"."handle_game_session_changes"();



ALTER TABLE ONLY "public"."player_signups"
    ADD CONSTRAINT "player_signups_game_session_id_fkey" FOREIGN KEY ("game_session_id") REFERENCES "public"."game_sessions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."player_signups"
    ADD CONSTRAINT "player_signups_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "public"."players"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_players"
    ADD CONSTRAINT "team_players_game_session_id_fkey" FOREIGN KEY ("game_session_id") REFERENCES "public"."game_sessions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_players"
    ADD CONSTRAINT "team_players_player_id_fkey" FOREIGN KEY ("player_id") REFERENCES "public"."players"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."team_players"
    ADD CONSTRAINT "team_players_team_id_fkey" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_game_session_id_fkey" FOREIGN KEY ("game_session_id") REFERENCES "public"."game_sessions"("id") ON DELETE CASCADE;



CREATE POLICY "Allow full access to game_sessions" ON "public"."game_sessions" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to player_signups" ON "public"."player_signups" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to players" ON "public"."players" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to team_players" ON "public"."team_players" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to teams" ON "public"."teams" USING (true) WITH CHECK (true);



CREATE POLICY "Allow select for all" ON "public"."game_sessions" FOR SELECT USING (true);



CREATE POLICY "Allow select for all" ON "public"."player_signups" FOR SELECT USING (true);



CREATE POLICY "Allow select for all" ON "public"."players" FOR SELECT USING (true);



CREATE POLICY "Allow select for all" ON "public"."team_players" FOR SELECT USING (true);



CREATE POLICY "Allow select for all" ON "public"."teams" FOR SELECT USING (true);



ALTER TABLE "public"."game_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."player_signups" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."players" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."team_players" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."game_sessions";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."player_signups";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."players";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."team_players";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."teams";






GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



































































































































































































GRANT ALL ON FUNCTION "public"."delete_push_subscription"("endpoint_param" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."delete_push_subscription"("endpoint_param" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_push_subscription"("endpoint_param" "text") TO "service_role";



GRANT ALL ON TABLE "public"."push_subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."push_subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."push_subscriptions" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_all_push_subscriptions"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_all_push_subscriptions"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_all_push_subscriptions"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_push_subscription"("endpoint_param" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_push_subscription"("endpoint_param" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_push_subscription"("endpoint_param" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_game_session_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_game_session_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_game_session_changes"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_push_subscription"("endpoint_param" "text", "keys_param" "text", "created_at_param" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."insert_push_subscription"("endpoint_param" "text", "keys_param" "text", "created_at_param" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_push_subscription"("endpoint_param" "text", "keys_param" "text", "created_at_param" timestamp with time zone) TO "service_role";
























GRANT ALL ON TABLE "public"."admin_users" TO "anon";
GRANT ALL ON TABLE "public"."admin_users" TO "authenticated";
GRANT ALL ON TABLE "public"."admin_users" TO "service_role";



GRANT ALL ON TABLE "public"."app_settings" TO "anon";
GRANT ALL ON TABLE "public"."app_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."app_settings" TO "service_role";



GRANT ALL ON TABLE "public"."game_sessions" TO "anon";
GRANT ALL ON TABLE "public"."game_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."game_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."player_signups" TO "anon";
GRANT ALL ON TABLE "public"."player_signups" TO "authenticated";
GRANT ALL ON TABLE "public"."player_signups" TO "service_role";



GRANT ALL ON TABLE "public"."players" TO "anon";
GRANT ALL ON TABLE "public"."players" TO "authenticated";
GRANT ALL ON TABLE "public"."players" TO "service_role";



GRANT ALL ON TABLE "public"."team_players" TO "anon";
GRANT ALL ON TABLE "public"."team_players" TO "authenticated";
GRANT ALL ON TABLE "public"."team_players" TO "service_role";



GRANT ALL ON TABLE "public"."teams" TO "anon";
GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT ALL ON TABLE "public"."teams" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
