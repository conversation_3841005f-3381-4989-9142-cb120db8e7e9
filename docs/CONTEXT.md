# Inter NRW Football App - System Documentation

## 1. Core Modules & Functionalities

### 1.1 Game Scheduling (Admin Panel)

- Admins can create upcoming games by selecting date, time, and location
- Scheduled games appear prominently on the start page with countdown timers
- Supports recurring scheduling patterns with automatic next-game creation

### 1.2 Player Sign-Up System

- Players can sign up or withdraw from scheduled matches with one-click actions
- Real-time signup counts are displayed with dynamic UI updates via Supabase realtime subscriptions
- Automatic sorting of players into confirmed and reserve lists based on signup time

### 1.3 Team Generation & Management

- Once signups are closed, admin can generate balanced teams based on player skills and positions
- Supports 2, 3, or 4 teams configuration based on player count and admin settings
- Team names are auto-generated (A, B, C, etc.) but fully editable
- Teams preview screen allows admin to:
  - Edit team names
  - Swap players between teams
  - Balance teams by rating
- Final teams become visible to players only after admin approval

### 1.4 Game Lifecycle Management (Admin Panel)

- Admin actions include:
  - Open/close signups
  - Generate teams
  - Cancel scheduled games
  - Mark games as played
  - Archive past game sessions
- Real-time status updates reflected immediately across all connected clients

### 1.5 Match History

- "History" tab shows previously played matches with complete details
- Displays:
  - Date, time, and duration
  - Teams composition with player names
  - Top 5 most active players statistics
  - MVP results when voting has concluded
- Filtering and search capabilities for finding specific matches

### 1.6 App Authentication

- Admin access protected via a shared PIN/password system
- Login sessions persisted in local storage for convenient access
- Simple authentication model designed for team-based usage

### 1.7 Settings Management

- Available via a dedicated settings page in the admin panel
- Stores app-wide settings including:
  - Team generation rules
  - Game duration defaults
  - Visual theme preferences

## 2. Rule-Based Configurations

### 2.1 Team Configuration Rules

- Admin can enable/disable 3-team mode via settings
- When disabled, team generator automatically chooses between 2 or 4 teams
- Intelligent player distribution based on:
  - Total player count
  - Special positions (goalkeeper, striker)
  - Player skill ratings

### 2.2 Game Duration Logic

- Automatic game duration calculation based on player count:
  - 2 teams (10 players): 90 minutes by default
  - 3 teams (15 players): 120 minutes by default
  - 4 teams (20 players): 120 minutes by default
- All durations customizable in settings
- Duration saved in database and displayed on game information page

## 3. Optional Feature: MVP Voting

### 3.1 Voting Trigger & Flow

- When a game is marked as played, MVP voting session starts automatically
- UI notification banner appears: "Vote now for the MVP of the last match!"
- One vote per player per game using UUID tracking
- Real-time vote counting with leaderboard

### 3.2 Admin Voting Controls

- Admin can manually close voting session when appropriate
- Once closed, votes are tallied and results finalized
- No changes allowed after voting period is closed

### 3.3 MVP History & Display

- Dedicated "MVP History" view shows:
  - Match date and details
  - MVP winner with highlight
  - Vote count and distribution
- The Match History view also displays MVP winners with trophy icons
- Direct navigation between MVP history and match details
