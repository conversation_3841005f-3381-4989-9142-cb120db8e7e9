# Tournament Mode

## Overview

The Tournament Mode is a feature that allows administrators to organize and manage football tournaments within a game session. It provides functionality for planning matches, tracking results, and displaying standings in a tournament format.

## Key Features

- Tournament configuration with customizable parameters
- Automatic match scheduling
- Real-time match timers
- Result tracking and standings calculation
- Synchronization with game session match results

## Components and Architecture

### Core Components

1. **Tournament Configuration**

   - Configures tournament parameters (match duration, breaks, fields)
   - Creates initial tournament structure

2. **Tournament Schedule**

   - Displays and manages the match schedule
   - Controls for starting/ending matches
   - Result entry

3. **Tournament Standings**

   - Displays current tournament standings
   - Updates in real-time as results are entered

4. **Match Timer**

   - Controls match timing
   - Visual and audio indicators for match start/end

5. **Results Entry**
   - Single and multiple match result entry
   - Validation of entered results

### Data Model

#### Tournament

```typescript
interface Tournament {
  id: string;
  gameSessionId: string;
  config: TournamentConfig;
  matches: TournamentMatch[];
  standings: TournamentStanding[];
  currentMatchIndex: number;
  isActive: boolean;
  startTime: Date;
  endTime?: Date;
}
```

#### Tournament Configuration

```typescript
interface TournamentConfig {
  totalDurationMinutes: number;
  matchDurationMinutes: number;
  breakBetweenMatchesMinutes: number;
  numberOfTeams: number;
  numberOfFields: number;
}
```

#### Tournament Match

```typescript
interface TournamentMatch {
  id: string;
  teamA: Team;
  teamB: Team;
  goalsA: number | null;
  goalsB: number | null;
  isCompleted: boolean;
  matchNumber: number;
  field: string;
  timeSlot: number;
  startTime?: Date;
  endTime?: Date;
}
```

#### Tournament Standing

```typescript
interface TournamentStanding {
  team: Team;
  played: number;
  won: number;
  drawn: number;
  lost: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
}
```

## User Flows

### Tournament Creation Flow

1. Admin navigates to the tournament mode in the admin panel
2. Admin configures tournament parameters:
   - Match duration (in minutes)
   - Break between matches (in minutes)
   - Number of fields
3. System calculates the optimal number of matches based on:
   - Total available time (from game session)
   - Number of teams
   - Match duration and breaks
4. System generates a round-robin schedule where each team plays against every other team equally
5. Admin reviews and confirms the tournament creation
6. System creates the tournament and displays the schedule

### Match Management Flow

1. Admin views the tournament schedule
2. Admin can:
   - Start a single match
   - Start multiple matches simultaneously (on different fields)
   - Enter results for completed matches
   - Add additional matches if time permits
3. When starting a match:
   - Timer is initialized with the configured duration
   - Match status is updated to "in progress"
   - Visual indicators show active matches
4. When a match ends:
   - Admin enters the final score
   - System updates the match status to "completed"
   - Standings are automatically recalculated
   - Results are synchronized with the game session match results

### Tournament Completion Flow

1. Admin can end the tournament at any time
2. When ending a tournament:
   - All active matches are completed
   - Final standings are calculated
   - Tournament status is set to inactive
   - No more matches can be added or started

## Technical Implementation

### State Management

The tournament state is managed through the `useTournament` hook, which provides:

- Tournament data and loading states
- Functions for creating and updating tournaments
- Functions for managing matches
- Functions for calculating standings

### Match Scheduling Algorithm

The match scheduling algorithm follows these principles:

1. Each team should play against every other team equally
2. Matches are distributed across available fields
3. No team should play in consecutive time slots
4. No team should play on multiple fields simultaneously

### Standings Calculation

Standings are calculated using the following rules:

- Win: 3 points
- Draw: 1 point
- Loss: 0 points
- Teams are sorted by:
  1. Points (descending)
  2. Goal difference (descending)
  3. Goals scored (descending)
  4. Head-to-head results
  5. Alphabetically by team name

### Synchronization with Match Results

Tournament results are synchronized with the game session match results to ensure consistency across the application. This is handled by:

1. `syncTournamentResultsToMatchResults`: Updates match results in the database
2. `importMatchResultsToTournament`: Imports match results from the database to the tournament

## UI/UX Design Principles

### Responsive Design

- **Desktop Layout**: Side-by-side display of tournament components for efficient use of screen space

  - Tournament configuration and standings displayed in the sidebar
  - Match schedule and active matches displayed in the main content area
  - Multiple active matches displayed in a grid layout

- **Mobile Layout**: Stacked components with optimized controls for touch interaction

  - Tab-based navigation between tournament sections
  - Icon-only buttons to save space
  - Full-width buttons for better touch targets
  - Collapsible sections for less important information

- **Adaptive Tables**: Responsive table layouts that adapt to different screen sizes
  - Horizontal scrolling for tables on small screens
  - Hidden less important columns on mobile
  - Consistent column alignment regardless of content

### Visual Design System

- **Color Scheme**: Blue as the primary accent color with a clean, modern aesthetic

  - Blue gradient progress indicators
  - Pastel team colors for better readability
  - High contrast between text and background

- **Team Representation**: Consistent team colors across all views

  - Unique gradient colors for each team
  - Team colors derived from the TeamDisplay component
  - No duplicate colors between teams

- **Status Indicators**:

  - Active matches highlighted with a pulsing border
  - Completed matches show final scores with a subtle background
  - Timer rings change color gradually (blue to red) as time runs out
  - Trophy icon for the first-place team in standings

- **Typography**:
  - Clear hierarchy with different font weights
  - Consistent text alignment
  - Adequate spacing between text elements

### Component Design

- **Cards**: Consistent card design across all components

  - Clear headers with titles and descriptions
  - Appropriate spacing between card elements
  - Subtle separators between card sections

- **Buttons**: Contextual button styling

  - Primary actions use filled buttons
  - Secondary actions use outline buttons
  - Destructive actions use red buttons
  - Icon-only buttons have tooltips

- **Tabs**: Oval-shaped tabs with clear active state

  - Higher contrast for selected tabs
  - Consistent spacing between tabs
  - Responsive tab layout on smaller screens

- **Timers**: Circular countdown timers with visual feedback
  - Percentage-based progress indicators
  - Color changes as time runs out
  - Clear display of remaining time
  - Intuitive control buttons

### Interaction Design

- **Feedback**: Immediate visual feedback for all user actions

  - Toast notifications for successful/failed operations
  - Loading indicators for asynchronous operations
  - Confirmation dialogs for destructive actions

- **Error Handling**: User-friendly error messages

  - Clear explanation of what went wrong
  - Suggestions for how to fix the issue
  - Option to retry failed operations

- **Navigation**: Intuitive navigation between tournament sections
  - Breadcrumb navigation for complex flows
  - Back buttons for multi-step processes
  - Consistent placement of navigation elements

### Accessibility

- **Color Contrast**: High contrast between text and background

  - WCAG 2.1 AA compliant color contrast ratios
  - Alternative visual indicators beyond color

- **Keyboard Navigation**: Full keyboard support

  - Logical tab order
  - Focus indicators
  - Keyboard shortcuts for common actions

- **Screen Readers**: Screen reader compatible components

  - Semantic HTML structure
  - ARIA labels for interactive elements
  - Meaningful alt text for images

- **Responsive Text**: Text that scales with viewport size
  - Minimum font sizes for readability
  - Proper heading hierarchy
  - Adequate line spacing

## Error Handling

- Validation of all user inputs
- Graceful handling of database errors
- Automatic retry for failed synchronization
- User-friendly error messages

## Future Enhancements

- Support for different tournament formats (knockout, groups)
- Advanced scheduling options
- Player statistics tracking
- Tournament history and analytics
- Export tournament results to PDF/CSV
- Integration with external scoring systems

## Testing

The tournament mode is thoroughly tested with:

- Unit tests for individual components
- Integration tests for component interactions
- End-to-end tests for complete user flows
- Performance tests for large tournaments

## Implementation Details

### Component Structure

```
src/components/admin/tournament/
├── CompactMatchTimer.tsx       # Timer component for tournament matches
├── MultiResultsEntry.tsx       # Dialog for entering multiple match results
├── ResultsEntry.tsx            # Dialog for entering a single match result
├── TournamentConfig.tsx        # Tournament configuration form
├── TournamentMode.tsx          # Main tournament mode container
├── TournamentSchedule.tsx      # Match schedule display and controls
└── TournamentStandings.tsx     # Tournament standings table
```

### Hooks and Services

```
src/hooks/
└── useMatchTimer.ts            # Hook for managing match timers
└── useTournament.ts            # Hook for tournament state management

src/services/
└── tournamentDbService.ts      # Database operations for tournaments
└── tournamentResultsService.ts # Synchronization with match results
```

### Key Functions

#### Tournament Creation

```typescript
// Creates a new tournament with the given configuration
const createTournament = async (config: TournamentConfig): Promise<Tournament | null> => {
  try {
    // Generate matches based on teams and configuration
    const matches = generateMatches(teams, config);

    // Initialize standings for all teams
    const standings = teams.map((team) => ({
      team,
      played: 0,
      won: 0,
      drawn: 0,
      lost: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      goalDifference: 0,
      points: 0,
    }));

    // Create tournament object
    const tournament: Tournament = {
      id: uuidv4(),
      gameSessionId,
      config,
      matches,
      standings,
      currentMatchIndex: 0,
      isActive: true,
      startTime: new Date(),
    };

    // Save tournament to database
    const savedTournament = await saveTournament(tournament);

    return savedTournament;
  } catch (error) {
    console.error("Error creating tournament:", error);
    return null;
  }
};
```

#### Match Result Update

```typescript
// Updates a match result and recalculates standings
const updateMatchResult = async (
  matchId: string,
  goalsA: number,
  goalsB: number
): Promise<boolean> => {
  try {
    // Update match in database
    const success = await updateTournamentMatchResult(matchId, goalsA, goalsB);

    if (success && tournament) {
      // Update local state
      const updatedMatches = tournament.matches.map((match) => {
        if (match.id === matchId) {
          return {
            ...match,
            goalsA,
            goalsB,
            isCompleted: true,
            endTime: match.endTime || new Date(),
          };
        }
        return match;
      });

      // Recalculate standings
      const updatedStandings = calculateStandings(updatedMatches, teams);

      // Update tournament state
      setTournament({
        ...tournament,
        matches: updatedMatches,
        standings: updatedStandings,
      });

      // Sync with match results
      await syncTournamentResultsToMatchResults({
        ...tournament,
        matches: updatedMatches,
        standings: updatedStandings,
      });
    }

    return success;
  } catch (error) {
    console.error("Error updating match result:", error);
    return false;
  }
};
```

## Deployment Considerations

### Database Requirements

The tournament mode requires the following database tables:

- `tournaments`: Stores tournament data
- `tournament_matches`: Stores match data
- `match_results`: Stores match results (shared with game session)

### Performance Optimization

- Lazy loading of tournament components
- Efficient database queries with proper indexing
- Caching of tournament data to reduce database load
- Batch updates for multiple match results

### Security Considerations

- Only administrators can create and manage tournaments
- Input validation to prevent SQL injection
- Rate limiting to prevent abuse
- Audit logging of all tournament actions

## Conclusion

The Tournament Mode provides a comprehensive solution for managing football tournaments within the application. It streamlines the process of scheduling matches, tracking results, and calculating standings, while ensuring a seamless user experience for administrators.

The modular architecture allows for easy maintenance and future enhancements, while the robust testing ensures reliability and stability. The synchronization with game session match results ensures data consistency across the application.
